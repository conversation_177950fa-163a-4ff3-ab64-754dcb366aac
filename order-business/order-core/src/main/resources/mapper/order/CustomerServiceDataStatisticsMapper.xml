<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.CustomerServiceDataStatisticsMapper">

    <select id="selectChineseCustomerServiceData"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.ChineseCustomerServiceDataVO">
        SELECT
            ov.contact_id AS customerServiceId,
            COUNT( DISTINCT CASE WHEN ov.STATUS IN (
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode}
                                          ) THEN ov.id END ) AS orderCount,
            COUNT( DISTINCT CASE WHEN ov.STATUS IN (
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                           ${@<EMAIL>}
                                          ) THEN ov.id END ) AS orderTotalCount,
            COUNT( DISTINCT CASE WHEN ( (ovt.id IS NOT NULL AND ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode}) OR orf.id IS NOT NULL OR ov.rollback_id IS NOT NULL )
                                 AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} THEN ov.id END ) AS afterSaleCount,
            ROUND( IFNULL( COUNT( DISTINCT CASE WHEN ( (ovt.id IS NOT NULL AND ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode}) OR orf.id IS NOT NULL OR ov.rollback_id IS NOT NULL )
                                            AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} THEN ov.id END )
                               / NULLIF(COUNT(DISTINCT CASE
                                        WHEN ovf.target_status IN (
                                                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                                ${@<EMAIL>}
                                                                  )
                                        AND ov.status != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} THEN ov.id END), 0), 0 ), 4
        ) AS afterSaleRate,
            COUNT( DISTINCT CASE WHEN ( (ovt.id IS NOT NULL AND ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode}) OR orf.id IS NOT NULL OR ov.rollback_id IS NOT NULL )
                                 AND ov.`status` IN (
                                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_PAY.getcode},
                                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CHECK.getcode},
                                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode},
                                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode},
                                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                ${@<EMAIL>}
                                                )
                <if test="beginTime != null and endTime != null">
                    AND ov.create_time BETWEEN DATE_FORMAT(#{beginTime}, '%Y-%m-%d 00:00:00') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d 23:59:59')
                </if> THEN ov.id END ) AS rightAfterSaleCount
        FROM
            order_video ov
                LEFT JOIN order_video_task ovt ON ovt.video_id = ov.id
                LEFT JOIN order_video_refund orf ON orf.video_id = ov.id
                LEFT JOIN order_video_flow ovf ON ovf.video_id = ov.id
        WHERE
            ov.contact_id IS NOT NULL
        GROUP BY
            ov.contact_id
    </select>
    <select id="getCustomerServiceBaseBoardByDate"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.CustomerServiceBaseBoardVO">
        SELECT
            COUNT(DISTINCT CASE WHEN ov.STATUS IN (
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode}
                ) AND ov.rollback_id IS NULL THEN ov.id END ) AS serviceOrderCount,
            COUNT(DISTINCT CASE WHEN DATE ( ovf.event_execute_time )= #{date} AND ov.STATUS != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} AND ov.rollback_id IS NULL THEN ov.id END ) AS orderNewCount,
            COUNT(DISTINCT CASE WHEN ov.STATUS = ${@<EMAIL>} AND DATE ( ov.status_time )= #{date} AND ov.rollback_id IS NULL THEN ov.id END ) AS orderFinishCount,
            COUNT(DISTINCT CASE WHEN DATE ( ovm.submit_time )= #{date} AND ovm.submit_time = ov.last_model_submit_time AND ov.rollback_id IS NULL THEN ov.id END ) AS orderScheduledCount,
            COUNT(DISTINCT CASE WHEN DATE ( ovm.submit_time )= #{date} AND ovm.shoot_model_type = ${@com.ruoyi.common.core.enums.ModelTypeEnum@AVERAGE_PEOPLE.getcode} AND ovm.submit_time = ov.last_model_submit_time AND ov.rollback_id IS NULL THEN ov.id END ) AS orderScheduledSoleCount,
            COUNT(DISTINCT CASE WHEN DATE ( ovm.submit_time )= #{date} AND ovm.shoot_model_type = ${@<EMAIL>} AND ovm.submit_time = ov.last_model_submit_time AND ov.rollback_id IS NULL THEN ov.id END ) AS orderScheduledInfluencerCount,
            COUNT(DISTINCT CASE WHEN DATE ( ovmpm.oust_time )= #{date} AND ovmpm.oust_type = ${@com.ruoyi.common.core.enums.PreselectModelOustTypeEnum@MERCHANT_REJECTION.getcode} THEN ovm.id END ) AS modelRejectCount,
            COUNT(DISTINCT CASE WHEN DATE ( ovmpm.oust_time )= #{date} AND ovmpm.oust_type = ${@com.ruoyi.common.core.enums.PreselectModelOustTypeEnum@MERCHANT_REJECTION.getcode} AND ovm.shoot_model_type = ${@com.ruoyi.common.core.enums.ModelTypeEnum@AVERAGE_PEOPLE.getcode} THEN ovm.id END ) AS modelRejectSoleCount,
            COUNT(DISTINCT CASE WHEN DATE ( ovmpm.oust_time )= #{date} AND ovmpm.oust_type = ${@com.ruoyi.common.core.enums.PreselectModelOustTypeEnum@MERCHANT_REJECTION.getcode} AND ovm.shoot_model_type = ${@<EMAIL>} THEN ovm.id END ) AS modelRejectInfluencerCount
        FROM
            order_video ov
                LEFT JOIN order_video_flow ovf ON ovf.video_id = ov.id AND ovf.target_status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode}
                LEFT JOIN order_video_match ovm ON ovm.video_id = ov.id
                LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
    </select>
    <select id="selectEnglishCustomerServiceData"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO">
        SELECT
            ov.issue_id AS customerServiceId,
            COUNT( DISTINCT CASE WHEN ov.STATUS IN (
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode}
                                          ) THEN ov.id END ) AS orderCount,
            COUNT( DISTINCT CASE WHEN ov.STATUS IN (
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                            ${@<EMAIL>}
                                          ) THEN ov.id END ) AS orderTotalCount,
            COUNT( DISTINCT CASE WHEN ov.STATUS IN (
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                            ${@<EMAIL>}
                                          )
                            AND (
                                ( ovfb.earliest_feedback_time IS NULL AND TIMESTAMPDIFF( DAY, ovl.receipt_time, NOW() ) >= 20 )
                                OR ( ovl.receipt_time &lt; ovfb.earliest_feedback_time AND TIMESTAMPDIFF( DAY, ovl.receipt_time, ovfb.earliest_feedback_time ) &gt;= 20 )
                            )
                            AND ov.rollback_id IS NULL THEN ov.id END ) AS dragOrderCount,
            ROUND(
                IFNULL(
                    COUNT( DISTINCT CASE WHEN ov.STATUS IN (
                                                    ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                    ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                    ${@<EMAIL>}
                                                  )
                                    AND (
                                        ( ovfb.earliest_feedback_time IS NULL AND TIMESTAMPDIFF( DAY, ovl.receipt_time, NOW() ) >= 20 )
                                        OR ( ovl.receipt_time &lt; ovfb.earliest_feedback_time AND TIMESTAMPDIFF( DAY, ovl.receipt_time, ovfb.earliest_feedback_time ) &gt;= 20 )
                                    )
                                    AND ov.rollback_id IS NULL THEN ov.id END )
                        / COUNT( DISTINCT CASE WHEN ov.STATUS IN (
                                                        ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                        ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                        ${@<EMAIL>}
                                                        ) THEN ov.id END ), 0 ), 4 ) AS dragOrderRate,
            COUNT( DISTINCT CASE WHEN ( (ovt.id IS NOT NULL AND ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode}) OR orf.id IS NOT NULL OR ov.rollback_id IS NOT NULL )
                            AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} THEN ov.id END ) AS afterSaleCount,
            ROUND(
                IFNULL(
                    COUNT( DISTINCT CASE WHEN ( (ovt.id IS NOT NULL AND ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode}) OR orf.id IS NOT NULL OR ov.rollback_id IS NOT NULL )
                                    AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} THEN ov.id END )
                        / NULLIF(COUNT(DISTINCT CASE
                                WHEN ovf.target_status IN (
                                                        ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                        ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                        ${@<EMAIL>}
                                                          )
                                AND ov.status != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} THEN ov.id END), 0), 0 ), 4
                                ) AS afterSaleRate,
            COUNT( DISTINCT CASE WHEN ov.STATUS IN (
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                            ${@<EMAIL>}
                                            )
                            AND (
                                ( ovfb.earliest_feedback_time IS NULL AND TIMESTAMPDIFF( DAY, ovl.receipt_time, NOW() ) >= 20 )
                                OR ( ovl.receipt_time &lt; ovfb.earliest_feedback_time AND TIMESTAMPDIFF( DAY, ovl.receipt_time, ovfb.earliest_feedback_time ) &gt;= 20 )
                            )
                            AND ov.rollback_id IS NULL
                            <if test="beginTime != null and endTime != null">
                                AND ov.create_time BETWEEN DATE_FORMAT( #{beginTime}, '%Y-%m-%d 00:00:00' ) AND DATE_FORMAT( #{endTime}, '%Y-%m-%d 23:59:59' )
                            </if> THEN ov.id END ) AS rightDragOrderCount,
            COUNT( DISTINCT CASE WHEN ( (ovt.id IS NOT NULL AND ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode}) OR orf.id IS NOT NULL OR ov.rollback_id IS NOT NULL )
                        AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}
                            <if test="beginTime != null and endTime != null">
                                AND ov.create_time BETWEEN DATE_FORMAT( #{beginTime}, '%Y-%m-%d 00:00:00' ) AND DATE_FORMAT( #{endTime}, '%Y-%m-%d 23:59:59' )
                            </if> THEN ov.id END ) AS rightAfterSaleCount
        FROM
            order_video ov
                LEFT JOIN order_video_task ovt ON ovt.video_id = ov.id
                LEFT JOIN order_video_refund orf ON orf.video_id = ov.id
                LEFT JOIN order_video_flow ovf ON ovf.video_id = ov.id
                LEFT JOIN (
                            SELECT video_id, receipt_time
                            FROM (
                                SELECT
                                    video_id,
                                    receipt_time,
                                    ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY shipping_time DESC) AS rn
                                FROM order_video_logistic
                            ) t
                            WHERE t.rn = 1
                            ) ovl ON ov.id = ovl.video_id
                LEFT JOIN ( SELECT video_id, MIN( create_time ) AS earliest_feedback_time FROM order_video_feed_back GROUP BY video_id ) ovfb ON ov.id = ovfb.video_id
        WHERE
            ov.issue_id IS NOT NULL
        GROUP BY
            ov.issue_id
    </select>
    <select id="selectChineseCustomerServiceTaskData"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.ChineseCustomerServiceDataVO">
        SELECT
            user_id AS customerServiceId,
            COUNT(DISTINCT id) AS taskCount
            <if test="beginTime != null and endTime != null">
                ,
                COUNT( DISTINCT CASE WHEN submit_time BETWEEN DATE_FORMAT(#{beginTime}, '%Y-%m-%d 00:00:00') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d 23:59:59') THEN id END) AS taskNewCountInTime,
                COUNT( DISTINCT CASE WHEN end_time BETWEEN DATE_FORMAT(#{beginTime}, '%Y-%m-%d 00:00:00') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d 23:59:59') AND `status` = ${@<EMAIL>} THEN id END) AS taskFinishCountInTime
            </if>
        FROM (
                 SELECT
                     ovtd.id,
                     ovtd.submit_by_id AS user_id,
                     ovtd.submit_time,
                     ovtd.end_time,
                     ovtd.status
                 FROM order_video_task_detail ovtd
                          JOIN order_video_task ovt ON ovt.id = ovtd.task_id
                          JOIN order_video ov ON ov.id = ovt.video_id
                 WHERE ovtd.status NOT IN (
                                           ${@<EMAIL>},
                                           ${@<EMAIL>}
                     )
                   AND ov.rollback_id IS NULL

                 UNION ALL

                 SELECT
                     ovtd.id,
                     ovtd.assignee_id AS user_id,
                     ovtd.submit_time,
                     ovtd.end_time,
                     ovtd.status
                 FROM order_video_task_detail ovtd
                          JOIN order_video_task ovt ON ovt.id = ovtd.task_id
                          JOIN order_video ov ON ov.id = ovt.video_id
                 WHERE ovtd.status NOT IN (
                                           ${@<EMAIL>},
                                           ${@<EMAIL>}
                     )
                   AND ov.rollback_id IS NULL
             ) AS ovtd
        WHERE user_id IS NOT NULL
        GROUP BY user_id
    </select>
    <select id="selectEnglishCustomerServiceMatchData"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO">
        SELECT
            ovm.shoot_model_person_id AS customerServiceId,
            COUNT( DISTINCT CASE WHEN 1=1
                <if test="beginTime != null and endTime != null">
                    AND ovm.submit_time BETWEEN DATE_FORMAT( #{beginTime}, '%Y-%m-%d 00:00:00' ) AND DATE_FORMAT( #{endTime}, '%Y-%m-%d 23:59:59' )
                </if> THEN ovm.id END ) AS orderScheduledCount,
            COUNT( DISTINCT CASE WHEN ovm.shoot_model_type = ${@com.ruoyi.common.core.enums.ModelTypeEnum@AVERAGE_PEOPLE.getcode}
                <if test="beginTime != null and endTime != null">
                    AND ovm.submit_time BETWEEN DATE_FORMAT( #{beginTime}, '%Y-%m-%d 00:00:00' ) AND DATE_FORMAT( #{endTime}, '%Y-%m-%d 23:59:59' )
                </if> THEN ovm.id END ) AS orderScheduledSoleCount,
            COUNT( DISTINCT CASE WHEN ovm.shoot_model_type = ${@<EMAIL>}
                <if test="beginTime != null and endTime != null">
                    AND ovm.submit_time BETWEEN DATE_FORMAT( #{beginTime}, '%Y-%m-%d 00:00:00' ) AND DATE_FORMAT( #{endTime}, '%Y-%m-%d 23:59:59' )
                </if> THEN ovm.id END ) AS orderScheduledInfluencerCount,
            COUNT( DISTINCT CASE WHEN ovmpm.oust_type = ${@com.ruoyi.common.core.enums.PreselectModelOustTypeEnum@MERCHANT_REJECTION.getcode}
                <if test="beginTime != null and endTime != null">
                    AND ovmpm.oust_time BETWEEN DATE_FORMAT( #{beginTime}, '%Y-%m-%d 00:00:00' ) AND DATE_FORMAT( #{endTime}, '%Y-%m-%d 23:59:59' )
                </if> THEN ovm.id END ) AS rejectOrderCount,
            ROUND(
                IFNULL(
                COUNT( DISTINCT CASE WHEN ovm.shoot_model_cooperation = ${@<EMAIL>}
                <if test="beginTime != null and endTime != null">
                    AND ovm.submit_time BETWEEN DATE_FORMAT( #{beginTime}, '%Y-%m-%d 00:00:00' ) AND DATE_FORMAT( #{endTime}, '%Y-%m-%d 23:59:59' )
                </if> THEN ovm.id END )
                / COUNT( DISTINCT CASE WHEN 1=1
                <if test="beginTime != null and endTime != null">
                    AND ovm.submit_time BETWEEN DATE_FORMAT( #{beginTime}, '%Y-%m-%d 00:00:00' ) AND DATE_FORMAT( #{endTime}, '%Y-%m-%d 23:59:59' )
                </if> THEN ovm.id END ), 0 ), 4 ) AS proportionOfQualityModel,
            ROUND(
                IFNULL(
                COUNT( DISTINCT CASE WHEN ovm.shoot_model_cooperation = ${@<EMAIL>}
                <if test="beginTime != null and endTime != null">
                    AND ovm.submit_time BETWEEN DATE_FORMAT( #{beginTime}, '%Y-%m-%d 00:00:00' ) AND DATE_FORMAT( #{endTime}, '%Y-%m-%d 23:59:59' )
                </if> THEN ovm.id END )
                / COUNT( DISTINCT CASE WHEN 1=1
                <if test="beginTime != null and endTime != null">
                    AND ovm.submit_time BETWEEN DATE_FORMAT( #{beginTime}, '%Y-%m-%d 00:00:00' ) AND DATE_FORMAT( #{endTime}, '%Y-%m-%d 23:59:59' )
                </if> THEN ovm.id END ), 0 ), 4 ) AS proportionOfGeneralModel
        FROM
            order_video_match ovm
            LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
        WHERE ovm.submit_time IS NOT NULL AND ovm.shoot_model_person_id IS NOT NULL
        GROUP BY ovm.shoot_model_person_id
    </select>
</mapper>
