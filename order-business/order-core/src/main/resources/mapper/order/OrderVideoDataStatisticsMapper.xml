<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoDataStatisticsMapper">

    <select id="getOrderVideoBaseBoard"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoBaseBoardVO">
        SELECT
            COUNT(DISTINCT CASE WHEN ov.un_confirm_time IS NOT NULL AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode} THEN ov.id END ) AS orderCount,
            COUNT(DISTINCT CASE WHEN ovt.id IS NOT NULL AND ovtd.`status` NOT IN ( ${@<EMAIL>},${@<EMAIL>} ) THEN ov.id END ) AS taskCount
        FROM
            order_video ov
                LEFT JOIN order_video_task ovt ON ovt.video_id = ov.id
                LEFT JOIN order_video_task_detail ovtd ON ovtd.task_id = ovt.id
    </select>
    <select id="getOrderVideoServiceCount"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoServiceCountVO">
        SELECT
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getCode} THEN ov.id END ) AS waitConfirmOrderCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getCode} AND ov.is_care = ${@<EMAIL>} THEN ov.id END ) AS waitConfirmOrderCareCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode} THEN ov.id END ) AS waitMatchOrderCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode} AND ov.is_care = ${@<EMAIL>} THEN ov.id END ) AS waitMatchOrderCareCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode} AND ov.status_time &lt;= NOW() - INTERVAL 7 DAY THEN ov.id END ) AS overSevenDaysNoMatchOrderCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode} AND ov.status_time &lt;= NOW() - INTERVAL 7 DAY AND ov.is_care = ${@<EMAIL>} THEN ov.id END ) AS overSevenDaysNoMatchOrderCareCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getCode} THEN ov.id END ) AS waitDeliveryOrderCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getCode} AND ov.is_care = ${@<EMAIL>} THEN ov.id END ) AS waitDeliveryOrderCareCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getCode} AND ov.status_time &lt;= NOW() - INTERVAL 7 DAY THEN ov.id END ) AS overSevenDaysNoDeliveryOrderCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getCode} AND ov.status_time &lt;= NOW() - INTERVAL 7 DAY AND ov.is_care = ${@<EMAIL>} THEN ov.id END ) AS overSevenDaysNoDeliveryOrderCareCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getCode} THEN ov.id END ) AS waitFinishOrderCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getCode} AND ov.is_care = ${@<EMAIL>} THEN ov.id END ) AS waitFinishOrderCareCount,
            COUNT(DISTINCT CASE WHEN ( ovfb.earliest_feedback_time IS NULL AND TIMESTAMPDIFF( DAY, ovl.receipt_time, NOW() ) &gt;= 20 ) THEN ov.id END ) AS signOverTwentyDaysNoFeedbackOrderCount,
            COUNT(DISTINCT CASE WHEN ( ovfb.earliest_feedback_time IS NULL AND TIMESTAMPDIFF( DAY, ovl.receipt_time, NOW() ) &gt;= 20 ) AND ov.is_care = ${@<EMAIL>} THEN ov.id END ) AS signOverTwentyDaysNoFeedbackOrderCareCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getCode} THEN ov.id END ) AS needConfirmOrderCount,
            COUNT(DISTINCT CASE WHEN ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getCode} AND ov.is_care = ${@<EMAIL>} THEN ov.id END ) AS needConfirmOrderCareCount
        FROM
            order_video ov
            LEFT JOIN (
                SELECT
                    video_id, receipt_time
                FROM (
                    SELECT
                        video_id,
                        receipt_time,
                        ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY shipping_time DESC) AS rn
                    FROM order_video_logistic
                ) t
                WHERE t.rn = 1
            ) ovl ON ov.id = ovl.video_id
            LEFT JOIN ( SELECT video_id, MIN( create_time ) AS earliest_feedback_time FROM order_video_feed_back GROUP BY video_id ) ovfb ON ov.id = ovfb.video_id
        WHERE
            ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode}
    </select>
    <select id="getOrderNewCountByTimeBetween" resultType="com.ruoyi.system.api.domain.bo.order.datastatistics.OrderVideoTrendBO">
        SELECT
            DATE_FORMAT( ovf.event_execute_time, ${dateFormat} ) AS date,
            COUNT( DISTINCT ov.id ) AS count
        FROM
            order_video ov
                LEFT JOIN order_video_flow ovf ON ovf.video_id = ov.id AND ovf.target_status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode}
        WHERE
            ovf.event_execute_time IS NOT NULL
            AND ( ov.STATUS != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode} OR (ov.STATUS = ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode} AND DATE ( ovf.event_execute_time ) != DATE ( ov.status_time )))
            AND ovf.event_execute_time BETWEEN #{beginTime} AND #{endTime}
            AND ovf.event_execute_time &lt; CURDATE()
        GROUP BY
            DATE_FORMAT( ovf.event_execute_time, ${dateFormat} )
    </select>
    <select id="getOrderCancelCountByTimeBetween" resultType="com.ruoyi.system.api.domain.bo.order.datastatistics.OrderVideoTrendBO">
        SELECT
            DATE_FORMAT( ov.status_time, ${dateFormat} ) AS date,
            COUNT( DISTINCT ov.id ) AS count
        FROM
            order_video ov
        WHERE
            ov.un_confirm_time IS NOT NULL
            AND ov.STATUS = ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode}
            AND ov.status_time BETWEEN #{beginTime} AND #{endTime}
            AND ov.status_time &lt; CURDATE()
        GROUP BY
            DATE_FORMAT( ov.status_time, ${dateFormat} )
    </select>
    <select id="getOrderScheduledCountByTimeBetween" resultType="com.ruoyi.system.api.domain.bo.order.datastatistics.OrderVideoTrendBO">
        SELECT
            DATE_FORMAT(main.submit_time, ${dateFormat}) AS DATE,
            COUNT(DISTINCT main.video_id) AS count
        FROM (
                 SELECT
                     ovm.id,
                     ovm.video_id,
                     ovm.submit_time
                 FROM order_video_match ovm
                          JOIN order_video ov ON ov.id = ovm.video_id
                 WHERE
                     ov.un_confirm_time IS NOT NULL
                     AND ovm.submit_time BETWEEN #{beginTime} AND #{endTime}
                     AND ovm.submit_time &lt; CURDATE()
        ) AS main
                 JOIN (
                    SELECT
                        ovmpm.match_id,
                        MAX(ovm.submit_time) AS latest_submit_time
                    FROM order_video_match_preselect_model ovmpm
                             JOIN order_video_match ovm ON ovmpm.match_id = ovm.id
                    WHERE ovmpm.status = ${@<EMAIL>}
                    GROUP BY ovmpm.match_id
            ) AS filtered ON main.id = filtered.match_id AND main.submit_time = filtered.latest_submit_time
        GROUP BY
            DATE_FORMAT(main.submit_time, ${dateFormat})
    </select>
    <select id="selectOrderVideoMatchDurationDataList"
            resultType="com.ruoyi.system.api.domain.bo.order.datastatistics.OrderVideoDurationDataBO">
        WITH sessions AS (
            SELECT
                video_id,
                submit_time,
                LAG( submit_time ) OVER ( PARTITION BY video_id ORDER BY submit_time ) AS prev_submit_time
            FROM
                order_video_match
            WHERE
                submit_time IS NOT NULL
        ),
        session_starts AS (
            SELECT
                s.video_id,
                s.submit_time,
                MIN( t.start_time ) AS session_start
            FROM
                sessions AS s
                    JOIN order_video_match AS t ON t.video_id = s.video_id
                        AND t.start_time &lt;= s.submit_time AND ( s.prev_submit_time IS NULL OR t.start_time > s.prev_submit_time
            )
            GROUP BY
                s.video_id,
                s.submit_time
        )
        SELECT
            ss.video_id,
            ROUND( SUM( TIMESTAMPDIFF( SECOND, ss.session_start, ss.submit_time ) ) / 86400.0, 4 ) AS duration
        FROM
            session_starts ss
                JOIN order_video ov ON ss.video_id = ov.id
        WHERE
            ov.STATUS IN (
                          ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                          ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                          ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                          ${@<EMAIL>},
                          ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}
                         )
            AND ov.rollback_id IS NULL
            AND ov.un_confirm_time IS NOT NULL
            AND ov.last_model_submit_time IS NOT NULL
        <if test="date != null and date != '' ">
            AND DATE_FORMAT( ov.create_time, '%Y-%m' ) = #{date}
        </if>
        GROUP BY
            ss.video_id
    </select>
    <select id="selectOrderVideoDeliveryDurationDataList"
            resultType="com.ruoyi.system.api.domain.bo.order.datastatistics.OrderVideoDurationDataBO">
        SELECT
            id AS video_id,
            TIMESTAMPDIFF(SECOND, last_model_submit_time, un_finished_time) / 86400.0 AS duration
        FROM
            order_video
        WHERE
            last_model_submit_time IS NOT NULL
            AND un_finished_time IS NOT NULL
            AND last_model_submit_time &lt; un_finished_time
            AND rollback_id IS NULL
            AND status IN (
                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                           ${@<EMAIL>}
            )
        <if test="date != null and date != '' ">
            AND DATE_FORMAT( create_time, '%Y-%m' ) = #{date}
        </if>
    </select>
    <select id="getAverageDeliveryDuration" resultType="java.math.BigDecimal">
        SELECT
            ROUND( SUM( TIMESTAMPDIFF( SECOND, last_model_submit_time, un_finished_time ) / 86400.0 ) / count(*), 4 ) AS averageDeliveryDuration
        FROM
            order_video
        WHERE
            last_model_submit_time IS NOT NULL
            AND un_finished_time IS NOT NULL
            AND last_model_submit_time &lt; un_finished_time
            AND rollback_id IS NULL
            AND STATUS IN (
                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                           ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                           ${@<EMAIL>}
            )
        <if test="date != null and date != '' ">
            AND DATE_FORMAT( create_time, '%Y-%m' ) = #{date}
        </if>
    </select>
    <select id="selectOrderVideoFeedbackDurationDataList"
            resultType="com.ruoyi.system.api.domain.bo.order.datastatistics.OrderVideoDurationDataBO">
        SELECT
            ov.id AS video_id,
            TIMESTAMPDIFF(SECOND, MIN(ovl.receipt_time), MIN(ovfb.create_time)) / 86400 AS duration
        FROM
            order_video ov
                JOIN order_video_logistic ovl ON ov.id = ovl.video_id
                JOIN order_video_feed_back ovfb ON ov.id = ovfb.video_id
        WHERE
            ov.rollback_id IS NULL
            AND ovl.receipt_time IS NOT NULL
            AND ovfb.create_time IS NOT NULL
            AND ov.`status` IN (
                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                ${@<EMAIL>}
                               )
        <if test="date != null and date != '' ">
            AND DATE_FORMAT( ov.create_time, '%Y-%m' ) = #{date}
        </if>
        GROUP BY
            ov.id
        HAVING
            MIN( ovl.receipt_time ) &lt; MIN( ovfb.create_time )
    </select>
    <select id="getAverageFeedbackDuration" resultType="java.math.BigDecimal">
        SELECT
            ROUND(SUM(duration) / COUNT(*), 4) AS averageDeliveryDuration
        FROM (
                 SELECT
                     ov.id AS video_id,
                     TIMESTAMPDIFF(SECOND, MIN(ovl.receipt_time), MIN(ovfb.create_time)) / 86400 AS duration
                 FROM
                     order_video ov
                         JOIN order_video_logistic ovl ON ov.id = ovl.video_id
                         JOIN order_video_feed_back ovfb ON ov.id = ovfb.video_id
                 WHERE
                     ov.rollback_id IS NULL
                   AND ovl.receipt_time IS NOT NULL
                   AND ovfb.create_time IS NOT NULL
                   AND ov.`status` IN (
                                       ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                       ${@<EMAIL>}
                     )
                <if test="date != null and date != '' ">
                    AND DATE_FORMAT( ov.create_time, '%Y-%m' ) = #{date}
                </if>
                 GROUP BY
                     ov.id
             ) AS t
    </select>
    <select id="getOrderVideoAverageDurationData"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoAverageDurationDataVO">
        -- 总数 CTE
        WITH status_time_map AS (
            SELECT
                ov.id,
                ov.STATUS,
                MIN( CASE WHEN ovf.target_status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode} THEN ovf.event_execute_time END ) AS t3,
                MIN( CASE WHEN ovf.target_status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode} THEN ovf.event_execute_time END ) AS t4,
                MIN( CASE WHEN ovf.target_status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode} THEN ovf.event_execute_time END ) AS t5,
                MIN( CASE WHEN ovf.target_status = ${@<EMAIL>} THEN ovf.event_execute_time END ) AS t8
            FROM
                order_video ov
                    JOIN order_video_flow ovf ON ov.id = ovf.video_id
            WHERE
                ov.STATUS IN (
                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode},
                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                            ${@<EMAIL>}
                             )
                AND ov.rollback_id IS NULL
            GROUP BY
                ov.id,
                ov.STATUS
        )
        SELECT
            IFNULL( ROUND( SUM( CASE WHEN t3 IS NOT NULL AND t4 IS NOT NULL THEN TIMESTAMPDIFF( SECOND, t3, t4 ) ELSE 0 END ) / COUNT( DISTINCT stm.id ) / 3600, 4 ) , 0 ) AS averageApprovalDuration,
            IFNULL(
                ROUND(
                    SUM(
                        CASE WHEN STATUS = ${@<EMAIL>}
                        AND t3 IS NOT NULL
                        AND t8 IS NOT NULL
                            THEN TIMESTAMPDIFF( SECOND, t3, t8 ) ELSE 0 END )
                        / COUNT( CASE WHEN STATUS = ${@<EMAIL>} THEN 1 END ) / 86400,
                    4
                ),
                0
            ) AS averageServiceDuration,
            COUNT( CASE WHEN ovt.video_id IS NOT NULL THEN ovt.video_id END ) AS taskOrderCount,
            IFNULL( ROUND(
                COUNT( CASE WHEN ovt.video_id IS NOT NULL THEN ovt.video_id END )
                    / COUNT( CASE WHEN stm.STATUS IN (
                                                      ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                      ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                      ${@<EMAIL>}
                                                     ) THEN stm.id END ),
                4 ) , 0 ) AS taskOrderRate,
            COUNT(
                DISTINCT
                CASE WHEN stm.STATUS IN (
                                         ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                         ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                         ${@<EMAIL>}
                                        )
                AND (
                    ( ovfb.earliest_feedback_time IS NULL AND TIMESTAMPDIFF( DAY, ovl.receipt_time, NOW() ) &gt;= 20 )
                    OR ( ovl.receipt_time &lt; ovfb.earliest_feedback_time AND TIMESTAMPDIFF( DAY, ovl.receipt_time, ovfb.earliest_feedback_time ) &gt;= 20 )
                ) THEN stm.id END ) AS dragOrderCount,
            ROUND(
                IFNULL(
                    COUNT(
                        DISTINCT
                        CASE WHEN stm.STATUS IN (
                                                 ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                 ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                 ${@<EMAIL>}
                                                )
                        AND (
                            ( ovfb.earliest_feedback_time IS NULL AND TIMESTAMPDIFF( DAY, ovl.receipt_time, NOW() ) &gt;= 20 )
                            OR ( ovl.receipt_time &lt; ovfb.earliest_feedback_time AND TIMESTAMPDIFF( DAY, ovl.receipt_time, ovfb.earliest_feedback_time ) &gt;= 20 )
                        ) THEN stm.id END )
                        / COUNT( DISTINCT CASE WHEN stm.STATUS IN (
                                                                   ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                                   ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                                   ${@<EMAIL>}
                                                                  ) THEN stm.id END ), 0 ), 4 ) AS dragOrderRate,
            COUNT(
                DISTINCT
                CASE WHEN ( stm.t4 IS NOT NULL AND stm.t5 IS NULL AND TIMESTAMPDIFF( DAY, stm.t4, NOW() ) &gt;= 7 )
                    OR ( stm.t4 &lt; stm.t5 AND TIMESTAMPDIFF( DAY, stm.t4, stm.t5 ) &gt;= 7 ) THEN stm.id END ) AS badOrderCount,
            ROUND(
                IFNULL(
                    COUNT(
                        DISTINCT
                        CASE WHEN ( stm.t4 IS NOT NULL AND stm.t5 IS NULL AND TIMESTAMPDIFF( DAY, stm.t4, NOW() ) &gt;= 7 )
                        OR ( stm.t4 &lt; stm.t5 AND TIMESTAMPDIFF( DAY, stm.t4, stm.t5 ) &gt;= 7 ) THEN stm.id END )
                        / COUNT( DISTINCT stm.id ), 0 ), 4 ) AS badOrderRate
        FROM
            status_time_map stm
            LEFT JOIN ( SELECT video_id FROM order_video_task GROUP BY video_id ) ovt ON ovt.video_id = stm.id
            LEFT JOIN (
                SELECT
                    video_id,
                    receipt_time
                FROM
                    ( SELECT video_id, receipt_time, ROW_NUMBER() OVER ( PARTITION BY video_id ORDER BY shipping_time DESC ) AS rn FROM order_video_logistic ) t
                WHERE
                    t.rn = 1
            ) ovl ON ovl.video_id = stm.id
            LEFT JOIN ( SELECT video_id, MIN( create_time ) AS earliest_feedback_time FROM order_video_feed_back GROUP BY video_id ) ovfb ON ovfb.video_id = stm.id
    </select>
    <select id="getOrderVideoAfterSaleTypeAnalysis"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoAfterSaleTypeAnalysisDetailVO">
        WITH task_union AS (
            SELECT
                DATE_FORMAT( ovt.create_time, '%c.%e' ) AS date,
                COUNT(*) AS after_sale_count,
                0 AS work_order_count,
                0 AS refund_count,
                0 AS rollback_count
            FROM
                order_video_task_detail ovtd
                    JOIN order_video_task ovt on ovt.id=ovtd.task_id
                    JOIN order_video ov ON ov.id = ovt.video_id
            WHERE
                DATE_FORMAT( ovt.create_time, '%Y-%m' ) = #{date}
                AND ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode}
            GROUP BY
                DATE_FORMAT( ovt.create_time, '%c.%e' )

            UNION ALL

            SELECT
                DATE_FORMAT( ovt.create_time, '%c.%e' ) AS date,
                0 AS after_sale_count,
                COUNT(*) AS work_order_count,
                0 AS refund_count,
                0 AS rollback_count
            FROM
                order_video_task_detail ovtd
                    JOIN order_video_task ovt on ovt.id=ovtd.task_id
                    JOIN order_video ov ON ov.id = ovt.video_id
            WHERE
                DATE_FORMAT( ovt.create_time, '%Y-%m' ) = #{date}
              AND ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@WORK_ORDER.getcode}
            GROUP BY
                DATE_FORMAT( ovt.create_time, '%c.%e' )

            UNION ALL

            SELECT
                DATE_FORMAT( ovr.apply_time, '%c.%e' ) AS date,
                0 AS after_sale_count,
                0 AS work_order_count,
                COUNT(*) AS refund_count,
                0 AS rollback_count
            FROM
                order_video_refund ovr
                    JOIN order_video ov ON ov.id = ovr.video_id
            WHERE
                ovr.refund_type = ${@<EMAIL>}
              AND DATE_FORMAT( ovr.apply_time, '%Y-%m' ) = #{date}
            GROUP BY
                DATE_FORMAT( ovr.apply_time, '%c.%e' )

            UNION ALL

            SELECT
                DATE_FORMAT( ovrr.operate_time, '%c.%e' ) AS date,
                0 AS after_sale_count,
                0 AS work_order_count,
                0 AS refund_count,
                COUNT(*) AS rollback_count
            FROM
                order_video_rollback_record ovrr
                    JOIN order_video ov ON ov.id = ovrr.video_id
            WHERE
                DATE_FORMAT( ovrr.operate_time, '%Y-%m' ) = #{date}
            GROUP BY
                DATE_FORMAT( ovrr.operate_time, '%c.%e' )
        ) SELECT
              date,
              SUM( after_sale_count ) AS afterSaleCount,
              SUM( work_order_count ) AS workOrderCount,
              SUM( refund_count ) AS reparationCount,
              SUM( rollback_count ) AS returnCount,
              ( SUM( after_sale_count ) + SUM( work_order_count ) + SUM( refund_count ) + SUM( rollback_count )) AS totalCount,
              ROUND( SUM( after_sale_count ) / NULLIF( SUM( after_sale_count + work_order_count + refund_count + rollback_count ), 0 ), 4 ) AS afterSaleRate,
              ROUND( SUM( work_order_count ) / NULLIF( SUM( after_sale_count + work_order_count + refund_count + rollback_count ), 0 ), 4 ) AS workOrderRate,
              ROUND( SUM( refund_count ) / NULLIF( SUM( after_sale_count + work_order_count + refund_count + rollback_count ), 0 ), 4 ) AS reparationRate,
              ROUND( SUM( rollback_count ) / NULLIF( SUM( after_sale_count + work_order_count + refund_count + rollback_count ), 0 ), 4 ) AS returnRate
        FROM
            task_union
        GROUP BY
            date
    </select>
    <select id="selectRefundAmountListByDate" resultType="java.math.BigDecimal">
        SELECT
            refund_amount
        FROM
            order_video_refund
        WHERE
            refund_status = ${@com.ruoyi.common.core.enums.RefundStatusEnum@AFTER_SALE_FINISHED.getcode}
            AND refund_type = ${@<EMAIL>}
            AND DATE_FORMAT( operate_time, '%Y-%m' ) = #{date}
    </select>
</mapper>