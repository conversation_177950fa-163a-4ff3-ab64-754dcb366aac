<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.PromotionActivityAmendmentRecordMapper">

    <select id="selectPromotionActivityAmendmentRecordList"
            resultType="com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityAmendmentRecordVO">
        SELECT
            paar.id,
            paar.activity_id,
            paar.type AS discount_type,
            paar.amount,
            paar.currency,
            paar.settle_discount_type,
            paar.settle_discount,
            paar.start_time,
            paar.end_time,
            paar.create_by,
            paar.create_time
        FROM
            promotion_activity_amendment_record paar
                JOIN promotion_activity pa ON pa.id = paar.activity_id
        WHERE pa.type = #{type}
        ORDER BY paar.create_time DESC
    </select>
</mapper>
