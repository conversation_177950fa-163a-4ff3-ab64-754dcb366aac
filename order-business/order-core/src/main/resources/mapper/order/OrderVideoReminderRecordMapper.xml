<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoReminderRecordMapper">


    <select id="selectListByCondition"
            resultType="com.ruoyi.system.api.domain.dto.order.OrderVideoReminderRecordListVO">
        SELECT
            ovrr.id,
            ovrr.video_id,
            ov.video_code,
            ov.product_pic,
            ov.product_chinese,
            ov.product_english,
            ov.create_order_business_id,
            ov.issue_id,
            ovrr.reminder,
            ovrr.reminder_time,
            ovrr.`status`
        FROM
            order_video_reminder_record ovrr
                LEFT JOIN order_video ov ON ov.id = ovrr.video_id
        <where>
            <if test="dto.keyword != null and dto.keyword != ''">
                (
                ov.order_num like concat('%', #{dto.keyword}, '%')
                OR ov.product_chinese like concat('%', #{dto.keyword}, '%')
                OR ov.product_english like concat('%', #{dto.keyword}, '%')
                OR ov.video_code like concat('%', #{dto.keyword}, '%')
                <if test="dto.businessIds != null and dto.businessIds.size() != 0">
                    OR ov.create_order_business_id IN
                    <foreach collection="dto.businessIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>

                </if>
                <if test="dto.issueIds != null and dto.issueIds.size() != 0">
                    OR ov.issue_id IN
                    <foreach collection="dto.issueIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
            )
            </if>

            <if test="dto.status != null and dto.status.size() != 0">
                AND ovrr.`status` IN
                <foreach collection="dto.status" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="reminderCount" resultType="com.ruoyi.system.api.domain.dto.order.OrderVideoReminderCountVO">
        SELECT
            COUNT( 1 ) AS total,
            COUNT( CASE WHEN ovrr.STATUS = #{status.UNTREATED} THEN 1 END) AS untreatedTotal
        FROM
            order_video_reminder_record ovrr
    </select>
</mapper>