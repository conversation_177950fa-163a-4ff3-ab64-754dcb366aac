<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoFeedBackMaterialInfoTaskDetailMapper">


    <select id="selectListByFeedBackIds"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailSimpleVO">
        SELECT
            ovfbmitd.feed_back_id,
            ovfbmitd.task_detail_id,
            ovtd.after_sale_class,
            ovtd.after_sale_video_type,
            ovtd.after_sale_pic_type,
            ovtd.work_order_type,
            ovtd.submit_time
        FROM
            order_video_feed_back_material_info_task_detail ovfbmitd
                JOIN order_video_task_detail ovtd ON ovtd.id = ovfbmitd.task_detail_id
        WHERE
            ovfbmitd.feed_back_id IN
            <foreach collection="feedBackIds" item="feedBackId" open="(" separator="," close=")">
                #{feedBackId}
            </foreach>
    </select>
    <select id="selectListByVideoIds"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterialInfoTaskDetail">
        SELECT
            ovfbmitd.*
        FROM
            order_video_feed_back_material_info_task_detail ovfbmitd
                JOIN order_video ov ON ov.id = ovfbmitd.video_id AND ov.rollback_id &lt;=&gt; ovfbmitd.rollback_id
        <where>
            <if test="videoIds != null and videoIds.size() > 0">
                AND ovfbmitd.video_id IN
                <foreach collection="videoIds" item="videoId" open="(" separator="," close=")">
                    #{videoId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>