<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoFeedBackMaterialMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterial">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="videoId" column="video_id" jdbcType="VARCHAR"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,video_id,url,
        remark,status,create_time,
        update_time
    </sql>
    <update id="markDownload">
        UPDATE order_video_feed_back_material
        SET download_status = CASE WHEN download_status = 0 THEN 1 ELSE 0 END
        WHERE id = #{id}
        ;
    </update>
    <select id="selectHasFeedBackMaterialByVideoIds" resultType="java.lang.Long">
        SELECT
            DISTINCT(video_id)
        FROM
            order_video_feed_back_material
        <where>
            <if test="videoIds != null and videoIds.size() > 0">
                AND video_id IN
                <foreach close=")" collection="videoIds" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectHasFeedBackMaterialByDto" resultType="java.lang.Long">
        SELECT
        DISTINCT(video_id)
        FROM
        order_video_feed_back_material
        <where>
            <if test="videoIds != null and videoIds.size() > 0">
                AND video_id IN
                <foreach close=")" collection="videoIds" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="downloadStatus != null">
               and download_status = #{downloadStatus}
            </if>
        </where>
    </select>
    <select id="selectListByVideoIds"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterial">
        SELECT
            ovfbm.*
        FROM
            order_video_feed_back_material ovfbm
                JOIN order_video ov ON ov.id = ovfbm.video_id AND ov.rollback_id &lt;=&gt; ovfbm.rollback_id
        <where>
            <if test="videoIds != null and videoIds.size() > 0">
                AND ovfbm.video_id IN
                <foreach collection="videoIds" item="videoId" open="(" separator="," close=")">
                    #{videoId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
