<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoTaskDetailFlowRecordMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetailFlowRecord" id="OrderTaskFlowRecordResult">
        <result property="id"    column="id"    />
        <result property="taskNum"    column="task_num"    />
        <result property="time"    column="time"    />
        <result property="assigneeId"    column="assignee_id"    />
        <result property="operateType"    column="operate_type"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOrderTaskFlowRecordVo">
        select id, task_num, time, assignee_id, operate_type, remark from order_video_task_flow_record
    </sql>

    <insert id="insertOrderTaskFlowRecord" parameterType="com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetailFlowRecord" useGeneratedKeys="true" keyProperty="id">
        insert into order_video_task_flow_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskNum != null and taskNum != ''">task_num,</if>
            <if test="time != null">time,</if>
            <if test="assigneeId != null">assignee_id,</if>
            <if test="operateType != null">operate_type,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskNum != null and taskNum != ''">#{taskNum},</if>
            <if test="time != null">#{time},</if>
            <if test="assigneeId != null">#{assigneeId},</if>
            <if test="operateType != null">#{operateType},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
</mapper>