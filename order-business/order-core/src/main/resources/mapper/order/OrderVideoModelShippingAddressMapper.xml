<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoModelShippingAddressMapper">

    <sql id="ModelShippingAddressField">
        SELECT
            ovmsa.id,
            ovmsa.video_id,
            ovmsa.rollback_id,
            ovmsa.shoot_model_id,
            ovmsa.nation,
            ovmsa.recipient,
            ovmsa.city,
            ovmsa.state,
            ovmsa.zipcode,
            ovmsa.detail_address,
            ovmsa.shipping_remark,
            ovmsa.shipping_pic,
            ovmsa.phone,
            ovmsa.phone_visible,
            ovmsa.logistic_flag,
            ovmsa.logistic_flag_remark,
            ovmsa.logistic_flag_time,
            ovmsa.create_time
    </sql>
    <select id="selectLastOrderVideoModelShippingAddressListByVideoId"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoModelShippingAddress">
        <include refid="ModelShippingAddressField"/>
        FROM order_video_model_shipping_address ovmsa
           INNER JOIN (
                        SELECT `video_id`, MAX(`create_time`) AS latest_create_time
                        FROM `order_video_model_shipping_address`
                        GROUP BY `video_id`
            )`self` ON ovmsa.`video_id` = `self`.`video_id` AND ovmsa.`create_time` = `self`.latest_create_time
            <if test="videoIds != null and videoIds.size() > 0">
                WHERE ovmsa.`video_id` IN
                <foreach collection="videoIds" item="videoId" open="(" separator="," close=")">
                    #{videoId}
                </foreach>
            </if>
    </select>
    <select id="selectModelShippingAddressByLogisticNumber" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoModelShippingAddress">
        SELECT ovmsa.id,ovmsa.video_id,ovmsa.rollback_id
        FROM order_video_logistic ol
        JOIN (
            SELECT video_id, MAX(create_time) AS latest_time
            FROM order_video_logistic  where is_cancel = ${@<EMAIL>}
            GROUP BY video_id
        ) latest ON ol.video_id = latest.video_id AND ol.create_time = latest.latest_time
        JOIN order_video_model_shipping_address ovmsa on ovmsa.id = ol.shipping_address_id
        where ol.number in
        <foreach collection="numbers" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>