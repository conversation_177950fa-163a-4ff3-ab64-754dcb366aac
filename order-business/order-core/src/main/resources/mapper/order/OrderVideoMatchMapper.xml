<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoMatchMapper">


    <sql id="selectOrderVideoMatchColumn">
        SELECT
            ovm.`id`,
            ovm.`video_id`,
            ovm.`rollback_id`,
            ovm.`product_pic_change`,
            ovm.`goods_info_change`,
            ovm.`intention_model_change`,
            ovm.`pic_count_change`,
            ovm.`shoot_required_change`,
            ovm.`cautions_change`,
            ovm.`count`,
            ovm.`status`,
            ovm.`pause_reason`,
            ovm.`start_time`,
            ovm.`end_time`,
            ovm.`schedule_type`,
            ovm.`commission_unit`,
            ovm.`commission`,
            ovm.`overstatement`,
            ovm.`carry_type`,
            ovm.`main_carry_count`,
            ovm.`main_carry_video_id`,
            ovm.`carry_ignore`,
            ovm.`shipping_remark`,
            ovm.`shipping_pic`,
            ovm.`submit_time`,
            ovm.`shoot_model_id`,
            ovm.`shoot_model_type`,
            ovm.`shoot_model_platform`,
            ovm.`shoot_model_cooperation`,
            ovm.`shoot_model_person_id`,
            ovm.`shoot_model_person_name`,
            ovm.`issue_id`,
            ovm.`shoot_required_original`,
            ovm.`shoot_required_translation`,
            ovm.`create_by`,
            ovm.`create_by_id`,
            ovm.`create_time`,
            ovm.`update_by`,
            ovm.`update_by_id`,
            ovm.`update_time`
        FROM order_video_match ovm
    </sql>
    
    <sql id="mainCarryOrderVideoStatusFilter">
        AND ov.status IN (
                ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                ${@<EMAIL>}
        )
    </sql>

    <sql id="carryOrderVideoStatusFilter">
        AND ov.status IN (
                ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode},
                ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                ${@<EMAIL>}
        )
    </sql>

    <select id="countVideoOutModelByVideoIds"
            resultType="com.ruoyi.system.api.domain.vo.order.CountVideoOutModelVO">
        SELECT
            ovm.video_id,
            SUM( CASE WHEN ovmpm.`STATUS` = 3 THEN 1 ELSE 0 END ) AS count_status_3
        FROM
            order_video_match ovm
                LEFT JOIN order_video_match_preselect_model ovmpm ON ovm.id = ovmpm.match_id
        <if test="videoIds != null and videoIds.size() > 0">
            WHERE ovm.video_id IN
            <foreach collection="videoIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
            ovm.video_id
    </select>
    <select id="selectActiveListByVideoIds"
            resultType="com.ruoyi.system.api.domain.vo.order.VideoMatchOrderVO">
        SELECT ovm.id,
               ovm.video_id,
               ovm.status,
               ovm.pause_reason,
               ovm.submit_time,
               ovm.shoot_model_id,
               ovm.create_time,
               ovm.shoot_model_platform,
               ovm.carry_type,
               ovm.commission_unit,
               ovm.commission
        FROM (
            SELECT
            id,
            video_id,
            status,
            pause_reason,
            submit_time,
            shoot_model_id,
            create_time,
            shoot_model_platform,
            carry_type,
            commission_unit,
            commission,
            ROW_NUMBER() OVER(PARTITION BY video_id ORDER BY start_time DESC) as rn
            FROM
                order_video_match
            <where>
                <if test="videoIds != null and videoIds.size() > 0">
                    video_id IN
                    <foreach collection="videoIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
        ) AS ovm
        <where>
            rn = 1;
        </where>
    </select>
    <select id="selectOrderPoolListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderPoolListVO">
        SELECT
            ovm.id,
            ovm.video_id,
            ovm.rollback_id,
            ovm.product_pic_change,
            ovm.goods_info_change,
            ovm.intention_model_change,
            ovm.pic_count_change,
            ovm.shoot_required_change,
            ovm.particular_emphasis_change,
            ovm.order_specification_require_change,
            ovm.selling_point_product_change,
            ovm.cautions_change,
            ov.product_pic,
            ov.is_care,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.platform,
            ov.video_style,
            ov.shooting_country,
            ov.model_type,
            ov.video_format,
            ov.pic_count,
            ov.reference_pic AS reference_pic_id,
            ov.intention_model_id,
            ov.contact_id,
            ovm.count,
            ot.merchant_code,
            ov.create_order_biz_user_id,
            ov.create_order_user_id,
            ov.create_order_user_name,
            ov.create_order_user_nick_name,
            ov.is_gund,
            ov.status,
            COUNT( ovmpm.id ) AS preselect_model_count,
            ovm.start_time,
            CASE WHEN EXISTS (
                SELECT 1
                FROM order_video_match_preselect_model ovmpm
                WHERE ovmpm.match_id = prevovm.id
                  AND ovmpm.oust_type = 1
            ) THEN 1 ELSE 0 END AS isRejectAfterSubmitModel
        FROM
            order_video_match ovm
                JOIN order_video ov ON ov.id = ovm.video_id AND ov.STATUS = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode}
                LEFT JOIN order_table ot ON ot.order_num = ov.order_num
                LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                                                                         AND ovmpm.STATUS != ${@<EMAIL>}
                                                                         AND ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
                                                                         AND (
                                                                                ovmpm.add_type != ${@<EMAIL>}
                                                                                OR ovmpm.model_intention = ${@<EMAIL>}
                                                                            )
                LEFT JOIN order_video_match_preselect_model ovmpm_ex ON ovmpm_ex.match_id = ovm.id AND ovmpm_ex.status = ${@<EMAIL>}
                LEFT JOIN LATERAL (
                    SELECT
                        p.id
                    FROM
                        order_video_match AS p
                    WHERE
                        p.video_id = ovm.video_id
                        AND p.rollback_id &lt;=> ovm.rollback_id
                        AND p.count &lt; ovm.count
                        AND p.status = 1
                    ORDER BY p.count DESC
                        LIMIT 1
                ) AS prevovm ON TRUE
        <where>
            AND ovm.end_time IS NULL
            AND ovmpm_ex.id IS NULL
            <if test="dto.backUserRelevanceModelIds != null and dto.backUserRelevanceModelIds.size() > 0 and dto.currentUserIsAdmin == false">
                AND ovm.id NOT IN (SELECT match_id FROM order_video_match_preselect_model WHERE (model_id IN
                <foreach collection="dto.backUserRelevanceModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND `status` != ${@<EMAIL>}
                AND select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
                AND (  add_type != ${@<EMAIL>}
                    OR distribution_result NOT IN (
                                                    ${@<EMAIL>} ,
                                                    ${@com.ruoyi.common.core.enums.DistributionResultEnum@WANT_NOT.getCode} ,
                                                    ${@com.ruoyi.common.core.enums.DistributionResultEnum@CANCEL_DISTRIBUTION.getCode}
                                                    )
                    )
                )
            )
            </if>
            <if test="dto.keyword != null and dto.keyword != ''">
                AND (
                LOWER(ov.video_code) LIKE LOWER(CONCAT('%', #{dto.keyword}, '%'))
                OR LOWER(ov.product_chinese) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.product_english) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.product_link) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ot.merchant_code) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.create_order_user_nick_name) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.create_order_user_name) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                <if test="dto.intentionModelIds != null and dto.intentionModelIds.size() > 0">
                    OR ov.intention_model_id IN
                    <foreach collection="dto.intentionModelIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>

            <if test="dto.shootingCountry != null">
                AND ov.shooting_country = #{dto.shootingCountry}
            </if>

            <if test="dto.shootingCountries != null and dto.shootingCountries.size() > 0">
                AND ov.shooting_country in
                <foreach collection="dto.shootingCountries" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.modelTypes != null and dto.modelTypes.size() > 0">
                AND ov.model_type in
                <foreach collection="dto.modelTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.modelType != null">
                AND ov.model_type = #{dto.modelType}
            </if>

            <if test="dto.countSqlList != null and dto.countSqlList.size() > 0">
                AND
                <foreach collection="dto.countSqlList" item="item" open="(" separator=" OR " close=")">
                    ovm.count ${item}
                </foreach>
            </if>

            <if test="dto.matchStartTimes != null">
                AND (
                <foreach item="value" index="key" collection="dto.matchStartTimeMap.entrySet()" separator=" or " >
                    date_format(ovm.start_time, '%Y-%m-%d %H:%i:%s') BETWEEN #{key} AND #{value}
                </foreach>
                )
            </if>

            <if test="dto.beforeMatchStartTime != null">
                and DATE ( ovm.start_time ) &lt;= #{dto.beforeMatchStartTime}
            </if>

            <if test="dto.beforeMatchStartTimeStart != null and dto.beforeMatchStartTimeEnd != null ">
                AND ovm.start_time BETWEEN #{dto.beforeMatchStartTimeStart} AND #{dto.beforeMatchStartTimeEnd}
            </if>

            <if test="dto.isCare != null">
                AND ov.is_care = #{dto.isCare}
            </if>

            <!-- 新增筛选项：淘汰原因 -->
            <if test="dto.preselectModelOustType != null and dto.preselectModelOustType.size() > 0">
                AND EXISTS (
                SELECT 1
                FROM order_video_match_preselect_model ovmpm_oust left join order_video_match ovm_oust on ovmpm_oust.match_id = ovm_oust.id
                WHERE ovm_oust.video_id = ovm.video_id
                AND ovmpm_oust.status = ${@<EMAIL>}
                AND ovmpm_oust.oust_type IN
                <foreach collection="dto.preselectModelOustType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="dto.issueIds != null and dto.issueIds.size() > 0">
                    AND ovmpm_oust.model_person_id IN
                    <foreach collection="dto.issueIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <!-- 新增筛选项：英文部客服 -->
            <!-- 外层判断：只要有任何一个客服相关的筛选条件，就包含整个 AND 块 -->
            <if test="((dto.issueIds != null and dto.issueIds.size() > 0) or (dto.englishCustomerServiceNames != null and dto.englishCustomerServiceNames.size() > 0) or (dto.englishCustomerServiceModelIds != null and dto.englishCustomerServiceModelIds.size() > 0 and dto.currentUserIsAdmin == false)) and dto.preselectModelOustType == null">
                <!-- 使用 trim 包裹整个 OR 逻辑，防止生成空的 () -->
                <trim prefix="AND (" suffix=")" prefixOverrides="OR ">
                    <!-- 条件1: 筛选主匹配单的拍摄客服 -->
                    <!-- 仅当 issueIds 或 englishCustomerServiceNames 有值时才生成此条件 -->
                    <if test="(dto.issueIds != null and dto.issueIds.size() > 0) or (dto.englishCustomerServiceNames != null and dto.englishCustomerServiceNames.size() > 0)">
                        OR (
                        <!-- 使用 trim 来处理内部的 AND 逻辑，防止出现 AND () -->
                        <trim prefixOverrides="AND ">
                            <if test="dto.issueIds != null and dto.issueIds.size() > 0">
                                AND ovm.shoot_model_person_id IN
                                <foreach collection="dto.issueIds" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                            <if test="dto.englishCustomerServiceNames != null and dto.englishCustomerServiceNames.size() > 0">
                                AND ovm.shoot_model_person_name IN
                                <foreach collection="dto.englishCustomerServiceNames" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                        </trim>
                        )
                    </if>
                    <!-- 条件2: 筛选预选模特中涉及的客服 (EXISTS 子查询) -->
                    <!-- 仅当 issueIds/englishCustomerServiceNames 或 englishCustomerServiceModelIds 有值时才生成此 EXISTS 块 -->
                    <if test="(dto.issueIds != null and dto.issueIds.size() > 0) or (dto.englishCustomerServiceNames != null and dto.englishCustomerServiceNames.size() > 0) or (dto.englishCustomerServiceModelIds != null and dto.englishCustomerServiceModelIds.size() > 0)">
                        OR EXISTS (
                        SELECT 1
                        FROM order_video_match_preselect_model ovmpm_cs_sub  left join order_video_match ovm_oustt on ovm_oustt.id = ovmpm_cs_sub.match_id
                        WHERE ovm_oustt.video_id = ovm.video_id
                        AND ovmpm_cs_sub.add_type != ${@<EMAIL>}
                        AND ovmpm_cs_sub.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
                        AND (
                        <!-- 使用 trim 包裹子查询内部的 OR 逻辑，防止生成空的 () -->
                        <trim prefixOverrides="OR ">
                            <!-- 子条件A: model_person_id 不为空，且符合客服ID/名称筛选 -->
                            <!-- 仅当 issueIds 或 englishCustomerServiceNames 有值时才生成此条件 -->
                            <if test="(dto.issueIds != null and dto.issueIds.size() > 0) or (dto.englishCustomerServiceNames != null and dto.englishCustomerServiceNames.size() > 0)">
                                OR (
                                ovmpm_cs_sub.model_person_id IS NOT NULL
                                <!-- 使用 trim 处理内部的 AND 逻辑，防止出现 AND () -->
                                <trim prefix="AND (" suffix=")" prefixOverrides="AND ">
                                    <if test="dto.issueIds != null and dto.issueIds.size() > 0">
                                        AND ovmpm_cs_sub.model_person_id IN
                                        <foreach collection="dto.issueIds" item="item" open="(" separator="," close=")">
                                            #{item}
                                        </foreach>
                                    </if>
                                    <if test="dto.englishCustomerServiceNames != null and dto.englishCustomerServiceNames.size() > 0">
                                        AND ovmpm_cs_sub.model_person_name IN
                                        <foreach collection="dto.englishCustomerServiceNames" item="item" open="(" separator="," close=")">
                                            #{item}
                                        </foreach>
                                    </if>
                                </trim>
                                )
                            </if>
                            <!-- 子条件B: model_person_id 为空，但模特ID在englishCustomerServiceModelIds中 -->
                            <!-- 仅当 englishCustomerServiceModelIds 有值时才生成此条件 -->
                            <if test="dto.englishCustomerServiceModelIds != null and dto.englishCustomerServiceModelIds.size() > 0">
                                OR (
                                ovmpm_cs_sub.model_person_id IS NULL
                                AND ovmpm_cs_sub.model_id IN
                                <foreach collection="dto.englishCustomerServiceModelIds" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                                )
                            </if>
                        </trim>
                        )
                        )
                    </if>
                </trim>
            </if>
        </where>
        GROUP BY
            ovm.id,
            prevovm.id

        <if test="dto.preselection != null">
            HAVING preselect_model_count BETWEEN #{dto.preselectionBegin} AND #{dto.preselectionEnd}
        </if>
    </select>
    <select id="selectMyPreselectDockingList"
            resultType="com.ruoyi.system.api.domain.vo.order.MyPreselectDockingListVO">
        SELECT
            ovm.id,
            ovm.video_id,
            ovm.rollback_id,
            ovm.product_pic_change,
            ovm.goods_info_change,
            ovm.intention_model_change,
            ovm.pic_count_change,
            ovm.shoot_required_change,
            ovm.particular_emphasis_change,
            ovm.order_specification_require_change,
            ovm.selling_point_product_change,
            ovm.cautions_change,
            ov.product_pic,
            ov.is_care,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.reference_video_link,
            ov.platform,
            ov.video_style,
            ov.shooting_country,
            ov.model_type,
            ov.video_format,
            ov.pic_count,
            ov.refund_pic_count,
            ov.reference_pic AS reference_pic_id,
            ot.merchant_code,
            ov.create_order_biz_user_id,
            ov.create_order_user_id,
            ov.create_order_user_name,
            ov.create_order_user_nick_name,
            ov.contact_id,
            ov.video_duration,
            ov.is_gund,
            ovm.count,
            ovm.start_time,
            ovm.commission_unit,
            ovm.commission,
            ovm.carry_type,
            CASE WHEN EXISTS (
                SELECT 1
                FROM order_video_match_preselect_model ovmpm
                WHERE ovmpm.match_id = prevovm.id
                  AND ovmpm.oust_type = 1
            ) THEN 1 ELSE 0 END AS isRejectAfterSubmitModel
        FROM
            order_video_match ovm
                JOIN order_video ov ON ov.id = ovm.video_id AND ov.STATUS = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode}
                LEFT JOIN order_table ot ON ot.order_num = ov.order_num
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                    AND ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
                    AND ovmpm.`status` != ${@<EMAIL>}
                LEFT JOIN LATERAL (
                    SELECT
                        p.id
                    FROM
                        order_video_match AS p
                    WHERE
                        p.video_id = ovm.video_id
                        AND p.rollback_id &lt;=> ovm.rollback_id
                        AND p.count &lt; ovm.count
                        AND p.status = 1
                    ORDER BY p.count DESC
                        LIMIT 1
                ) AS prevovm ON TRUE
        <where>
            ovm.end_time IS NULL

            AND (
                ovmpm.add_type != ${@<EMAIL>}
                OR (ovmpm.add_type = ${@<EMAIL>} AND ovmpm.model_intention = ${@<EMAIL>})
            )

            <if test="dto.backUserRelevanceModelIds != null and dto.backUserRelevanceModelIds.size() > 0 and dto.currentUserIsAdmin == false and dto.canViewAllPreselection == false">
                AND ovmpm.model_id IN
                <foreach collection="dto.backUserRelevanceModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.keyword != null and dto.keyword != ''">
                AND (
                LOWER(ov.video_code) LIKE LOWER(CONCAT('%', #{dto.keyword}, '%'))
                OR LOWER(ov.product_chinese) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.product_english) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.product_link) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ot.merchant_code) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.create_order_user_nick_name) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.create_order_user_name) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                <if test="dto.intentionModelIds != null and dto.intentionModelIds.size() > 0">
                    OR ov.intention_model_id IN
                    <foreach collection="dto.intentionModelIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR ovmpm.model_id IN
                    <foreach collection="dto.intentionModelIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>

            <if test="dto.isCare != null">
                AND ov.is_care = #{dto.isCare}
            </if>

            <if test="dto.countSqlList != null and dto.countSqlList.size() > 0">
                AND
                <foreach collection="dto.countSqlList" item="item" open="(" separator=" OR " close=")">
                    ovm.count ${item}
                </foreach>
            </if>

            <if test="dto.modelIntentions != null and dto.modelIntentions.size() > 0">
                AND ovmpm.model_intention IN
                <foreach collection="dto.modelIntentions" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.status != null">
                AND ovmpm.status = #{dto.status}
            </if>

            <if test="dto.statusList != null and dto.statusList.size() > 0">
                AND ovmpm.status IN
                <foreach collection="dto.statusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.addPreselectTimes != null and dto.addPreselectTimes.size() > 0">
                AND (
                <foreach item="value" index="key" collection="dto.addPreselectTimeMap.entrySet()" separator=" or " >
                    date_format(ovmpm.add_time, '%Y-%m-%d %H:%i:%s') BETWEEN #{key} AND #{value}
                </foreach>
                )
            </if>

            <if test="dto.matchStartTimes != null">
                AND (
                <foreach item="value" index="key" collection="dto.matchStartTimeMap.entrySet()" separator=" or " >
                    date_format(ovm.start_time, '%Y-%m-%d %H:%i:%s') BETWEEN #{key} AND #{value}
                </foreach>
                )
            </if>

            <if test="dto.addTypes != null and dto.addTypes.size() > 0">
                AND ovmpm.add_type IN
                <foreach collection="dto.addTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.preselectModelIds != null and dto.preselectModelIds.size() > 0">
                AND ovmpm.model_id IN
                <foreach collection="dto.preselectModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.beforeMatchStartTime != null">
                and DATE ( ovm.start_time ) &lt;= #{dto.beforeMatchStartTime}
            </if>

            <if test="dto.beforeMatchStartTimeStart != null and dto.beforeMatchStartTimeEnd != null ">
                AND ovm.start_time BETWEEN #{dto.beforeMatchStartTimeStart} AND #{dto.beforeMatchStartTimeEnd}
            </if>

            <if test="dto.carryType != null and dto.carryType.size() >0">
                AND ovm.carry_type IN
                <foreach collection="dto.carryType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.preselectModelIds != null and dto.preselectModelIds.size() > 0 ">
                AND ovmpm.model_id IN
                <foreach collection="dto.preselectModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.modelSelectionStatus != null">
                <if test="dto.modelSelectionStatus == 0">
                    AND ovmpm.status IN (
                        ${@com.ruoyi.common.core.enums.PreselectStatusEnum@UN_JOINTED.getCode},
                        ${@<EMAIL>}
                    )
                </if>
                <if test="dto.modelSelectionStatus == 1">
                    AND ovmpm.status = ${@<EMAIL>}
                </if>
            </if>
        </where>
        GROUP BY
            ovm.id,
            prevovm.id

        <if test="dto.preselection != null">
            HAVING COUNT( ovmpm.id ) BETWEEN #{dto.preselectionBegin} AND #{dto.preselectionEnd}
        </if>
    </select>

    <select id="getHistoryPreselectModelCountList" resultType = "com.ruoyi.system.api.domain.vo.order.HistoryPreselectModelCountVO">
        SELECT
            ovm.video_id,
            COUNT( ovmpm.id ) AS history_preselect_model_count
        FROM
            order_video_match_preselect_model ovmpm
                JOIN order_video_match ovm ON ovmpm.match_id = ovm.id
        WHERE
            ovmpm.`status` = ${@<EMAIL>}
            AND ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
            AND ( ovmpm.add_type != ${@<EMAIL>} OR ovmpm.model_intention = ${@<EMAIL>} )
            AND ovm.video_id IN
            <foreach collection="videoIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY
            ovm.video_id
    </select>
    <select id="selectMyPreselectEndMatchList"
            resultType="com.ruoyi.system.api.domain.vo.order.MyPreselectEndMatchListVO">
        SELECT
            ovm.id,
            ovm.video_id,
            ovm.rollback_id,
            ovm.product_pic_change,
            ovm.goods_info_change,
            ovm.intention_model_change,
            ovm.pic_count_change,
            ovm.shoot_required_change,
            ovm.particular_emphasis_change,
            ovm.order_specification_require_change,
            ovm.selling_point_product_change,
            ov.product_pic,
            ov.is_care,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.reference_video_link,
            ov.platform,
            ov.video_style,
            ov.shooting_country,
            ov.model_type,
            ov.video_format,
            ovm.shoot_model_id,
            ovm.shoot_model_type,
            ovm.shoot_model_platform,
            ovm.shoot_model_cooperation,
            ovm.shoot_model_person_name,
            ov.pic_count,
            ov.refund_pic_count,
            ov.reference_pic AS reference_pic_id,
            ot.merchant_code,
            ov.create_order_user_nick_name,
            ov.create_order_user_name,
            ov.create_order_user_id,
            ov.contact_id,
            ov.video_duration,
            ovm.count,
            ovm.status AS match_status,
            ovm.issue_id,
            ovm.start_time,
            ovm.end_time,
            ovm.commission_unit,
            ovm.commission,
            ovm.carry_type,
            ov.`status`,
            ov.is_gund,
            CASE WHEN EXISTS (
                SELECT 1
                FROM order_video_match_preselect_model ovmpm
                WHERE ovmpm.match_id = prevovm.id
                    AND ovmpm.oust_type = 1
            ) THEN 1 ELSE 0 END AS isRejectAfterSubmitModel
        FROM
            order_video_match ovm
                JOIN order_video ov ON ov.id = ovm.video_id AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode}
                LEFT JOIN order_table ot ON ot.order_num = ov.order_num
                LEFT JOIN LATERAL (
                    SELECT
                        p.id
                    FROM
                        order_video_match AS p
                    WHERE
                        p.video_id = ovm.video_id
                        AND p.rollback_id &lt;=> ovm.rollback_id
                        AND p.count &lt; ovm.count
                        AND p.status = 1
                    ORDER BY p.count DESC
                        LIMIT 1
                ) AS prevovm ON TRUE
        <where>
            ovm.end_time IS NOT NULL

            <if test="dto.keyword != null and dto.keyword != ''">
                AND (
                LOWER(ov.video_code) LIKE LOWER(CONCAT('%', #{dto.keyword}, '%'))
                OR LOWER(ov.product_chinese) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.product_english) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.product_link) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ot.merchant_code) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.create_order_user_nick_name) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.create_order_user_name) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                <if test="dto.intentionModelIds != null and dto.intentionModelIds.size() > 0">
                    OR ov.intention_model_id IN
                    <foreach collection="dto.intentionModelIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>

            <if test="dto.beforeMatchStartTimeStart != null and dto.beforeMatchStartTimeEnd != null ">
                AND ovm.start_time >= #{dto.beforeMatchStartTimeStart}
                AND ovm.start_time &lt; DATE_ADD(DATE(#{dto.beforeMatchStartTimeEnd}), INTERVAL 1 DAY)
            </if>

            <if test="dto.beforeMatchEndTimeStart != null and dto.beforeMatchEndTimeEnd != null ">
                AND ovm.end_time >= #{dto.beforeMatchEndTimeStart}
                AND ovm.end_time &lt; DATE_ADD(DATE(#{dto.beforeMatchEndTimeEnd}), INTERVAL 1 DAY)
            </if>

            <if test="dto.isCare != null">
                AND ov.is_care = #{dto.isCare}
            </if>

            <if test="dto.countSqlList != null and dto.countSqlList.size() > 0">
                AND
                <foreach collection="dto.countSqlList" item="item" open="(" separator=" OR " close=")">
                    ovm.count ${item}
                </foreach>
            </if>

            <if test="dto.carryType != null">
                AND ovm.carry_type = #{dto.carryType}
            </if>

            <if test="dto.isMyOrder != null">
                AND  <if test="dto.isMyOrder == false">
                        NOT
                    </if>
                            EXISTS (
                                SELECT 1
                                FROM order_video_match_preselect_model ovmpm
                                WHERE ovmpm.match_id = ovm.id
                                <choose>
                                    <when test="dto.backUserRelevanceModelIds != null and dto.backUserRelevanceModelIds.size() > 0">
                                        AND ovmpm.model_id IN
                                        <foreach collection="dto.backUserRelevanceModelIds" item="item" open="(" separator="," close=")">
                                            #{item}
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        AND ovmpm.id = -1
                                    </otherwise>
                                </choose>
                            )
            </if>

            <if test="dto.orderStatus != null">
                AND ov.status = #{dto.orderStatus}
            </if>

            <if test="dto.shootModelIds != null and dto.shootModelIds.size() > 0 ">
                AND ovm.shoot_model_id IN
                <foreach collection="dto.shootModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.addTypes != null and dto.addTypes.size() > 0 ">
                AND EXISTS (
                    SELECT 1
                    FROM order_video_match_preselect_model ovmpm
                    WHERE ovmpm.match_id = ovm.id
                    AND
                    <foreach collection="dto.addTypes" item="item" open="(" separator=" OR " close=")">
                        <if test="item != 4">
                            ovmpm.add_type = #{item}
                        </if>
                        <if test="item == 4">
                            ( ovmpm.add_type = 4 AND ovmpm.distribution_result = 2 )
                        </if>
                    </foreach>

                    <if test="dto.shootModelIds != null and dto.shootModelIds.size() > 0 ">
                        AND ovmpm.model_id IN
                        <foreach collection="dto.shootModelIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                )
            </if>
            <if test="dto.matchingResults != null">
                <if test="dto.matchingResults == 1">
                    AND ovm.submit_time IS NOT NULL
                </if>
                <if test="dto.matchingResults == 2">
                    AND ovm.submit_time IS NULL
                </if>
            </if>

        </where>
    </select>

    <update id="clearFlag">
        UPDATE order_video_match
            SET
                schedule_type =null,
                commission_unit =null,
                commission =null,
                overstatement =null,
                carry_type =null,
                main_carry_count =null,
                main_carry_video_id =null,
                shipping_remark =null,
                shipping_pic =null
        <where>
            <if test="checkSelected != null and checkSelected == 1 ">
                NOT EXISTS (
                    SELECT 1
                    FROM order_video_match_preselect_model pm
                    WHERE pm.match_id = order_video_match.id
                    AND pm.status = ${@<EMAIL>}
                )
            </if>
            <if test="matchIds != null and matchIds.size() > 0">
                AND id IN
                <foreach collection="matchIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>

    <select id="selectVideoIdByAddPreselectTimeOfOrderVideo" resultType="java.lang.Long">
        WITH ranked_ovm AS (
            SELECT *,
                   ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY count DESC) AS rn
            FROM order_video_match
        )
        SELECT
            DISTINCT ovm.video_id
        FROM
            ranked_ovm ovm
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
        WHERE
            ovm.rn = 1
            AND DATE_FORMAT( ovmpm.add_time, '%Y-%m-%d %H:%i:%s' ) BETWEEN #{addPreselectTimeBegin} AND #{addPreselectTimeEnd}
            AND ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
            AND ovmpm.`status` != ${@<EMAIL>}
            AND (
                ovmpm.add_type != ${@<EMAIL>}
                  OR ovmpm.distribution_result = ${@<EMAIL>}
              )
    </select>

    <select id="selectCarryMatchListByMainCarryVideoId" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoMatch">
        <include refid="selectOrderVideoMatchColumn"/>
            JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
            JOIN order_video ov ON ov.id = ovm.video_id
        <where>
            ovmpm.`status` = ${@<EMAIL>}
            AND ovm.schedule_type = ${@com.ruoyi.common.core.enums.ScheduleTypeEnum@CARRY_ORDER.getCode}
            AND ovm.carry_type = ${@<EMAIL>}
            AND ovm.carry_ignore = ${@com.ruoyi.common.core.enums.FlagEnum@NO_FLAG.getCode}
            AND ovm.main_carry_video_id = #{mainCarryVideoId}
            <include refid="carryOrderVideoStatusFilter"/>
        </where>
    </select>

    <select id="getModelMainCarryCountByModelIds" resultType="com.ruoyi.system.api.domain.vo.order.ModelCarryVO">
        SELECT
            ovmpm.model_id,
            SUM( CASE WHEN ovm.carry_type = ${@com.ruoyi.common.core.enums.CarryTypeEnum@MAIN_CARRY.getCode} THEN ovm.main_carry_count ELSE 0 END ) AS main_carry_count
        FROM
            order_video_match ovm
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                JOIN order_video ov on ov.id = ovm.video_id
        <where>
            ovmpm.`status` = ${@<EMAIL>}
            AND ovm.schedule_type = ${@com.ruoyi.common.core.enums.ScheduleTypeEnum@CARRY_ORDER.getCode}
            <include refid="mainCarryOrderVideoStatusFilter"/>
            <if test="modelIds != null and modelId.size() > 0">
                AND ovmpm.model_id IN
                <foreach collection="modelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getModelCarriedCountByModelIds" resultType="com.ruoyi.system.api.domain.vo.order.ModelCarryVO">
        SELECT
            ovmpm.model_id,
            COUNT( ovm.id ) AS carried_count
        FROM
            order_video_match ovm
                JOIN order_video_match_preselect_model ovmpm on ovmpm.match_id = ovm.id
                JOIN order_video ov on ov.id = ovm.video_id
        <where>
            ovmpm.`status` = ${@<EMAIL>}
            AND ovm.schedule_type = ${@com.ruoyi.common.core.enums.ScheduleTypeEnum@CARRY_ORDER.getCode}
            AND ovm.carry_type = ${@<EMAIL>}
            AND ovm.carry_ignore = ${@com.ruoyi.common.core.enums.FlagEnum@NO_FLAG.getcode}
            <include refid="carryOrderVideoStatusFilter"/>
            <if test="modelIds != null and modelIds.size() > 0">
                AND ovmpm.model_id IN
                <foreach collection="modelIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getModelLeftCarryCountByModelIds" resultType="com.ruoyi.system.api.domain.vo.order.ModelCarryVO">
        SELECT
            ovmpm.model_id,
            COALESCE (SUM( CASE WHEN
                ovm.carry_type = ${@com.ruoyi.common.core.enums.CarryTypeEnum@MAIN_CARRY.getCode}
                <include refid="mainCarryOrderVideoStatusFilter"/>
                THEN ovm.main_carry_count ELSE 0 END ),0)
                -
            COALESCE (COUNT( CASE WHEN
                ovm.carry_type = ${@<EMAIL>}
                AND ovm.carry_ignore = ${@com.ruoyi.common.core.enums.FlagEnum@NO_FLAG.getcode}
                <include refid="carryOrderVideoStatusFilter"/>
                THEN ovm.id END ), 0 )
                AS leftCarryCount
        FROM
            order_video_match ovm
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                JOIN order_video ov ON ov.id = ovm.video_id
        <where>
            ovmpm.`status` = ${@<EMAIL>}
            AND ovm.schedule_type = ${@com.ruoyi.common.core.enums.ScheduleTypeEnum@CARRY_ORDER.getCode}
            <if test="modelIds != null and modelIds.size() > 0">
                and ovmpm.model_id in
                <foreach collection="modelIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
            ovmpm.model_id
    </select>

    <select id="markOrderMainCarryListByModelId" resultType="com.ruoyi.system.api.domain.vo.order.ModelCarryVO">
        SELECT
            ovm.video_id,
            ov.video_code,
            ovm.main_carry_count,
            ovm.main_carry_count - IFNULL( cc.carried_count, 0 ) AS leftCarryCount
        FROM
            order_video_match ovm
                JOIN order_video ov ON ov.id = ovm.video_id
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                LEFT JOIN (
                    SELECT
                        sub_ovm.main_carry_video_id,
                        COUNT(*) AS carried_count
                    FROM
                        order_video_match sub_ovm
                            JOIN order_video_match_preselect_model sub_ovmpm ON sub_ovmpm.match_id = sub_ovm.id
                            JOIN order_video sub_ov ON sub_ov.id = sub_ovm.video_id
                    WHERE
                        sub_ovmpm.model_id = #{modelId}
                        AND sub_ovm.carry_type = ${@<EMAIL>}
                        AND sub_ovmpm.`status` = ${@<EMAIL>}
                        AND sub_ovm.carry_ignore = ${@com.ruoyi.common.core.enums.FlagEnum@NO_FLAG.getcode}
                        AND sub_ov.STATUS IN (
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode},
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                            ${@<EMAIL>}
                                        )
                    GROUP BY
                        sub_ovm.main_carry_video_id
                ) cc ON cc.main_carry_video_id = ovm.video_id
            WHERE
            ovmpm.model_id = #{modelId}
            AND ovm.carry_type = ${@com.ruoyi.common.core.enums.CarryTypeEnum@MAIN_CARRY.getCode}
            AND ovm.schedule_type = ${@com.ruoyi.common.core.enums.ScheduleTypeEnum@CARRY_ORDER.getCode}
            <include refid="mainCarryOrderVideoStatusFilter"/>
            AND ovmpm.`status` = ${@<EMAIL>}
            AND ( IFNULL( cc.carried_count, 0 ) &lt; ovm.main_carry_count OR EXISTS ( SELECT 1 FROM order_video_match WHERE main_carry_video_id = ovm.video_id AND video_id = #{videoId} ) )
        ORDER BY
            ov.create_time DESC
    </select>
    <select id="getMainCarryMatchByMainCarryVideoId" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoMatch">
        <include refid="selectOrderVideoMatchColumn"/>
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                JOIN order_video ov ON ov.id = ovm.video_id
        WHERE
            ovmpm.`status` = ${@<EMAIL>}
            AND ovm.schedule_type = ${@com.ruoyi.common.core.enums.ScheduleTypeEnum@CARRY_ORDER.getCode}
            <include refid="mainCarryOrderVideoStatusFilter"/>
            AND ovm.carry_type = ${@com.ruoyi.common.core.enums.CarryTypeEnum@MAIN_CARRY.getCode}
            AND ovm.video_id = #{mainCarryVideoId}
    </select>
    <select id="getCarriedCountByMainCarryVideoId" resultType="java.lang.Long">
        SELECT
            COUNT( ovm.id ) AS carried_count
        FROM
            order_video_match ovm
                JOIN order_video_match_preselect_model ovmpm on ovmpm.match_id = ovm.id
                JOIN order_video ov ON ov.id = ovm.video_id
        <where>
            ovmpm.`status` = ${@<EMAIL>}
            AND ovm.schedule_type = ${@com.ruoyi.common.core.enums.ScheduleTypeEnum@CARRY_ORDER.getCode}
            AND ovm.carry_type = ${@<EMAIL>}
            AND ovm.carry_ignore = ${@com.ruoyi.common.core.enums.FlagEnum@NO_FLAG.getcode}
            <include refid="carryOrderVideoStatusFilter"/>
            <if test="mainCarryVideoId != null">
                AND ovm.main_carry_video_id = #{mainCarryVideoId}
            </if>

            <if test="ignoreCarryVideoId != null">
                AND ovm.video_id != #{ignoreCarryVideoId}
            </if>
        </where>
    </select>

    <update id="updateOrderVideoMatchFieldNullToNull">
        UPDATE order_video_match
        SET
            video_id = #{orderVideoMatch.videoId},
            rollback_id = #{orderVideoMatch.rollbackId},
            product_pic_change = #{orderVideoMatch.productPicChange},
            goods_info_change = #{orderVideoMatch.goodsInfoChange},
            intention_model_change = #{orderVideoMatch.intentionModelChange},
            pic_count_change = #{orderVideoMatch.picCountChange},
            shoot_required_change = #{orderVideoMatch.shootRequiredChange},
            particular_emphasis_change = #{orderVideoMatch.shootRequiredChange},
            order_specification_require_change = #{orderVideoMatch.shootRequiredChange},
            selling_point_product_change = #{orderVideoMatch.shootRequiredChange},
            cautions_change = #{orderVideoMatch.cautionsChange},
            count = #{orderVideoMatch.count},
            status = #{orderVideoMatch.status},
            pause_reason = #{orderVideoMatch.pauseReason},
            start_time = #{orderVideoMatch.startTime},
            end_time = #{orderVideoMatch.endTime},
            schedule_type = #{orderVideoMatch.scheduleType},
            commission_unit = #{orderVideoMatch.commissionUnit},
            commission = #{orderVideoMatch.commission},
            overstatement = #{orderVideoMatch.overstatement},
            carry_type = #{orderVideoMatch.carryType},
            main_carry_count = #{orderVideoMatch.mainCarryCount},
            main_carry_video_id = #{orderVideoMatch.mainCarryVideoId},
            carry_ignore = #{orderVideoMatch.carryIgnore},
            shipping_remark = #{orderVideoMatch.shippingRemark},
            shipping_pic = #{orderVideoMatch.shippingPic},
            submit_time = #{orderVideoMatch.submitTime},
            shoot_model_id = #{orderVideoMatch.shootModelId},
            shoot_model_type = #{orderVideoMatch.shootModelType},
            shoot_model_platform = #{orderVideoMatch.shootModelPlatform},
            shoot_model_cooperation = #{orderVideoMatch.shootModelCooperation},
            shoot_model_person_id = #{orderVideoMatch.shootModelPersonId},
            shoot_model_person_name = #{orderVideoMatch.shootModelPersonName},
            issue_id = #{orderVideoMatch.issueId},
            shoot_required_original = #{orderVideoMatch.shootRequiredOriginal},
            shoot_required_translation = #{orderVideoMatch.shootRequiredTranslation},
            create_by = #{orderVideoMatch.createBy},
            create_by_id = #{orderVideoMatch.createById},
            create_time = #{orderVideoMatch.createTime},
            update_by = #{orderVideoMatch.updateBy},
            update_by_id = #{orderVideoMatch.updateById},
            update_time = #{orderVideoMatch.updateTime}
        WHERE id = #{orderVideoMatch.id}
    </update>

    <update id="updateShuffledSortKey">
        UPDATE order_video_match
        SET shuffled_sort_key = CRC32(CAST(#{id} AS CHAR))
        WHERE id = #{id};
    </update>

    <update id="refreshModelAllSort">
        UPDATE order_video_match
        SET shuffled_sort_key = CRC32(CONCAT(CAST(id AS CHAR), UNIX_TIMESTAMP()))
        WHERE end_time is NULL and status =1
    </update>

    <select id="getNotCarryIgnoreMatch" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoMatch">
        SELECT
            ovm.*
        FROM
            order_video_match ovm
                JOIN order_video ov ON ov.id = ovm.video_id
                AND ov.rollback_id = ovm.rollback_id
                AND ov.carry_ignore != ovm.carry_ignore
    </select>
    <select id="selectRejectModelIdByVideoId" resultType="java.lang.Long">
        SELECT
            ovmpm.model_id
        FROM
            order_video_match_preselect_model ovmpm
                JOIN order_video_match ovm ON ovm.id = ovmpm.match_id
                AND ovm.video_id = #{videoId}
                AND ovmpm.oust_type = ${@com.ruoyi.common.core.enums.PreselectModelOustTypeEnum@MERCHANT_REJECTION.getCode}
    </select>
    <select id="getSuccessRateOfTheFirstMatch" resultType="java.math.BigDecimal">
        SELECT
            ROUND(
                    (
                        SELECT COUNT(*)
                        FROM (
                                 SELECT ov.id
                                 FROM order_video ov
                                          JOIN (
                                                SELECT *
                                                FROM (
                                                         SELECT *, ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY count DESC) AS rn
                                                         FROM order_video_match
                                                     ) ranked
                                                WHERE rn = 1
                                 ) ovm ON ovm.video_id = ov.id
                                 WHERE ov.rollback_id IS NULL
                                   AND ov.status IN (
                                                     ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                     ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                     ${@<EMAIL>},
                                                     ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}
                                                    )
                                   AND ov.un_finished_time IS NOT NULL
                                   AND NOT EXISTS (
                                                SELECT 1
                                                FROM order_video_match_preselect_model ovmpm
                                                    JOIN order_video_match ovm ON ovm.id = ovmpm.match_id
                                                WHERE ovm.video_id = ov.id AND ovmpm.oust_type = ${@com.ruoyi.common.core.enums.PreselectModelOustTypeEnum@MERCHANT_REJECTION.getcode}
                                            )
                             ) matched_filtered
                    ) /
                    (
                        SELECT COUNT(*)
                        FROM order_video ov
                        WHERE ov.status IN (
                                         ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                         ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                         ${@<EMAIL>},
                                         ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}
                                        )
                          AND ov.un_finished_time IS NOT NULL
                          AND ov.rollback_id IS NULL
                    ),
                    4
            ) AS successRateOfTheFirstMatch
    </select>
    <select id="getModelAfterSalesRate" resultType="java.math.BigDecimal">
        SELECT
            ROUND( IFNULL( a.cnt / b.cnt, 0 ), 4 ) AS modelAfterSalesRate
        FROM
            (
                SELECT
                    COUNT( DISTINCT ov.id ) AS cnt
                FROM
                    order_video ov
                        JOIN order_video_task ot ON ot.video_id = ov.id
                        JOIN order_video_task_detail otd ON otd.task_id = ot.id
                WHERE
                    ov.rollback_id IS NULL
                  AND (
                    otd.after_sale_video_type IN (
                                                  ${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOOT_VIDEO.getcode},
                                                  ${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOT_VIDEO.getcode}
                                                 )
                        OR otd.after_sale_pic_type IN (
                                                       ${@com.ruoyi.common.core.enums.OrderTaskAfterSalePicTypeEnum@RESHOOT_PIC.getcode},
                                                       ${@com.ruoyi.common.core.enums.OrderTaskAfterSalePicTypeEnum@RESHOT_PIC.getcode}
                                                      )
                      )
            ) a,
            (
                SELECT COUNT( 1 ) AS cnt
                FROM order_video
                WHERE rollback_id IS NULL
                  AND STATUS IN (
                                 ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                 ${@<EMAIL>}
                                )
              ) b
    </select>
    <select id="getSuccessRateOfIntentionMatching" resultType="java.math.BigDecimal">
        SELECT
            ROUND(IFNULL(a.cnt / b.cnt, 0), 4) AS ratio
        FROM
            (
                SELECT COUNT(*) AS cnt
                FROM order_video
                WHERE rollback_id IS NULL
                  AND intention_model_id IS NOT NULL
                  AND shoot_model_id IS NOT NULL
                  AND intention_model_id = shoot_model_id
            ) a,
            (
                SELECT COUNT(*) AS cnt
                FROM order_video
                WHERE rollback_id IS NULL
                  AND status IN (
                                 ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                                 ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                 ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                 ${@<EMAIL>},
                                 ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}
                                )
                  AND un_confirm_time IS NOT NULL
            ) b
    </select>
    <select id="getModelOvertimeRate" resultType="java.math.BigDecimal">
        SELECT
            ROUND(
                ( COUNT( DISTINCT ov.id ) ) /
                ( SELECT COUNT(*) FROM order_video WHERE STATUS IN (
                                                                    ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                                    ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                                    ${@<EMAIL>}
                                                                   ) AND rollback_id IS NULL ),
                4
            ) AS modelOvertimeRate
        FROM
        order_video ov
            JOIN (
                SELECT video_id, receipt_time
                FROM (
                         SELECT
                             video_id,
                             receipt_time,
                             ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY shipping_time DESC) AS rn
                         FROM order_video_logistic
                     ) t
                WHERE t.rn = 1
            ) ovl ON ov.id = ovl.video_id
            LEFT JOIN ( SELECT video_id, MIN( create_time ) AS earliest_feedback_time FROM order_video_feed_back GROUP BY video_id ) ovf ON ov.id = ovf.video_id
        WHERE
            ov.STATUS IN (
                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                            ${@<EMAIL>}
                        )
            AND ov.rollback_id IS NULL
            AND (
                ( ovf.earliest_feedback_time IS NULL AND TIMESTAMPDIFF( day, ovl.receipt_time, NOW() ) &gt;= 20 )
                OR ( ovl.receipt_time &lt; ovf.earliest_feedback_time AND TIMESTAMPDIFF( DAY, ovl.receipt_time, ovf.earliest_feedback_time ) &gt;= 20 )
            )
    </select>
    <select id="getAverageMatchingDuration" resultType="java.math.BigDecimal">
        WITH
            sessions AS (
                SELECT
                    video_id,
                    submit_time,
                    LAG(submit_time) OVER ( PARTITION BY video_id ORDER BY submit_time ) AS prev_submit_time
                FROM order_video_match
                WHERE submit_time IS NOT NULL
        ),

            session_starts AS (
                SELECT
                    s.video_id,
                    s.submit_time,
                    MIN(t.start_time) AS session_start
                FROM sessions AS s
                    JOIN order_video_match AS t
                        ON t.video_id = s.video_id
                        AND t.start_time &lt;= s.submit_time
                        AND (s.prev_submit_time IS NULL OR t.start_time > s.prev_submit_time)
                GROUP BY
                    s.video_id,
                    s.submit_time
        )

        SELECT
            ROUND(
                (
                        SUM(
                            TIMESTAMPDIFF(
                                SECOND,
                                ss.session_start,
                                ss.submit_time
                        )
                    )
                    / 86400.0
                )
                /
                (
                SELECT COUNT(*)
                FROM order_video ov
                WHERE ov.STATUS IN (
                                    ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                                    ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                    ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                    ${@<EMAIL>},
                                    ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}
                                   )
                AND ov.rollback_id IS NULL
                AND ov.un_confirm_time IS NOT NULL
                AND ov.last_model_submit_time IS NOT NULL
                <if test="date != null and date != '' ">
                    AND DATE_FORMAT( ov.create_time, '%Y-%m' ) = #{date}
                </if>
                )
            , 4) AS averageMatchingDuration
        FROM session_starts ss
            JOIN order_video ov ON ss.video_id = ov.id
        WHERE
            ov.STATUS IN (
                              ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                              ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                              ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                              ${@<EMAIL>},
                              ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}
                    )
            AND ov.rollback_id IS NULL
            AND ov.un_confirm_time IS NOT NULL
            AND ov.last_model_submit_time IS NOT NULL
        <if test="date != null and date != '' ">
            AND DATE_FORMAT( ov.create_time, '%Y-%m' ) = #{date}
        </if>
    </select>
    <select id="getAverageFeedbackDuration" resultType="java.math.BigDecimal">
        SELECT
            ROUND(SUM(TIMESTAMPDIFF(SECOND, un_finished_time, need_confirm_time))
                      / COUNT(*) / 86400, 4) AS average_days
        FROM order_video
        WHERE status IN (
                         ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                         ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                         ${@<EMAIL>}
                        )
          AND need_confirm_time IS NOT NULL
          AND un_finished_time IS NOT NULL
          AND rollback_id IS NULL
    </select>
    <select id="getModelOrderCommissionAnalysis"
            resultType="com.ruoyi.system.api.domain.bo.biz.datastatistics.ModelOrderCommissionAnalysisBO">
        SELECT
            ov.id,
            ovm.commission_unit,
            ovm.commission,
            IFNULL( ovm.shoot_model_type, ovmpm.model_type ) AS shoot_model_type,
            IFNULL( ovm.shoot_model_cooperation, ovmpm.model_cooperation ) AS shoot_model_cooperation
        FROM
            order_video ov
                JOIN (
                SELECT
                    *
                FROM
                    ( SELECT *, ROW_NUMBER() OVER ( PARTITION BY video_id ORDER BY count DESC ) AS rn FROM order_video_match ) ranked
                WHERE
                    rn = 1
            ) ovm ON ovm.video_id = ov.id
                AND IFNULL( ovm.rollback_id, 0 )= IFNULL( ov.rollback_id, 0 )
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                AND ovmpm.`status` = ${@<EMAIL>}
        WHERE
            ov.`status` IN (
                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                            ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                            ${@<EMAIL>}
                           )
          AND ovm.schedule_type = ${@<EMAIL>}

          <if test="dto.modelCooperation != null">
              AND IFNULL( ovm.shoot_model_cooperation, ovmpm.model_cooperation ) = #{dto.modelCooperation}
          </if>

          <if test="dto.beginTime != null and dto.endTime != null">
              AND ov.create_time BETWEEN #{dto.beginTime} AND #{dto.endTime}
          </if>
    </select>
    <select id="getModelSuccessMatchCount"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.ModelSuccessMatchCountVO">
        WITH latest_match AS (
            -- 步骤1：取每个 video_id 最新的一条 match 记录
            SELECT ovm.*
            FROM order_video_match ovm
            WHERE ovm.rollback_id IS NULL
              AND ovm.submit_time IS NOT NULL
              AND ovm.start_time = (
                SELECT MAX(ovm2.start_time)
                FROM order_video_match ovm2
                WHERE ovm2.video_id = ovm.video_id
                  AND ovm2.rollback_id IS NULL
                  AND ovm2.submit_time IS NOT NULL
            )
        ),

             valid_match AS (
                 -- 步骤2：在 latest_match 基础上，过滤 status=2 的存在性  排除 oust_type=1
                 SELECT lm.*
                 FROM latest_match lm

                 -- 必须至少有一条 status = 2
                 WHERE EXISTS (
                     SELECT 1
                     FROM order_video_match_preselect_model ovmpm
                     WHERE ovmpm.match_id = lm.id
                       AND ovmpm.status = ${@<EMAIL>}
                 )
             )

        -- 步骤3：统计总数  按 add_type 分类计数
        SELECT
            COUNT(*) AS successMatchCount,
            IFNULL ( SUM(CASE WHEN ovmpm.add_type = ${@com.ruoyi.common.core.enums.PreselectModelAddTypeEnum@INTENTION_MODEL.getcode} THEN 1 ELSE 0 END) , 0 ) AS merchantIntentionCount,
            IFNULL ( SUM(CASE WHEN ovmpm.add_type = ${@com.ruoyi.common.core.enums.PreselectModelAddTypeEnum@MODEL_OPTIONAL.getcode} THEN 1 ELSE 0 END) , 0 ) AS modelCustomerCount,
            IFNULL ( SUM(CASE WHEN ovmpm.add_type = ${@<EMAIL>} THEN 1 ELSE 0 END) , 0 ) AS serviceCustomerCount,
            IFNULL ( SUM(CASE WHEN ovmpm.add_type = ${@<EMAIL>} THEN 1 ELSE 0 END) , 0 ) AS serviceDistributionCount
        FROM valid_match vm
                 JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = vm.id AND ovmpm.status = ${@<EMAIL>}
                 JOIN order_video ov ON ov.id = vm.video_id AND ov.STATUS IN (
                                                                              ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                                              ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                                              ${@<EMAIL>},
                                                                              ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}
                                                                             ) AND ov.un_confirm_time IS NOT NULL
    </select>
    <select id="selectLastListBySubmitTime"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoMatch">
        SELECT
            ovm.commission_unit,
            ovm.commission,
            ovm.shoot_model_type,
            ov.issue_id
        FROM
            order_video_match ovm
            JOIN order_video ov ON ov.id = ovm.video_id
                                       AND ovm.submit_time = ov.last_model_submit_time
                                       AND ovm.schedule_type = ${@<EMAIL>}
                                       AND ov.issue_id IS NOT NULL
                                        <if test="beginDate != '' and endDate != '' ">
                                            AND ovm.submit_time BETWEEN DATE_FORMAT(#{beginDate}, '%Y-%m-%d 00:00:00') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d 23:59:59')
                                        </if>
    </select>
    <select id="selectListBySubmitTime" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoMatch">
        SELECT
            ovm.commission_unit,
            ovm.commission,
            ovm.shoot_model_type,
            ovm.shoot_model_person_id
        FROM
            order_video_match ovm
        WHERE
            ovm.schedule_type = ${@<EMAIL>}
            AND ovm.shoot_model_person_id IS NOT NULL
            AND ovm.submit_time IS NOT NULL
            <if test="beginDate != '' and endDate != '' ">
                AND ovm.submit_time BETWEEN DATE_FORMAT(#{beginDate}, '%Y-%m-%d 00:00:00') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d 23:59:59')
            </if>
    </select>
    <select id="selectModelDataTableListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO">
        WITH
        base_ovmpm AS (
        SELECT
        ovmpm.id,
        ovmpm.match_id,
        ovm.video_id,
        ovmpm.add_type,
        ovmpm.add_time,
        ovmpm.model_id,
        ovmpm.oust_type,
        ovmpm.oust_time,
        ovm.shoot_model_add_type,
        ovm.submit_time,
        ovm.rollback_id,
        ovm.shoot_model_id
        FROM order_video_match_preselect_model ovmpm
        JOIN order_video_match ovm
        ON ovm.id = ovmpm.match_id
        WHERE ovm.both_not_null = 1
        )


        SELECT
        b.model_id AS id,

        COUNT(DISTINCT CASE WHEN IFNULL(b.shoot_model_add_type,b.add_type) = 1
        AND b.shoot_model_id = b.model_id AND 1=1
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.match_id END
        )                                                   AS submitIntentionCount,
        COUNT(DISTINCT CASE WHEN b.add_type = 1 AND 1=1
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.add_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.id END)     AS intentionCount,
        ROUND(
        COALESCE(
        COUNT(DISTINCT CASE WHEN IFNULL(b.shoot_model_add_type,b.add_type) = 1
        AND b.shoot_model_id = b.model_id
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.match_id END)
        / NULLIF(COUNT(DISTINCT CASE WHEN b.add_type = 1
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.add_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.id END),0)
        ,0)
        ,4
        )                                                        AS intentionOrderRate,

        COUNT(DISTINCT CASE WHEN IFNULL(b.shoot_model_add_type,b.add_type) = 3
        AND b.shoot_model_id = b.model_id AND 1=1
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.match_id END)                     AS submitPreSelectCount,
        COUNT(DISTINCT CASE WHEN b.add_type = 3 AND 1=1
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.add_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.id END)     AS preSelectCount,
        ROUND(
        COALESCE(
        COUNT(DISTINCT CASE WHEN IFNULL(b.shoot_model_add_type,b.add_type) = 3
        AND b.shoot_model_id = b.model_id
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.match_id END)
        / NULLIF(COUNT(DISTINCT CASE WHEN b.add_type = 3
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.add_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.id END),0)
        ,0)
        ,4
        )                                                        AS preSelectOrderRate,

        COUNT(DISTINCT CASE WHEN IFNULL(b.shoot_model_add_type,b.add_type) = 4
        AND b.shoot_model_id = b.model_id AND 1=1
        THEN b.match_id END)                     AS submitDispatchCount,
        COUNT(DISTINCT CASE WHEN b.add_type = 4 AND 1=1 THEN b.id END)     AS dispatchCount,
        ROUND(
        COALESCE(
        COUNT(DISTINCT CASE WHEN IFNULL(b.shoot_model_add_type,b.add_type) = 4
        AND b.shoot_model_id = b.model_id THEN b.match_id END)
        / NULLIF(COUNT(DISTINCT CASE WHEN b.add_type = 4 THEN b.id END),0)
        ,0)
        ,4
        )                                                        AS dispatchOrderRate,

        COUNT(DISTINCT CASE WHEN IFNULL(b.shoot_model_add_type,b.add_type) = 2 AND b.shoot_model_id = b.model_id AND 1=1
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.match_id END) AS submitSelfSelectCount,
        COUNT(DISTINCT CASE WHEN b.add_type = 2 AND 1=1
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.add_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.id       END) AS selfSelectCount,
        ROUND(
        COALESCE(
        COUNT(DISTINCT CASE WHEN IFNULL(b.shoot_model_add_type,b.add_type) = 2 AND b.shoot_model_id = b.model_id
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.match_id END)
        / NULLIF(COUNT(DISTINCT CASE WHEN b.add_type = 2
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.add_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.id END),0)
        ,0)
        ,4
        )                                                        AS selfSelectOrderRate,

        COUNT(DISTINCT CASE WHEN b.oust_type = 1 AND 1=1
        AND IFNULL(b.shoot_model_add_type,b.add_type) != 1 AND b.shoot_model_id = b.model_id
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.oust_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.match_id END)                             AS changeModelCount,
        COUNT(DISTINCT CASE WHEN IFNULL(b.shoot_model_add_type,b.add_type) != 1 AND b.shoot_model_id = b.model_id AND 1=1
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.match_id END) AS submitModelCount,
        ROUND(
        COALESCE(
        COUNT(DISTINCT CASE WHEN b.oust_type = 1
        AND IFNULL(b.shoot_model_add_type,b.add_type)
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.oust_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.id END)
        / NULLIF(COUNT(DISTINCT CASE WHEN IFNULL(b.shoot_model_add_type,b.add_type) != 1 AND b.shoot_model_id = b.model_id
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.match_id END),0)
        ,0)
        ,4
        )                                                        AS rejectOrderRate,

        COUNT(DISTINCT CASE WHEN (b.oust_type IS NULL OR ( b.oust_type = 6 and ovrr.status != 5) )AND b.model_id=b.shoot_model_id
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.match_id END)
        AS orderScheduledCount,

        COUNT(DISTINCT CASE
        WHEN b.shoot_model_id = ov.shoot_model_id
        AND b.shoot_model_id = b.model_id
        AND b.rollback_id    &lt;=> ov.rollback_id
        AND ((ov.status =6 AND
        (hm.has_material =0 or hm.has_material is null))OR (ov.STATUS IN ( 6, 7 ) and (ovt.has_after is not null or ovt.has_after =1))OR ov.status =5)
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN ov.id
        END)                                                     AS waitPictureCount,

        COUNT(
        DISTINCT CASE WHEN (b.oust_type is null or ( b.oust_type = 6 and ovrr.status != 5))AND b.model_id=b.shoot_model_id and ff.cnt_feedback > 0
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>THEN ov.id END
        )                                                     AS feedbackCount,

        COUNT(DISTINCT CASE
        WHEN b.shoot_model_id = ov.shoot_model_id
        AND b.shoot_model_id = b.model_id
        AND b.rollback_id    &lt;=> ov.rollback_id
        AND ov.status IN (6,7,8,9)
        AND 1=1
        AND lg.rn=1
        AND ((lg.is_overtime = 1
        AND ff.first_feedback_time is null )OR(lg.receipt_time &lt; ff.first_feedback_time and TIMESTAMPDIFF(DAY,lg.receipt_time,ff.first_feedback_time)>=20))
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN ov.id
        END)                                                     AS overtimeCount,

        COUNT(DISTINCT CASE
        WHEN b.shoot_model_id = ov.shoot_model_id
        AND b.rollback_id    &lt;=> ov.rollback_id
        AND ov.status IN (6,7,8,9)
        AND 1=1
        AND lg.receipt_time is not null
        AND lg.rn=1
        AND b.shoot_model_id = b.model_id
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN ov.id
        END)                                                     AS confirmReceiptCount,

        ROUND(
        COALESCE(
        count(DISTINCT CASE WHEN b.shoot_model_id = ov.shoot_model_id
        AND b.shoot_model_id = b.model_id
        AND b.rollback_id    &lt;=> ov.rollback_id
        AND ov.status IN (6,7,8,9)
        AND lg.rn=1
        AND ((lg.is_overtime = 1
        AND ff.first_feedback_time is null )OR(lg.receipt_time &lt; ff.first_feedback_time and TIMESTAMPDIFF(DAY,lg.receipt_time,ff.first_feedback_time)>=20))
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN ov.id END)
        / NULLIF(count(DISTINCT CASE WHEN b.shoot_model_id = ov.shoot_model_id
        AND b.rollback_id    &lt;=> ov.rollback_id
        AND ov.status IN (6,7,8,9)
        AND lg.receipt_time is not null
        AND lg.rn=1
        AND b.shoot_model_id = b.model_id
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN ov.id END),0)
        ,0)
        ,4
        )                                                        AS overtimeRate,

        COUNT(DISTINCT CASE
        WHEN ovf.target_status = 8
        AND b.shoot_model_id = ov.shoot_model_id
        AND b.shoot_model_id = b.model_id
        AND b.rollback_id    &lt;=> ov.rollback_id
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN ov.id
        END)                                                     AS completeCount,


        COUNT(DISTINCT CASE
        WHEN ovlf.model_result = 3
        AND b.shoot_model_id = ov.shoot_model_id
        AND b.shoot_model_id = b.model_id
        AND b.rollback_id    &lt;=> ov.rollback_id
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN ov.id
        END)                                                     AS dropCount,
        COUNT(DISTINCT CASE
        WHEN ov.status       = 9
        AND ovr.status IN (6,7,8)
        AND ovr.refund_type   = 2
        AND ovr.refund_status = 4
        AND ovr.apply_time >= b.submit_time
        AND ( ovr.apply_time &lt; ovrr.operate_time OR ovrr.operate_time IS NULL )
        AND b.shoot_model_id = b.model_id
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN ov.id
        END)                                                     AS cancelCount,

        COUNT(DISTINCT CASE
        WHEN b.shoot_model_id = ovrr.shoot_model_id
        AND b.shoot_model_id = b.model_id
        AND ovrr.status IN (6,7,8)
        <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
            AND b.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
        </if>
        THEN b.match_id
        END)                                                     AS returnCount

        FROM base_ovmpm b
        JOIN order_video ov
        ON ov.id = b.video_id

        LEFT JOIN LATERAL (
        SELECT
        ovl.id,
        ovl.receipt_time,
        TIMESTAMPDIFF(DAY, ovl.receipt_time, NOW()) >= 20 AS is_overtime,

        ROW_NUMBER() OVER (
        PARTITION BY ovl.video_id, ovmsa.rollback_id
        ORDER BY ovl.shipping_time DESC
        ) AS rn
        FROM order_video_logistic ovl
        JOIN order_video_model_shipping_address ovmsa
        ON ovl.shipping_address_id = ovmsa.id
        WHERE ovl.video_id   = ov.id
        AND ovmsa.rollback_id &lt;=> ov.rollback_id
        ) lg ON TRUE

        LEFT JOIN order_video_logistic_follow ovlf
        ON ovlf.order_video_logistic_id =lg.id

        LEFT JOIN LATERAL (
        SELECT
        MIN(fb.create_time) AS first_feedback_time,
        COUNT(*)            AS cnt_feedback
        FROM order_video_feed_back fb
        WHERE fb.video_id   = b.video_id
        AND fb.rollback_id &lt;=> b.rollback_id
        ) ff ON TRUE

        LEFT JOIN LATERAL (
        SELECT 1 AS has_material
        FROM order_video_feed_back_material mat
        WHERE mat.video_id   = ov.id
        AND mat.rollback_id &lt;=> ov.rollback_id
        LIMIT 1
        ) hm ON TRUE

        LEFT JOIN order_video_flow ovf
        ON ovf.video_id     = ov.id
        AND ovf.target_status = 8


        LEFT JOIN order_video_refund ovr
        ON ovr.video_id = ov.id

        LEFT JOIN (
        SELECT
        rc.rollback_id,
        r.video_id,
        rc.status,
        rc.shoot_model_id,
        r.operate_time,
        rc.last_model_submit_time
        FROM order_video_rollback_record r
        JOIN order_video_rollback_record_change rc
        ON rc.rollback_id = r.id
        AND rc.last_model_submit_time IS NOT NULL
        ) ovrr
        ON ovrr.video_id    = b.video_id
        AND ovrr.last_model_submit_time = b.submit_time

        LEFT JOIN LATERAL (
        SELECT 1 AS has_after
        FROM order_video_task ovt
        JOIN order_video_task_detail ovtd on ovtd.task_id=ovt.id
        WHERE ovt.video_id   = ov.id
        and ovt.task_type =1 and ovtd.after_sale_video_type in (1,2) and ovtd.status not in (4,5,6)
        LIMIT 1
        ) ovt ON TRUE

        GROUP BY b.model_id
    </select>
    <select id="selectModelDataTableAfterSaleRate"
            resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO">
        SELECT
            ovm.shoot_model_id AS id,
            COUNT(DISTINCT case when (
                    ovtd.submit_time >= ovm.submit_time
                    AND ( ovtd.submit_time &lt; ovrr.operate_time OR ovrr.operate_time IS NULL )
                    OR ( ovr.apply_time >= ovm.submit_time AND ( ovr.apply_time &lt; ovrr.operate_time OR ovrr.operate_time IS NULL ) )
                    OR ovrr.operate_time IS NOT NULL) and (ovmpm.oust_type is null or ( ovmpm.oust_type = 6 and ovrr.status != 5))
            <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
                AND ovm.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
            </if> then ovm.id end)AS afterSaleCount,
            COUNT(DISTINCT CASE WHEN (ovmpm.oust_type is null or ( ovmpm.oust_type = 6 and ovrr.status != 5))
            <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
                AND ovfb.create_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
            </if>THEN ovfb.video_id END)AS feedbackMaterialsCount,
            ROUND(IFNULL(COUNT(DISTINCT case when (
        ovtd.submit_time >= ovm.submit_time
        AND ( ovtd.submit_time &lt; ovrr.operate_time OR ovrr.operate_time IS NULL )
        OR ( ovr.apply_time >= ovm.submit_time AND ( ovr.apply_time &lt; ovrr.operate_time OR ovrr.operate_time IS NULL ) )
        OR ovrr.operate_time IS NOT NULL)and (ovmpm.oust_type is null or ( ovmpm.oust_type = 6 and ovrr.status != 5))
                    <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
                        AND ovm.submit_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
                    </if> then ovm.id end)
                             /COUNT(DISTINCT CASE WHEN (ovmpm.oust_type is null or ( ovmpm.oust_type = 6 and ovrr.status != 5))
                            <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
                                AND ovfb.create_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
                            </if>THEN ovfb.video_id END),0),4)as afterSaleRate
        FROM
            order_video_match ovm
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                LEFT JOIN order_video_task ovt on ovt.video_id=ovm.video_id and ovt.task_type=1
                LEFT JOIN order_video_task_detail ovtd on ovtd.task_id=ovt.id
                LEFT JOIN order_video_refund ovr on ovr.video_id=ovm.video_id
                LEFT JOIN order_video_feed_back ovfb on ovfb.video_id = ovm.video_id AND ovfb.rollback_id &lt;=> ovm.rollback_id
                LEFT JOIN (
                    SELECT
                        ovrr.id,
                        ovrrc.id AS cid,
                        ovrr.video_id,
                        ovrr.operate_time,
                        ovrrc.last_model_submit_time,
                        ovrrc.status
                    FROM
                        order_video_rollback_record ovrr
                            JOIN order_video_rollback_record_change ovrrc ON ovrrc.rollback_id = ovrr.id
                ) ovrr ON ovrr.video_id = ovm.video_id AND ovrr.last_model_submit_time = ovm.submit_time
        WHERE
            ovm.both_not_null=1
        GROUP BY
            ovm.shoot_model_id
    </select>
    <select id="selectHistoryMatchVOListByVideoId"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoMatchVO">
        SELECT
            ovm.id,
            ovm.video_id,
            ovm.rollback_id,
            ovm.count,
            ovm.status,
            ovm.start_time,
            ovm.end_time,
            CASE WHEN EXISTS (
                SELECT 1
                FROM order_video_match_preselect_model ovmpm
                WHERE ovmpm.match_id = prevovm.id
                    AND ovmpm.oust_type = 1
            ) THEN 1 ELSE 0 END AS isRejectAfterSubmitModel
        FROM
            order_video_match ovm
            LEFT JOIN LATERAL (
                SELECT
                    p.id
                FROM
                    order_video_match AS p
                WHERE
                    p.video_id = ovm.video_id
                    AND p.rollback_id &lt;=> ovm.rollback_id
                    AND p.count &lt; ovm.count
                      AND p.status = 1
                ORDER BY p.count DESC
                LIMIT 1
            ) AS prevovm ON TRUE
        WHERE ovm.video_id = #{videoId}
        ORDER BY ovm.start_time DESC
    </select>
    <select id="selectMyPreselectFailListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.MyPreselectDockingListVO">
        SELECT
            ovm.id,
            ovm.video_id,
            ovm.product_pic_change,
            ovm.goods_info_change,
            ovm.intention_model_change,
            ovm.pic_count_change,
            ovm.shoot_required_change,
            ovm.particular_emphasis_change,
            ovm.order_specification_require_change,
            ovm.selling_point_product_change,
            ovm.cautions_change,
            ov.product_pic,
            ov.is_care,
            ov.is_gund,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.platform,
            ov.shooting_country,
            ov.model_type,
            ov.video_format,
            ov.STATUS,
            ov.pic_count,
            ov.refund_pic_count,
            ov.reference_pic AS reference_pic_id,
            ov.contact_id,
            ovm.count,
            COUNT( ovmpm_count.id ) AS preselect_model_count,
            ovm.start_time
        FROM
            order_video_match ovm
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                JOIN order_video ov ON ov.id = ovm.video_id
                LEFT JOIN order_video_match_preselect_model ovmpm_count ON ovmpm_count.match_id = ovm.id
                    AND ovmpm_count.STATUS != ${@<EMAIL>}
                    AND ovmpm_count.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
                    AND (
                        ovmpm_count.add_type != ${@<EMAIL>}
                        OR ovmpm_count.model_intention = ${@<EMAIL>}
                    )
        <where>
            ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
            AND ovmpm.`status` = ${@<EMAIL>}
            AND ovmpm.oust_type != ${@com.ruoyi.common.core.enums.PreselectModelOustTypeEnum@CUSTOMER_SERVICE_ELIMINATION.getCode}
            AND (
                ovmpm.add_type != ${@<EMAIL>}
                OR ovmpm.distribution_result = ${@<EMAIL>}
            )
            AND ovmpm.confirm_oust_time IS NULL
        <if test="dto.backUserRelevanceModelIds != null and dto.backUserRelevanceModelIds.size() > 0 and dto.currentUserIsAdmin == false">
                AND ovmpm.model_id IN
                <foreach collection="dto.backUserRelevanceModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        <if test="dto.keyword != null and dto.keyword != ''">
            AND (
                LOWER(ov.video_code) LIKE LOWER(CONCAT('%', #{dto.keyword}, '%'))
                OR LOWER(ov.product_chinese) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.product_english) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
                OR LOWER(ov.product_link) LIKE LOWER(CONCAT( '%',#{dto.keyword},'%' ))
            )
        </if>

        <if test="dto.oustTimes != null and dto.oustTimes.size() > 0">
            AND (
            <foreach item="value" index="key" collection="dto.oustTimeMap.entrySet()" separator=" OR " >
                DATE_FORMAT(ovmpm.oust_time, '%Y-%m-%d %H:%i:%s') BETWEEN #{key} AND #{value}
            </foreach>
            )
        </if>

        <if test="dto.isCares != null and dto.isCares.size() > 0 ">
            AND ov.is_care IN
            <foreach item="item" index="index" collection="dto.isCares" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="dto.addTypes != null and dto.addTypes.size() > 0 ">
            AND ovmpm.add_type IN
            <foreach item="item" index="index" collection="dto.addTypes" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="dto.modelIntentions != null and dto.modelIntentions.size() > 0 ">
            AND ovmpm.model_intention IN
            <foreach item="item" index="index" collection="dto.modelIntentions" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="dto.oustModelIds != null and dto.oustModelIds.size() > 0 ">
            AND ovmpm.model_id IN
            <foreach item="item" index="index" collection="dto.oustModelIds" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        </where>
        GROUP BY
            ovm.id
        HAVING
            COUNT(ovmpm.id) > 0
    </select>
    <select id="getOrderTheSameProducts"
            resultType="com.ruoyi.system.api.domain.vo.order.MyPreselectDockingListVO">
        WITH ov_match AS (
            SELECT
            ovm.video_id,
            ovm.id AS match_id,
            ovm.rollback_id
            FROM order_video_match ovm
        ),
        valid_model AS (
            SELECT
            ovmpm.match_id
            FROM order_video_match_preselect_model ovmpm
            WHERE ovmpm.status = ${@<EMAIL>}
            GROUP BY ovmpm.match_id
        ),
        video_match_status AS (
        SELECT
            ov.id AS video_id,
            ov.product_link,
            ov.shoot_model_id,
            ov.STATUS,
            vm.match_id,
            vm_valid.match_id AS has_valid_model
            FROM order_video ov
                LEFT JOIN ov_match vm ON vm.video_id = ov.id AND vm.rollback_id &lt;=> ov.rollback_id
                LEFT JOIN valid_model vm_valid ON vm_valid.match_id = vm.match_id
            WHERE
                ov.product_link IS NOT NULL
                AND ov.product_link != ''
                AND ov.product_link IN
                <foreach item="item" index="index" collection="productLinks" separator="," close=")" open="(">
                    #{item}
                </foreach>
        )
        SELECT
            product_link,
            COUNT(DISTINCT CASE WHEN shoot_model_id IS NULL AND status != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode} AND ( match_id IS NULL OR has_valid_model IS NULL ) THEN video_id END) AS unMatchSameProductLinkOrderCount,
            COUNT(DISTINCT video_id) AS sameProductLinkOrderCount
        FROM
            video_match_status
        GROUP BY
            product_link
    </select>
    <select id="selectMyPreselectFailListRecommendList"
            resultType="com.ruoyi.system.api.domain.vo.order.MyPreselectFailListRecommendListVO">
        WITH
            oust_exclude AS (
                SELECT DISTINCT
                ovm.video_id,
                ovm.rollback_id,
                ovmpm.status,
                ovmpm.model_id
            FROM order_video_match ovm
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
            ),
        <if test="(orderTagIds != null and orderTagIds.size() > 0) or (modelTagIds != null and modelTagIds.size() > 0) ">
            tag_stats AS (
                SELECT
                video_id
                <if test="orderTagIds != null and orderTagIds.size() > 0">
                    ,MAX(tag_id IN
                    <foreach item="item" index="index" collection="orderTagIds" separator="," close=")" open="(">
                        #{item}
                    </foreach>
                    ) AS hasOrderTag
                </if>
                <if test="modelTagIds != null and modelTagIds.size() > 0">
                    ,MAX(tag_id IN
                    <foreach item="item" index="index" collection="modelTagIds" separator="," close=")" open="(">
                        #{item}
                    </foreach>
                    ) AS hasModelTag
                </if>
                FROM order_video_tag
                <where>
                    <if test="orderTagIds != null and orderTagIds.size() > 0">
                        tag_id IN
                        <foreach item="item" index="index" collection="orderTagIds" separator="," close=")" open="(">
                            #{item}
                        </foreach>
                    </if>
                    <if test="modelTagIds != null and modelTagIds.size() > 0">
                        OR tag_id IN
                        <foreach item="item" index="index" collection="modelTagIds" separator="," close=")" open="(">
                            #{item}
                        </foreach>
                    </if>
                </where>
                GROUP BY video_id
            ),
        </if>
            preselect_counts AS (
                SELECT
                    match_id,
                    COUNT(*) AS preselect_model_count
                FROM order_video_match_preselect_model
                    WHERE status != ${@<EMAIL>}
                    AND select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
                    AND (add_type != ${@<EMAIL>} OR model_intention = ${@<EMAIL>})
                GROUP BY match_id
            ),
            remote_exclude AS (
                SELECT DISTINCT
                    sub_ov.id,
                    sub_ov.product_link
                FROM order_video sub_ov
                    JOIN order_video_match ovm ON ovm.video_id = sub_ov.id
                    JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                WHERE ovmpm.oust_type = ${@com.ruoyi.common.core.enums.PreselectModelOustTypeEnum@MERCHANT_REJECTION.getCode}
            )
        SELECT
            ov.id AS videoId,
            ovm.id AS matchId,
            ov.product_pic,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.model_type,
            ov.platform,
            ov.shooting_country,
            ov.is_care,
            ov.is_gund,
            <if test="productLink != null and productLink != '' ">
                CASE WHEN ov.product_link = #{productLink} THEN TRUE ELSE FALSE END AS isSameProductLink,
            </if>
            ov.pic_count,
            ov.refund_pic_count,
            ov.reference_pic AS reference_pic_id,
            ov.intention_model_id,
            ov.cautions_pic AS cautions_pic_id,
            COALESCE(pc.preselect_model_count, 0) AS preselect_model_count
            <if test="orderTagIds != null and orderTagIds.size() > 0">
                ,COALESCE(MAX(ts.hasOrderTag), 0) AS hasOrderTag
            </if>
            <if test="modelTagIds != null and modelTagIds.size() > 0">
                ,COALESCE(MAX(ts.hasModelTag), 0) AS hasModelTag
            </if>
        FROM
            order_video ov
                JOIN order_video_match ovm ON ovm.video_id = ov.id AND ovm.rollback_id &lt;=> ov.rollback_id AND ovm.end_time IS NULL
                LEFT JOIN preselect_counts pc ON pc.match_id = ovm.id
        <if test="(orderTagIds != null and orderTagIds.size() > 0) or (modelTagIds != null and modelTagIds.size() > 0) ">
                LEFT JOIN tag_stats ts ON ts.video_id = ov.id
        </if>
        WHERE
            ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode}
            AND FIND_IN_SET( ov.platform, #{platform} )
            AND ov.shooting_country = #{nation}
            AND ov.model_type = #{modelType}

            AND NOT EXISTS (
                SELECT 1
                FROM oust_exclude oe
                WHERE oe.video_id = ov.id
                    AND ((oe.rollback_id &lt;=> ov.rollback_id AND oe.status = ${@<EMAIL>} ) OR oe.model_id = #{modelId})
            )
            AND NOT EXISTS (
                SELECT 1
                FROM remote_exclude re
                WHERE re.product_link = ov.product_link
                    AND re.id != ov.id
            )
            <if test="haveBlockedModelUserIds != null and haveBlockedModelUserIds.size() > 0 ">
                AND ov.create_order_biz_user_id NOT IN
                <foreach item="item" index="index" collection="haveBlockedModelUserIds" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            AND (
                <if test="productLink != null and productLink != ''">
                    ov.product_link = #{productLink} OR
                </if>
                <if test="createOrderBusinessId != null">
                 (
                     ov.create_order_business_id = #{createOrderBusinessId}
                        AND NOT EXISTS (
                        SELECT
                            1
                        FROM
                            order_video_match ovm
                            JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                        WHERE
                            ovm.video_id = ov.id
                            AND ovmpm.oust_type IS NOT NULL
                            AND ovmpm.oust_type != ${@com.ruoyi.common.core.enums.PreselectModelOustTypeEnum@NOT_SELECTED.getCode}
                            AND ovmpm.model_id = #{modelId}
                    )
                ) OR
                </if>
                (ov.is_gund = 0 AND ov.status_time &lt; NOW() - INTERVAL 3 DAY)
                OR ov.status_time &lt; NOW() - INTERVAL 5 DAY
            )
            <if test="videoIds != null and videoIds.size() > 0 ">
                AND ov.id IN
                <foreach item="item" index="index" collection="videoIds" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
        GROUP BY
            ov.id,
            ovm.id
        ORDER BY
        <if test="productLink != null and productLink != '' ">
            ov.product_link != #{productLink},
        </if>
        <if test="createOrderBusinessId != null">
            ov.create_order_business_id != #{createOrderBusinessId},
        </if>
        <if test="orderTagIds != null and orderTagIds.size() > 0">
            MAX(ts.hasOrderTag) = 0,
        </if>
        <if test="modelTagIds != null and modelTagIds.size() > 0">
            MAX(ts.hasModelTag) = 0,
        </if>
            preselect_model_count ASC,
            ov.status_time ASC,
            ov.id DESC
    </select>
    <select id="selectLastOrderVideoMatchByVideoIds"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoMatch">
        SELECT
            *
        FROM
            ( SELECT id,video_id,status, ROW_NUMBER() OVER ( PARTITION BY video_id ORDER BY start_time DESC ) AS rn FROM order_video_match ) t
        WHERE
            t.rn = 1
            AND video_id IN
            <foreach item="item" index="index" collection="videoIds" separator="," close=")" open="(">
                #{item}
            </foreach>
    </select>
    <select id="selectOrderTheSameProductList"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderTheSameProductListVO">
        SELECT
            ov.id AS video_id,
            ovm.id AS match_id,
            ov.video_code,
            ov.`status`,
            ov.shooting_country,
            ov.model_type,
            ov.platform,
            COUNT( ovmpm.id ) AS preselect_model_count,
            MAX( CASE WHEN ovmpm.`status` = ${@<EMAIL>} THEN TRUE ELSE FALSE END ) AS hasSelectedModel,
            ov.shoot_model_id IS NOT NULL AS hasShootModel
        FROM
            order_video ov
            LEFT JOIN (
                SELECT *
                FROM (
                         SELECT id,video_id,rollback_id,
                                ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY start_time DESC) AS rn
                         FROM order_video_match
                         WHERE status = ${@<EMAIL>}
                     ) t
                WHERE t.rn = 1
            )ovm ON ovm.video_id = ov.id AND ovm.rollback_id &lt;=> ov.rollback_id
            LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                AND ovmpm.STATUS != ${@<EMAIL>}
                AND ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
                AND ( ovmpm.add_type != ${@<EMAIL>} OR ovmpm.model_intention = ${@<EMAIL>} )
        WHERE
            ov.product_link = #{productLink}
        GROUP BY
            ov.id,
            ovm.id
    </select>
    <select id="selectSameProductModelIdsByProductLink"
            resultType="com.ruoyi.system.api.domain.vo.order.AddPreselectRemoteVO">
        SELECT
            GROUP_CONCAT( DISTINCT CASE WHEN ovmpm.`status` = ${@<EMAIL>} AND ovmpm.model_id = ovm.shoot_model_id AND ovm.shoot_model_id = ov.shoot_model_id AND ovm.rollback_id &lt;=> ov.rollback_id THEN ovmpm.model_id END ) AS sameProductModelIdStr,
            GROUP_CONCAT( DISTINCT CASE WHEN ovmpm.oust_type IN ( ${@com.ruoyi.common.core.enums.PreselectModelOustTypeEnum@MERCHANT_REJECTION.getCode}, ${@com.ruoyi.common.core.enums.PreselectModelOustTypeEnum@WANT_NOT.getCode} ) THEN ovmpm.model_id END ) AS sameProductOutModelIdStr,
            GROUP_CONCAT( DISTINCT CASE WHEN ovmpm.`status` != ${@<EMAIL>} THEN ovmpm.model_id END ) AS sameProductPreselectModelIdStr
        FROM
            order_video ov
            JOIN order_video_match ovm ON ovm.video_id = ov.id
            JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
        WHERE
            ov.product_link = #{productLink}
    </select>
    <select id="getDoesNotMeetTheMinimumOrderRequirementModelIds" resultType="java.lang.Long">
        WITH latest_logistic AS (
            SELECT
                ovl.video_id,
                ovl.receipt_time,
                ROW_NUMBER() OVER (
                PARTITION BY ovl.video_id
                ORDER BY ovl.shipping_time DESC
            ) AS rn
                FROM order_video_logistic AS ovl
        )
        SELECT
            DISTINCT ov.shoot_model_id
        FROM order_video AS ov
            JOIN latest_logistic AS ll ON ll.video_id = ov.id AND ll.rn = 1
            LEFT JOIN order_video_feed_back_material AS ovfbm ON ovfbm.video_id = ov.id
            LEFT JOIN order_video_task AS ovt ON ovt.video_id = ov.id AND task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getCode}
            LEFT JOIN order_video_task_detail AS ovtd ON ovtd.task_id = ovt.id AND ovtd.after_sale_video_type IN (${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOOT_VIDEO.getCode},${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOT_VIDEO.getCode}) AND ovtd.status NOT IN (${@<EMAIL>},${@<EMAIL>},${@<EMAIL>})
        WHERE
            ll.receipt_time IS NOT NULL
            AND ll.receipt_time &lt;= NOW() - INTERVAL 15 DAY
            AND (
                ovfbm.id IS NULL
                OR ovtd.id IS NOT NULL
            )
    </select>

    <!-- 根据匹配单ID集合查询对应的商家用户ID -->
    <select id="selectBizUserIdsByMatchIds" resultType="com.ruoyi.system.api.domain.vo.order.MatchBizUserMappingVO">
        SELECT
            ovm.id AS match_id,
            ov.create_order_biz_user_id AS biz_user_id
        FROM
            order_video_match ovm
            INNER JOIN order_video ov ON ovm.video_id = ov.id
                AND (ov.rollback_id &lt;=&gt; ovm.rollback_id)
        <where>
            <if test="matchIds != null and matchIds.size() > 0">
                ovm.id IN
                <foreach collection="matchIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>