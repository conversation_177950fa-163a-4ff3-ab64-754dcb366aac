<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderPayeeAccountConfigInfoMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigInfo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="payeeId" column="payee_id" jdbcType="BIGINT"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="bankAccount" column="bank_account" jdbcType="VARCHAR"/>
        <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
        <result property="companyAccountType" column="company_account_type" jdbcType="VARCHAR"/>
        <result property="companyBankCode" column="company_bank_code" jdbcType="VARCHAR"/>
        <result property="companyBankSubCode" column="company_bank_sub_code" jdbcType="VARCHAR"/>
        <result property="companyBankSwiftCode" column="company_bank_swift_code" jdbcType="VARCHAR"/>
        <result property="companyBankPayeeAddress" column="company_bank_payee_address" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,payee_id,status,
        bank_account,bank_name,company_account_type,
        company_bank_code,company_bank_sub_code,company_bank_swift_code,
        company_bank_payee_address,create_by,create_by_id,
        create_time,update_by,update_by_id,
        update_time
    </sql>
</mapper>
