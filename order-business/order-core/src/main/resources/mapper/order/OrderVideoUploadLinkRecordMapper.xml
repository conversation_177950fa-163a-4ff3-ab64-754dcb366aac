<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoUploadLinkRecordMapper">

    <select id="getHistoryUploadRecord"
            resultType="com.ruoyi.system.api.domain.vo.order.HistoryUploadRecordVO">
        SELECT
            ovulr.id,
            ovul.object,
            ovul.user_id,
            ovul.`time`,
            ovulr.count,
            ovulr.need_upload_link,
            ovulr.video_title,
            ovulr.upload_account,
            ovulr.video_cover,
            ovulr.remark,
            ovulr.upload_link,
            ovulr.`status`,
            ovulr.upload_user_id,
            ovulr.upload_time,
            ovulr.operate_remark
        FROM
            order_video_upload_link_record ovulr
                JOIN order_video_upload_link ovul ON ovul.id = ovulr.upload_link_id
                AND ovulr.upload_link_id = #{uploadLinkId}
        ORDER BY ovulr.create_time DESC
    </select>
</mapper>
