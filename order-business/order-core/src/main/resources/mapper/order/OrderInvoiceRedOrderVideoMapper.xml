<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderInvoiceRedOrderVideoMapper">


    <!--    根据发票红冲id查询发票红冲视频订单-->
    <select id="selectRedOrderVideoVOListByRedOrderIds"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderInvoiceRedOrderVideoVO">
        SELECT
            oiro.id,
            oiro.invoice_red_id,
            oiro.order_num,
            oirov.video_id,
            oirov.video_code,
            oirov.refund_type,
            oirov.withdraw_deposit_time,
            oirov.withdraw_deposit_amount,
            ot.pay_type
        FROM
            order_invoice_red_order_video oirov
                JOIN order_invoice_red_order oiro ON oiro.id = oirov.invoice_red_order_id
                LEFT JOIN order_pay_log ot ON ot.order_num = oiro.order_num
        <where>
            <if test="invoiceRedOrderIds != null and invoiceRedOrderIds.size() > 0 ">
                oirov.invoice_red_order_id IN
                <foreach collection="invoiceRedOrderIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>