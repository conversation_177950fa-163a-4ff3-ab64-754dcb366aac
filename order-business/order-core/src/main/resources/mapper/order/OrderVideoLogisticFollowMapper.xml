<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoLogisticFollowMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderVideoLogisticId" column="order_video_logistic_id" jdbcType="BIGINT"/>
            <result property="businessId" column="business_id" jdbcType="BIGINT"/>
            <result property="memberCode" column="member_code" jdbcType="VARCHAR"/>
            <result property="videoCode" column="video_code" jdbcType="VARCHAR"/>
            <result property="videoId" column="video_id" jdbcType="BIGINT"/>
            <result property="number" column="number" jdbcType="VARCHAR"/>
            <result property="handleStatus" column="handle_status" jdbcType="INTEGER"/>
            <result property="logisticStatus" column="logistic_status" jdbcType="INTEGER"/>
            <result property="followStatus" column="follow_status" jdbcType="INTEGER"/>
            <result property="notifyTime" column="notify_time" jdbcType="TIMESTAMP"/>
            <result property="logisticStartTime" column="logistic_start_time" jdbcType="TIMESTAMP"/>
            <result property="isDefaultLogisticStartTime" column="is_default_logistic_start_time" jdbcType="INTEGER"/>
            <result property="latestMainStatus" column="latest_main_status" jdbcType="TIMESTAMP"/>
            <result property="logisticUpdateTime" column="logistic_update_time" jdbcType="TIMESTAMP"/>
            <result property="modelResult" column="model_result" jdbcType="INTEGER"/>
            <result property="signTime" column="sign_time" jdbcType="TIMESTAMP"/>
            <result property="latestRemark" column="latest_remark" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_video_logistic_id,business_id,member_code,
        video_code,video_id,number,
        handle_status,logistic_status,follow_status,
        notify_time,logistic_start_time,is_default_logistic_start_time,latest_main_status,logistic_update_time,
        model_result,sign_time,latest_remark,
        create_by,create_by_id,create_time,
        update_by,update_by_id,update_time
    </sql>
    <sql id="baseList">
        select ovlf.id,
               ovlf.order_video_logistic_id,
               ovlf.business_id,
               ovlf.member_code,
               ovlf.video_code,
               ovlf.video_id,
               ovlf.number,
               ovlf.handle_status,
               ovlf.logistic_status,
               ovlf.follow_status,
               ovlf.notify_time,
               ovlf.logistic_start_time,
               ovlf.is_default_logistic_start_time,
               ovlf.latest_main_status,
               ovlf.logistic_update_time,
               ovlf.is_call_back,
               ovlf.model_result,
               ovlf.sign_time,
               ovlf.latest_remark,
               ovlf.latest_resource_id,
               ovlf.create_by,
               ovlf.create_by_id,
               ovlf.create_time,
               ovlf.update_by,
               ovlf.update_by_id,
               ovlf.update_time,
               ovlf.product_chinese                       AS closeProductChinese,
               ovlf.product_english                       AS closeProductEnglish,
               ovlf.product_pic                           AS closeProductPic,
               ovlf.shoot_model_id                        AS closeShootModelId,
               ovlf.contact_id                            AS closeContactId,
               ovlf.issue_id                              AS closeIssueId,
               ovlf.product_link                          AS closeProductLink,
               ovlf.create_order_user_name                AS closeOrderCreator,
               ovlf.create_order_user_nick_name           AS closeOrderCreatorNick,
               ovlf.create_order_operation_user_name      AS closeOrderOperator,
               ovlf.create_order_operation_user_nick_name AS closeOrderOperatorNick,
               ovlf.platform                              AS closePlatform,
               ov.platform,
               ov.product_chinese,
               ov.product_english,
               ov.product_pic,
               ov.shoot_model_id,
               ov.contact_id,
               ov.issue_id,
               ov.product_link,
               ov.status                                     videoStatus,
               ov.create_order_user_name,
               ov.create_order_user_nick_name,
               ov.create_order_operation_user_name,
               ov.create_order_operation_user_nick_name
        from order_video_logistic_follow ovlf
        inner join order_video ov on ov.id = ovlf.video_id
    </sql>
    <sql id = "followListWhere">
        ovlf.follow_status != ${@<EMAIL>}
        <if test="dto.orderVideoLogisticId != null">
            AND ovlf.order_video_logistic_id = #{dto.orderVideoLogisticId}
        </if>

        <if test="dto.businessId != null">
            AND ovlf.business_id = #{dto.businessId}
        </if>
        <if test="dto.memberCode != null and dto.memberCode != ''">
            AND ovlf.member_code = CONCAT('%', #{dto.memberCode}, '%')
        </if>
        <if test="dto.videoCode != null and dto.videoCode != ''">
            AND ovlf.video_code LIKE CONCAT('%', #{dto.videoCode}, '%')
        </if>
        <if test="dto.keyword != null and dto.keyword != ''">
            AND (
            ovlf.video_code LIKE CONCAT('%', UCASE(#{dto.keyword}), '%')
            or ovlf.member_code LIKE CONCAT('%', UCASE(#{dto.keyword}), '%')
            or UCASE(ovlf.number) LIKE CONCAT('%', UCASE(#{dto.keyword}), '%')
            <choose>
                <when test="dto.followStatus != null and dto.followStatus == 14">
                    or UCASE(ovlf.product_chinese) LIKE CONCAT('%',  UCASE(#{dto.keyword}), '%')
                    or UCASE(ovlf.create_order_user_name) LIKE CONCAT('%', UCASE(#{dto.keyword}), '%')
                    or UCASE(ovlf.create_order_user_nick_name) LIKE CONCAT('%', UCASE(#{dto.keyword}), '%')
                    <if test="dto.shootModelIdsByKeyword != null and dto.shootModelIdsByKeyword.size() >0">
                        or ovlf.shoot_model_id in
                        <foreach collection="dto.shootModelIdsByKeyword" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="dto.contactIdsByKeyword != null and dto.contactIdsByKeyword.size() >0">
                        or ovlf.contact_id in
                        <foreach collection="dto.contactIdsByKeyword" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or ovlf.issue_id in
                        <foreach collection="dto.contactIdsByKeyword" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </when>
                <otherwise>
                    or UCASE(ov.product_chinese) LIKE CONCAT('%',  UCASE(#{dto.keyword}), '%')
                    or UCASE(ov.create_order_user_name) LIKE CONCAT('%', UCASE(#{dto.keyword}), '%')
                    or UCASE(ov.create_order_user_nick_name) LIKE CONCAT('%', UCASE(#{dto.keyword}), '%')
                    <if test="dto.shootModelIdsByKeyword != null and dto.shootModelIdsByKeyword.size() >0">
                        or ov.shoot_model_id in
                        <foreach collection="dto.shootModelIdsByKeyword" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="dto.contactIdsByKeyword != null and dto.contactIdsByKeyword.size() >0">
                        or ov.contact_id in
                        <foreach collection="dto.contactIdsByKeyword" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or ov.issue_id in
                        <foreach collection="dto.contactIdsByKeyword" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            )
        </if>
        <if test="dto.videoId != null">
            AND ovlf.video_id = #{dto.videoId}
        </if>
        <if test="dto.number != null and dto.number != ''">
            AND ovlf.number = LIKE CONCAT('%', #{dto.number}, '%')
        </if>

        <if test="dto.handleStatus != null">
            AND ovlf.handle_status = #{dto.handleStatus}
        </if>
        <if test="dto.logisticStatus != null">
            AND ovlf.logistic_status = #{dto.logisticStatus}
        </if>
        <if test="dto.followStatus != null">
            AND ovlf.follow_status = #{dto.followStatus}
        </if>
        <if test="dto.modelResult != null">
            AND ovlf.model_result = #{dto.modelResult}
        </if>
        <choose>
            <when test="dto.followStatus != null and dto.followStatus == 14">
                <if test="dto.aboutMe == 'true' ">
                    and (ovlf.contact_id = #{dto.backUserId} OR ovlf.issue_id = #{dto.backUserId})
                </if>
                <if test="dto.contactIds != null and dto.contactIds.size() >0">
                    and ovlf.contact_id in
                    <foreach collection="dto.contactIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.issueIds != null and dto.issueIds.size() != 0">
                    and ovlf.issue_id in
                    <foreach collection="dto.issueIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.shootModelIdList != null and dto.shootModelIdList.size() >0">
                AND ovlf.shoot_model_id in
                <foreach collection=" dto.shootModelIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            </when>
            <otherwise>
                <if test="dto.aboutMe == 'true' ">
                    and (ov.contact_id = #{dto.backUserId} OR ov.issue_id = #{dto.backUserId})
                </if>
                <if test="dto.contactIds != null and dto.contactIds.size() >0">
                    and ov.contact_id in
                    <foreach collection="dto.contactIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.issueIds != null and dto.issueIds.size() != 0">
                    and ov.issue_id in
                    <foreach collection="dto.issueIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.shootModelIdList != null and dto.shootModelIdList.size() >0">
                AND ov.shoot_model_id in
                <foreach collection=" dto.shootModelIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            </otherwise>
        </choose>

        <if test="dto.handleStatusList != null and dto.handleStatusList.size() >0">
            and ovlf.handle_status in
            <foreach collection="dto.handleStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


        <if test="dto.memberCodes != null and dto.memberCodes.size() >0">
            AND ovlf.member_code in
            <foreach collection=" dto.memberCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.mainStatusLabels != null and dto.mainStatusLabels.size() >0">
            and ovlf.latest_main_status in
            <foreach collection="dto.mainStatusLabels" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="dto.logisticUpdateTimeStart != null and dto.logisticUpdateTimeEnd != null">
            AND ovlf.logistic_update_time BETWEEN #{dto.logisticUpdateTimeStart} AND #{dto.logisticUpdateTimeEnd}
        </if>
    </sql>
    <select id="selectOrderVideoLogisticFollowList" resultType="com.ruoyi.system.api.domain.vo.order.logistic.OrderVideoLogisticFollowVO">
        <include refid="baseList"/>
        <where>
            <include refid="followListWhere"/>
        </where>
    </select>

    <select id="getDetailById" resultType="com.ruoyi.system.api.domain.vo.order.logistic.OrderVideoLogisticFollowVO">
        <include refid="baseList"/>
        where ovlf.id = #{id}
    </select>

    <select id="getStatisticsVO" resultType="com.ruoyi.system.api.domain.vo.order.logistic.OrderVideoLogisticFollowStatisticsVO">
        select
        sum(CASE WHEN ovlf.follow_status = ${@com.ruoyi.common.core.enums.FollowStatusEnum@NEED_HANDLE.getcode} THEN 1 ELSE 0 END) needHandleCount,
        sum(CASE WHEN ovlf.follow_status = ${@com.ruoyi.common.core.enums.FollowStatusEnum@TEMP_HOLD.getcode} THEN 1 ELSE 0 END)   tempHoldCount,
        sum(CASE WHEN ovlf.follow_status = ${@com.ruoyi.common.core.enums.FollowStatusEnum@NEED_FOLLOW_UP.getcode} THEN 1 ELSE 0 END)   needFollowCount,
        sum(CASE WHEN ovlf.follow_status = ${@com.ruoyi.common.core.enums.FollowStatusEnum@MODEL_CONFIRM_PEND.getcode} THEN 1 ELSE 0 END)   modelConfirmPendCount,
        sum(CASE WHEN ovlf.follow_status = ${@com.ruoyi.common.core.enums.FollowStatusEnum@NO_FOLLOW_NEED.getcode} THEN 1 ELSE 0 END)   noFollowNeedCount,
        sum(CASE WHEN ovlf.follow_status = ${@<EMAIL>} THEN 1 ELSE 0 END)   closeCount
        from order_video_logistic_follow ovlf
                 inner join order_video ov on ov.id = ovlf.video_id
        where  ovlf.follow_status != ${@<EMAIL>}
    </select>

    <select id="getOrderIssueId" resultType="java.lang.Long">
        select
            ov.issue_id
        from order_video_logistic_follow ovlf
                 inner join order_video ov on ov.id = ovlf.video_id
        where ovlf.follow_status = #{followStatus}

    </select>
    <select id="memberCodeList" resultType="com.ruoyi.system.api.domain.vo.order.logistic.MemberCodeListVO">
        select
          ovlf.member_code,
          COUNT(ovlf.member_code) AS logisticFollowNum
        from order_video_logistic_follow ovlf
        inner join order_video ov on ov.id = ovlf.video_id
        <where>
            <include refid="followListWhere"/>
        </where>
        group by ovlf.member_code
        order by logisticFollowNum desc
    </select>


    <select id="modelListSelect" resultType="com.ruoyi.system.api.domain.vo.order.logistic.LogisticFollowModelListVO">
        select
          ov.shoot_model_id as modelId,
          COUNT(ov.shoot_model_id) AS logisticFollowNum
        from order_video_logistic_follow ovlf
        inner join order_video ov on ov.id = ovlf.video_id
        <where>
            <include refid="followListWhere"/>
        </where>
        group by ov.shoot_model_id
        order by logisticFollowNum desc
    </select>


    <select id="getOrderContactId" resultType="java.lang.Long">
        select
            ov.contact_id
        from order_video_logistic_follow ovlf
                 inner join order_video ov on ov.id = ovlf.video_id
        where ovlf.follow_status = #{followStatus}
    </select>
</mapper>
