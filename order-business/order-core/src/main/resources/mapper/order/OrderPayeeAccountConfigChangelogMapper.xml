<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderPayeeAccountConfigChangelogMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigChangelog">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="comments" column="comments" jdbcType="VARCHAR"/>
        <result property="configType" column="config_type" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,type,comments,
        config_type,create_by,create_by_id,
        create_time,update_by,update_by_id,
        update_time
    </sql>
    <select id="historyLog"
            resultType="com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigChangelogDTO">
        SELECT <include refid="Base_Column_List"></include> from order_payee_account_config_changelog
        where config_type = #{type}
        order by create_time desc
    </select>
</mapper>
