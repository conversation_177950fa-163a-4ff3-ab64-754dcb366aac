<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoLogisticFollowRecordMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="followId" column="follow_id" jdbcType="BIGINT"/>
            <result property="resourceId" column="resource_id" jdbcType="VARCHAR"/>
            <result property="eventName" column="event_name" jdbcType="VARCHAR"/>
            <result property="eventContent" column="event_content" jdbcType="VARCHAR"/>
            <result property="eventExecuteObject" column="event_execute_object" jdbcType="BOOLEAN"/>
            <result property="eventExecuteUserId" column="event_execute_user_id" jdbcType="BIGINT"/>
            <result property="eventExecuteUserName" column="event_execute_user_name" jdbcType="VARCHAR"/>
            <result property="eventExecuteNickName" column="event_execute_nick_name" jdbcType="VARCHAR"/>
            <result property="eventExecuteTime" column="event_execute_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,follow_id,resource_id,
        event_name,event_content,event_execute_object,
        event_execute_user_id,event_execute_user_name,event_execute_nick_name,
        event_execute_time
    </sql>
</mapper>
