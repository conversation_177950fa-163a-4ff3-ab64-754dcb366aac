//package com.wnkx.order.service.impl
//
//import cn.hutool.core.text.CharSequenceUtil
//import cn.hutool.core.util.StrUtil
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
//import com.ruoyi.common.core.constant.UserTypeConstants
//import com.ruoyi.common.core.enums.*
//import com.ruoyi.common.core.utils.SpringUtils
//import com.ruoyi.common.core.utils.StringUtils
//import com.ruoyi.common.redis.service.DelayQueueService
//import com.ruoyi.common.redis.service.RedisService
//import com.ruoyi.common.security.service.TokenService
//import com.ruoyi.common.security.utils.SecurityUtils
//import com.ruoyi.system.api.RemoteBusinessAccountService
//import com.ruoyi.system.api.config.OrderPayProperties
//import com.ruoyi.system.api.domain.dto.order.OrderAuditDto
//import com.ruoyi.system.api.domain.dto.order.OrderPayLockDTO
//import com.ruoyi.system.api.domain.dto.order.OrderVideoDTO
//import com.ruoyi.system.api.domain.dto.order.SubmitCredentialDTO
//import com.ruoyi.system.api.domain.entity.Model
//import com.ruoyi.system.api.domain.entity.order.Order
//import com.ruoyi.system.api.domain.entity.order.OrderVideo
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO
//import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO
//import com.ruoyi.system.api.domain.vo.order.OrderPayInfoVO
//import com.ruoyi.system.api.domain.vo.order.RealTimeExchangeRateVO
//import com.ruoyi.system.api.model.LoginBusiness
//import com.wnkx.order.config.AnotherPayProperties
//import com.wnkx.order.config.OrderVideoProperties
//import com.wnkx.order.config.PayConfig
//import com.wnkx.order.factory.OrderVideoFlowServiceFactory
//import com.wnkx.order.mapper.OrderMapper
//import com.wnkx.order.mapper.OrderVideoMapper
//import com.wnkx.order.mapper.VideoCartMapper
//import com.wnkx.order.remote.RemoteService
//import com.wnkx.order.service.*
//import com.wnkx.order.service.core.AsyncTaskService
//import com.wnkx.order.service.core.OrderDataScopeService
//import com.wnkx.order.utlis.CompletableFutureUtil
//import okhttp3.OkHttpClient
//import org.mockito.MockedStatic
//import org.mockito.Mockito
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
//import org.springframework.transaction.support.TransactionTemplate
//import spock.lang.Shared
//import spock.lang.Specification
//
//import java.lang.reflect.Field
//
//class OrderServiceImplTest extends Specification {
//
//    IOrderVideoModelService orderVideoModelService = Mock()
//    RemoteService remoteService = Mock()
//    RemoteBusinessAccountService remoteBusinessAccountService = Mock()
//    ExchangeRateService exchangeRateService = Mock()
//    RedisService redisService = Mock()
//    IOrderNoteService orderNoteService = Mock()
//    IOrderMemberService orderMemberService = Mock()
//    IOrderVideoRefundService orderVideoRefundService = Mock()
//    IOrderDocumentResourceService orderDocumentResourceService = Mock()
//    IOrderVideoMatchPreselectModelService orderVideoPreselectModelService = Mock()
//    IOrderVideoLogisticService orderVideoLogisticService = Mock()
//    IOrderVideoCaseService orderVideoCaseService = Mock()
//    OrderVideoFeedBackService orderFeedBackService = Mock()
//    OrderVideoFlowServiceFactory orderVideoFlowServiceFactory = Mock()
//    IOrderInvoiceService orderInvoiceService = Mock()
//    ThreadPoolTaskExecutor threadPoolTaskExecutor = Mock()
//    OrderDataScopeService orderDataScopeService = Mock()
//    IFyOrderTableService fyOrderTableService = Mock()
//    IOrderService self = Mock()
//    OrderMapper orderMapper = Mock()
//    OrderResourceService orderResourceService = Mock()
//    IPayeeAccountConfigService payeeAccountConfigService = Mock()
//
//
//    /**
//     * orderVideoServiceImpl
//     */
//    IOrderVideoContentService videoContentService = Mock()
//    IOrderVideoCarryService orderVideoCarryService = Mock()
//    IOrderVideoTagService orderVideoTagService = Mock()
//    OrderVideoMapper orderVideoMapper = Mock()
//    AsyncTaskService asyncTaskService = Mock()
//    OrderVideoChangeLogService orderVideoChangeLogService = Mock()
//    OrderVideoFlowRecordService orderVideoFlowRecordService = Mock()
//    OrderVideoModelShippingAddressService orderVideoModelShippingAddressService = Mock()
//    OrderVideoBackModifyAmountRecordsService orderVideoBackModifyAmountRecordsService = Mock()
//    OrderVideoOperateService orderVideoOperateService = Mock()
//    OrderVideoFlowNodeDiagramService orderVideoFlowNodeDiagramService = Mock()
//    OrderVideoModelChangeService orderVideoModelChangeService = Mock()
//    OrderVideoProperties orderVideoProperties = Mock()
//    DelayQueueService delayQueueService = Mock()
//    OrderVideoMatchService orderVideoMatchService = Mock()
//    OrderVideoServiceImpl orderVideoService = new OrderVideoServiceImpl(
//            videoContentService,
//            orderVideoCarryService,
//            orderVideoModelService,
//            orderVideoTagService,
//            orderVideoCaseService,
//            asyncTaskService,
//            remoteService,
//            orderMapper,
//            orderResourceService,
//            orderVideoChangeLogService,
//            orderVideoFlowRecordService,
//            orderVideoModelShippingAddressService,
//            orderVideoBackModifyAmountRecordsService,
//            orderVideoOperateService,
//            orderVideoFlowNodeDiagramService,
//            orderVideoModelChangeService,
//            orderVideoProperties,
//            delayQueueService,
//            redisService,
//            orderVideoMatchService
//    )
//
//    /**
//     * payServiceImpl
//     */
//    PayConfig payConfig = Mock()
//    OkHttpClient okHttpClient = Mock()
//    ThirdPayLogService thirdPayLogService = Mock()
//    TransactionTemplate transactionTemplate = Mock()
//    OrderPayProperties orderPayProperties = Mock()
//    WeChatService weChatService = Mock()
//    AlipayServiceImpl alipayServiceImpl = Mock()
//    OrderAnotherPayService orderAnotherPayService = Mock()
//    PayServiceImpl payServiceImpl = new PayServiceImpl(
//            payConfig,
//            okHttpClient,
//            redisService,
//            thirdPayLogService,
//            exchangeRateService,
//            transactionTemplate,
//            orderService,
//            orderVideoService,
//            orderMemberService,
//            fyOrderTableService,
//            asyncTaskService,
//            remoteService,
//            orderPayProperties,
//            orderDataScopeService,
//            weChatService,
//            alipayServiceImpl,
//            orderAnotherPayService
//    )
//
//    /**
//     * videoCartServiceImpl
//     */
//    VideoCartMapper videoCartMapper = Mock()
//    VideoCartContentService videoCartContentService = Mock()
//    CompletableFutureUtil completableFutureUtil = Mock()
//    VideoCartServiceImpl videoCartServiceImpl = new VideoCartServiceImpl(
//            payServiceImpl,
//            videoCartContentService,
//            remoteService,
//            threadPoolTaskExecutor,
//            redisService,
//            asyncTaskService,
//            orderResourceService,
//            orderVideoService,
//            orderVideoOperateService
//    )
//
//    OrderVideoRoastService orderVideoRoastService = Mock()
//    OrderVideoReminderRecordService orderVideoReminderRecordService = Mock()
//    OrderVideoCommentService orderCommentService = Mock()
//    OrderVideoFeedBackMaterialService orderFeedBackMaterialService = Mock()
//    OrderVideoUploadLinkService orderVideoUploadLinkService = Mock()
//    IOrderPayeeAccountService orderPayeeAccountService = Mock()
//    TokenService tokenService = Mock()
//    OrderVideoTaskService orderVideoTaskService = Mock()
//    IOrderVideoTaskDetailService orderVideoTaskDetailService = Mock()
//    AnotherPayProperties anotherPayProperties = Mock()
//    /**
//     * orderService
//     */
//    IOrderService orderService = new OrderServiceImpl(
//            redisService,
//            orderNoteService,
//            orderVideoService,
//            orderMemberService,
//            orderVideoRefundService,
//            orderDocumentResourceService,
//            orderVideoLogisticService,
//            orderVideoCaseService,
//            orderVideoModelService,
//            orderFeedBackService,
//            orderFeedBackMaterialService,
//            remoteService,
//            remoteBusinessAccountService,
//            orderVideoFlowServiceFactory,
//            orderInvoiceService,
//            threadPoolTaskExecutor,
//            orderDataScopeService,
//            orderVideoRoastService,
//            self,
//            videoCartServiceImpl,
//            orderVideoReminderRecordService,
//            completableFutureUtil,
//            orderCommentService,
//            orderResourceService,
//            orderVideoUploadLinkService,
//            orderVideoChangeLogService,
//            orderVideoModelShippingAddressService,
//            orderVideoOperateService,
//            orderVideoFlowNodeDiagramService,
//            orderVideoModelChangeService,
//            videoContentService,
//            asyncTaskService,
//            orderPayProperties,
//            orderPayeeAccountService,
//            orderVideoTagService,
//            orderVideoMatchService,
//            orderVideoTaskService,
//            orderVideoTaskDetailService,
//            orderAnotherPayService,
//            anotherPayProperties
//    )
//    @Shared
//    MockedStatic securityUtils
//    @Shared
//    MockedStatic springUtils
//    @Shared
//    MockedStatic stringUtils
//
//    def setupSpec() {
//        securityUtils = Mockito.mockStatic(SecurityUtils.class)
//        springUtils = Mockito.mockStatic(SpringUtils.class)
//        stringUtils = Mockito.mockStatic(StringUtils.class)
//    }
//
//    def setup() {
//        // 使用反射注入 baseMapper
//        Field baseMapperField = ServiceImpl.getDeclaredField("baseMapper")
//        baseMapperField.setAccessible(true)
//        baseMapperField.set(videoCartServiceImpl, videoCartMapper)
//        baseMapperField.set(orderVideoService, orderVideoMapper)
//        baseMapperField.set(orderService, orderMapper)
//    }
//
//    def cleanupSpec() {
//        securityUtils.close()
//        springUtils.close()
//        stringUtils.close()
//    }
//
//    /**
//     * 重新加入购物车（成功测试）
//     */
//    def rejoinCartSuccess() {
//        given:
//        orderVideoMapper.selectById(videoId) >> orderVideo
//        Mockito.when(SecurityUtils.getLoginUserType()).thenReturn(UserTypeConstants.USER_TYPE)
//        remoteService.getModelSimpleMap(_) >> Collections.emptyMap()
//        Mockito.when(StringUtils.splitToLong(CharSequenceUtil.EMPTY, StrUtil.COMMA)).thenReturn(Collections.emptyList())
//        orderResourceService.getResourceMapByIds(_) >> Collections.emptyMap()
//        videoContentService.selectListByVideoIdOrType(_, VideoContentTypeEnum.REQUIRE.getCode()) >> Collections.emptyList()
//        addCartGiven()
//        Mockito.when(SpringUtils.getBean(VideoCartService.class)).thenReturn(videoCartServiceImpl)
//
//        when:
//        videoCartServiceImpl.rejoinCart(videoId)
//
//        then:
//        notThrown(Exception)
//
//        where:
//        videoId | orderVideo
//        9L      | new OrderVideo(id: 9L, status: 9)
//    }
//    /**
//     * 重新加入购物车（异常测试）
//     */
//    def rejoinCartException() {
//        given:
//        orderVideoMapper.selectById(videoId) >> orderVideo
//        Mockito.when(SecurityUtils.getLoginUserType()).thenReturn(UserTypeConstants.USER_TYPE)
//        remoteService.getModelSimpleMap(_) >> Collections.emptyMap()
//        Mockito.when(StringUtils.splitToLong(CharSequenceUtil.EMPTY, StrUtil.COMMA)).thenReturn(Collections.emptyList())
//        orderResourceService.getResourceMapByIds(_) >> Collections.emptyMap()
//        videoContentService.selectListByVideoIdOrType(_, VideoContentTypeEnum.REQUIRE.getCode()) >> Collections.emptyList()
//
//        when:
//        videoCartServiceImpl.rejoinCart(videoId)
//
//        then:
//        IllegalArgumentException illegalArgumentException = thrown()
//        println(illegalArgumentException)
//        illegalArgumentException.message == errorMessage
//
//        where:
//        videoId | orderVideo                        | errorMessage
//        1L      | new OrderVideo(id: 1L, status: 1) | "订单不是交易关闭，无需重新加入购物车"
//        2L      | new OrderVideo(id: 2L, status: 2) | "订单不是交易关闭，无需重新加入购物车"
//        3L      | new OrderVideo(id: 3L, status: 3) | "订单不是交易关闭，无需重新加入购物车"
//        4L      | new OrderVideo(id: 4L, status: 4) | "订单不是交易关闭，无需重新加入购物车"
//        5L      | new OrderVideo(id: 5L, status: 5) | "订单不是交易关闭，无需重新加入购物车"
//        6L      | new OrderVideo(id: 6L, status: 6) | "订单不是交易关闭，无需重新加入购物车"
//        7L      | new OrderVideo(id: 7L, status: 7) | "订单不是交易关闭，无需重新加入购物车"
//        8L      | new OrderVideo(id: 8L, status: 8) | "订单不是交易关闭，无需重新加入购物车"
//    }
//
//
//    /**
//     * 加入购物车（成功测试）
//     */
//    def addCartSuccess() {
//        given:
//        addCartGiven()
//
//        when:
//        var result = orderService.addCart(dto)
//        println(dto)
//
//        then:
//        result != null
//        result.orderNum == null
//        result.cannotModel == null
//        notThrown(Exception)
//
//        where:
//        dto << [
//                new OrderVideoDTO(shootCount: 2, intentionModelIds: []),
//                new OrderVideoDTO(shootCount: 2, intentionModelIds: [31018L], shootingCountry: 7, modelType: 0, platform: 0)
//        ]
//    }
//
//    /**
//     * 加入购物车（异常测试）
//     */
//    def addCartException() {
//        given:
//        addCartGiven()
//
//        when:
//        orderService.addCart(dto)
//
//        then:
//        Exception exception = thrown()
//        println(exception.message)
//        exception.message == exceptionMessage
//
//        where:
//        dto                                                                                                                  | exceptionMessage
//        new OrderVideoDTO(shootCount: 1, intentionModelIds: [31017L, 31018L], shootingCountry: 7, modelType: 0, platform: 0) | "1个视频对应1个模特，请留下1个模特即可~"
//        new OrderVideoDTO(shootCount: 201, intentionModelIds: [31018L], shootingCountry: 7, modelType: 0, platform: 0)       | "购物车最多添加200个视频，还能加购200单"
//    }
//
//
//    /**
//     * 创建订单
//     */
//    def createOrder() {
//        given:
//        Mockito.when(SecurityUtils.getLoginBusinessUser()).thenReturn(getLoginUser(null, null))
//        remoteService.getBusinessAccountByUnionId(_) >> getBusinessAccountVo(null, null)
//        orderVideoModelService.checkModelOverdueVideo(_) >> Collections.emptyList()
//        orderVideoModelService.checkModelAcceptability(_) >> Collections.emptyList()
//        remoteService.queryCannotAcceptList(_) >> Collections.emptyList()
//
//        Mockito.when(SpringUtils.getBean(PayService.class)).thenReturn(payServiceImpl)
//        exchangeRateService.getCurrentExchange() >> RealTimeExchangeRateVO.builder().realTimeExchangeRate(BigDecimal.valueOf(7.2345)).isDefault(false).build()
//        remoteService.queryModelSimpleList(_)>> [new ModelOrderSimpleVO(id: 1024L, nation: 7, type: 1, platform: 3)]
//
//        when:
//        var result = orderService.createOrder(dto)
//        println(result)
//        println(dto)
//
//        then:
//        result != null
//        result.orderNum != null
//        result.cannotModel == null
//        notThrown(Exception)
//
//        where:
//        dto << [
//                [new OrderVideoDTO(
//                        platform: 1,
//                        productChinese: "产品中文名",
//                        productEnglish: "Product English name",
//                        videoFormat: 2,
//                        shootingCountry: 7,
//                        modelType: 1,
//                        referenceVideoLink: ""
//                )],
//                [new OrderVideoDTO(
//                        platform: 3,
//                        productChinese: "产品中文名",
//                        productEnglish: "Product English name",
//                        videoFormat: 2,
//                        shootingCountry: 7,
//                        modelType: 1,
//                        referenceVideoLink: "",
//                        intentionModelId: 1024L
//                )]
//        ]
//    }
//
//    def "审核订单：审核状态：#orderAuditDto.auditStatus 实付金额：#orderAuditDto.realPayAmount 订单审核状态：#orderVo.auditStatus 订单金额：#orderVo.payAmount 异常消息：#expectedMessage"() {
//        given:
//        orderMapper.selectOrderById(_) >> orderVo
//        redisService.getLock(_, _) >> true
////        Mockito.when(StringUtils.isEmpty(Collections.emptyList())).thenReturn(true)
//
//        when:
//        orderService.auditOrder(orderAuditDto)
//
//        then:
//        def exception = thrown(Exception)
//        exception.message == expectedMessage
//
//        where:
//        orderAuditDto                          | orderVo                               | expectedMessage
//        getOrderAuditDto(3, new Date(), 100.0) | getOrder(1, 100.0, 1, 1, null, null)  | "选择审核状态有误，请确认选择状态！"
//        getOrderAuditDto(2, new Date(), 100.0) | getOrder(1, 100.0, 1, 1, null, null)  | "订单已审核，无法标记为异常"
//        getOrderAuditDto(2, new Date(), 100.0) | getOrder(2, 100.0, 1, 1, null, null)  | "订单已标记为异常，无法再次标记为异常"
//        getOrderAuditDto(1, new Date(), 100.0) | getOrder(1, 100.0, 1, 1, null, null)  | "抱歉,订单已审核,无法再次审核"
//        getOrderAuditDto(1, null, 100.0)       | getOrder(0, 100.0, 1, 1, null, null)  | "抱歉,支付时间不能为空"
//        getOrderAuditDto(1, new Date(), null)  | getOrder(0, 100.0, 1, 1, null, null)  | "抱歉,订单实付金额不能为空"
//    }
//
//    def "检查商家状态：账号状态 = #businessAccountVo.status，商家状态：#businessAccountVo.businessVO.status 异常:#expectedMessage"() {
//        given:
//        Mockito.when(SecurityUtils.getLoginBusinessUser()).thenReturn(getLoginUser(null, null))
//        remoteService.getBusinessAccountByUnionId(_) >> businessAccountVo
//
//        when:
//        orderService.checkBusinessStatus()
//
//        then:
//        def exception = thrown(Exception)
//        exception.message == expectedMessage
//
//        where:
//        businessAccountVo          | expectedMessage
//        getBusinessAccountVo(0, 1) | "对不起，您的账号：123654 已停用，如需恢复，请联系商家~"
//        getBusinessAccountVo(0, 2) | "对不起，您的账号：123654 已被删除，如需恢复，请联系商家~"
//        getBusinessAccountVo(1, 0) | "对不起，您的商家：润一科技 已停用，如需恢复，请联系客服~"
//        getBusinessAccountVo(2, 0) | "对不起，您的商家：润一科技 已被删除，如需恢复，请联系客服~"
//
//    }
//
//    def "提交凭证:订单支付类型=#tableOrder.payType 发票抬头：#submitCredentialDTO.title 税号=#submitCredentialDTO.dutyParagraph 发票备注=#submitCredentialDTO.content  异常：#expectedMessage"() {
//        given:
//        orderMapper.getOrderByOrderNum(_) >> tableOrder
//        def orderVideo1 = new OrderVideo(videoPrice: 300.0, picPrice: 150.0, exchangePrice: 100.0, servicePrice: 50.0, status: orderVideoStatus)
//
//        Mockito.when(SpringUtils.getBean(PayService.class)).thenReturn(Mock(PayService.class))
//        def orderPayInfoVo = new OrderPayInfoVO(payAmount: 100.0, canInputSeedCode: false)
//        SpringUtils.getBean(PayService.class).payInfo(_, _, _,false) >> orderPayInfoVo
//
//        orderVideoService.baseMapper.selectByOrderNum(_) >> [orderVideo1]
//        orderVideoService.selectValidByOrderNum(_) >> [orderVideo1]
//        when:
//        orderService.submitCredential(submitCredentialDTO, false)
//
//        then:
//        def exception = thrown(Exception)
//        println(exception.message)
//        exception.message == expectedMessage
//        where:
//        tableOrder                                                                                                                  | submitCredentialDTO                                                                       | orderVideoStatus              || expectedMessage
//        getOrder(AuditStatusEnum.APPROVE.code, 100.0, PayTypeEnum.WECHAT.code, OrderTypeEnum.VIDEO_ORDER.code, null, null)          | getSubmitCredentialDTO(null, null, null, null, null, null)                                | OrderStatusEnum.FINISHED.code || "订单支付类型不能为空~"
//        getOrder(AuditStatusEnum.APPROVE.code, 100.0, PayTypeEnum.WECHAT.code, OrderTypeEnum.VIDEO_ORDER.code, null, null)          | getSubmitCredentialDTO(null, null, null, PayTypeEnum.ALIPAY.code, null, null)             | OrderStatusEnum.FINISHED.code || "支付方式只能是银行或者对公"
//        getOrder(AuditStatusEnum.APPROVE.code, 100.0, PayTypeEnum.PUBLIC.code, OrderTypeEnum.VIDEO_ORDER.code, null, null)          | getSubmitCredentialDTO("title", null, null, PayTypeEnum.BANK.code, null, null)            | OrderStatusEnum.FINISHED.code || "视频订单状态异常，需要是：[待支付]，当前是：[已完成]，请刷新页面"
//        getOrder(AuditStatusEnum.APPROVE.code, 100.0, PayTypeEnum.PUBLIC.code, OrderTypeEnum.VIDEO_ORDER.code, null, null)          | getSubmitCredentialDTO("title", null, null, PayTypeEnum.BANK.code, 10.0, 2.0)             | OrderStatusEnum.UN_PAY.code   || "应支付金额与实际不符，不允许操作"
////        getOrder(AuditStatusEnum.APPROVE.code, 100.0, PayTypeEnum.PUBLIC.code, OrderTypeEnum.VIDEO_ORDER.code, null, null)          | getSubmitCredentialDTO("title", null, null, PayTypeEnum.BANK.code, 100.0, 0.0)            | OrderStatusEnum.UN_PAY.code   || "税点费用与实际不符，不允许操作"
//
//        getOrder(AuditStatusEnum.APPROVE.code, 100.0, PayTypeEnum.PUBLIC_BALANCE.code, OrderTypeEnum.VIDEO_ORDER.code, 100.0, null) | getSubmitCredentialDTO("title", "dutyParagraph", null, PayTypeEnum.PUBLIC.code, 100.0, 2.0) | OrderStatusEnum.UN_PAY.code   || "对公下发票信息不能为空"
//        getOrder(AuditStatusEnum.APPROVE.code, 100.0, PayTypeEnum.PUBLIC_BALANCE.code, OrderTypeEnum.VIDEO_ORDER.code, 100.0, null) | getSubmitCredentialDTO("title", null, "content", PayTypeEnum.PUBLIC.code, 100.0, 2.0)       | OrderStatusEnum.UN_PAY.code   || "对公下发票信息不能为空"
//    }
//
////    def "下单锁定:支付类型：#orderPayLockDto.payType 订单支付类型：#tableOrder.payType 异常：#expectedMessage"() {
////        given:
////        baseMapper.getOrderByOrderNum(_) >> tableOrder
////        redisService.getLock(_, _) >> true
////        remoteService.getBusinessAccountByAccountId(_) >> businessAccountVo
////        Mockito.when(SpringUtils.getBean(PayService.class)).thenReturn(payServiceImpl)
////
////        when:
////        orderService.payLock(orderPayLockDto)
////
////        then:
////        def exception = thrown(Exception)
////        exception.message == expectedMessage
////
////        where:
////        tableOrder                              | businessAccountVo             | orderPayLockDto                          | expectedMessage
////        getOrder(1, null, 1, 1, 150.0, null)    | getBusinessAccount(0, 100)    | getOrderPayLockDTO(1, 50.0, 100.0, 0.0)  | "支付类型已选择无法再次选择"
////        getOrder(1, null, null, 1, 150.0, null) | getBusinessAccount(1, 100)    | getOrderPayLockDTO(1, 50.0, 100.0, 0.0)  | "商家余额已被锁定"
////        getOrder(1, null, null, 1, 150.0, null) | getBusinessAccount(0, 30.0)   | getOrderPayLockDTO(1, 50.0, 100.0, 0.0)  | "商家余额不足"
////        getOrder(1, null, null, 3, 150.0, null) | getBusinessAccount(0, 100.0)  | getOrderPayLockDTO(1, 50.0, 100.0, 0.0)  | "订单类型有误"
////        getOrder(1, null, null, 1, 150.0, null) | getBusinessAccount(0, 100.0)  | getOrderPayLockDTO(1, 50.0, 100.0, 10.0) | "税点费用与实际不符，不允许操作！"
////        getOrder(1, null, null, 1, 150.0, 5.0)  | getBusinessAccount(0, 100.0)  | getOrderPayLockDTO(1, 30.0, 100.0, 0.0)  | "应支付金额与实际不符，不允许操作！"
////        getOrder(1, null, null, 1, 100.0, 0.0)  | getBusinessAccount(0, 1000.0) | getOrderPayLockDTO(1, 130.0, 0.0, 0.0)   | "余额不能大于订单金额！"
////    }
//
//    def getLoginUser(businessStatus, status) {
//        def loginBusiness = new LoginBusiness()
//        def businessAccountVO = getBusinessAccountVo(businessStatus, status)
//        loginBusiness.businessAccountVO = businessAccountVO
//        return loginBusiness
//    }
//
//    def getBusinessAccountVo(businessStatus, status){
//        def businessAccountVO = new BusinessAccountVO()
//        def businessVo = new BusinessVO()
//        businessVo.id = 1017L
//        businessVo.memberCode = "LJQZ"
//        businessVo.memberType = StatusTypeEnum.YES.getCode()
//        businessVo.name = "润一科技"
//        businessVo.status = businessStatus
//        businessVo.waiterId = 109L
//        businessAccountVO.businessId = 1014L
//        businessAccountVO.status = status
//        businessAccountVO.account = "123654"
//        businessAccountVO.businessVO = businessVo
//        businessAccountVO.unionid = "oftcb6tyDBevCtXIJHDV_wXDqjAE"
//        return businessAccountVO;
//    }
//
//    def getModel() {
//        def model = new Model()
//        model.id = 1024L
//        return model
//    }
//
//    def getOrder(auditStatus, payAmount, payType, orderType, orderAmount, taxPointCost) {
//        def order = new Order()
//        order.id = 1L
//        order.auditStatus = auditStatus
//        order.payAmount = payAmount
//        order.payType = payType
//        order.orderType = orderType
//        order.orderAmount = orderAmount
//        order.taxPointCost = taxPointCost
//        order.isDefaultExchangeRate = false
//        return order
//    }
//
//    def getSubmitCredentialDTO(title, dutyParagraph, content, payType, payAmount, taxPointCost) {
//        def dto = new SubmitCredentialDTO()
//        dto.title = title
//        dto.dutyParagraph = dutyParagraph
//        dto.content = content
//        dto.payType = payType
//        dto.taxPointCost = taxPointCost
//        dto.payAmount = payAmount
//        return dto
//    }
//
//    def getOrderAuditDto(auditStatus, payTime, realPayAmount) {
//        def orderAudit = new OrderAuditDto()
//        orderAudit.id = 1L
//        orderAudit.realPayAmount = realPayAmount
//        orderAudit.orderRemark = "订单备注"
//        orderAudit.payTime = payTime
//        orderAudit.auditStatus = auditStatus
//
//        return orderAudit
//    }
//
//    def getBusinessAccount(isBalanceLock, balance) {
//        def businessAccountVO = new BusinessAccountVO()
//        def businessVo = new BusinessVO()
//        businessVo.isBalanceLock = isBalanceLock
//        businessVo.balance = balance
//        businessAccountVO.businessVO = businessVo
//        return businessAccountVO
//    }
//
//    def getOrderPayLockDTO(payType, useBalance, payAmount, taxPointCost) {
//        def orderPayLockDto = new OrderPayLockDTO()
//        orderPayLockDto.orderNum = "DDWN123456789"
//        orderPayLockDto.payType = payType
//        orderPayLockDto.useBalance = useBalance
//
//        return orderPayLockDto
//    }
//
//    def getOrderPayInfoVO(taxPointCost, payAmount) {
//        def payInfo = new OrderPayInfoVO()
//        payInfo.taxPointCost = taxPointCost
//        payInfo.payAmount = payAmount
//
//        return payInfo
//    }
//
//    void addCartGiven() {
//        Mockito.when(SecurityUtils.getLoginBusinessUser()).thenReturn(getLoginUser(null, null))
//        remoteService.getBusinessAccountByUnionId(_) >> getBusinessAccountVo(null, null)
//        orderVideoModelService.checkModelOverdueVideo(_) >> Collections.emptyList()
//        orderVideoModelService.checkModelAcceptability(_) >> Collections.emptyList()
//        remoteService.queryCannotAcceptList(_) >> Collections.emptyList()
//        videoCartMapper.getBusinessCartCount(_) >> 0L
//        exchangeRateService.getCurrentExchange() >> RealTimeExchangeRateVO.builder().realTimeExchangeRate(BigDecimal.valueOf(7.2345)).isDefault(false).build()
//
//        remoteService.queryModelSimpleList(_) >> [new ModelOrderSimpleVO(id: 31018L, nation: 7, type: 0, platform: 0)]
//    }
//}
