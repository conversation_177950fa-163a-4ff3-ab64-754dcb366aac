//package com.wnkx.order.service.impl
//
//import com.ruoyi.common.redis.service.RedisService
//import com.ruoyi.common.security.utils.SecurityUtils
//import com.ruoyi.system.api.config.OrderPayProperties
//import com.ruoyi.system.api.domain.dto.order.OrderVideoDTO
//import com.ruoyi.system.api.domain.dto.order.PayMemberInfoDTO
//import com.ruoyi.system.api.domain.entity.order.Order
//import com.ruoyi.system.api.domain.entity.order.OrderVideo
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO
//import com.ruoyi.system.api.domain.vo.order.RealTimeExchangeRateVO
//import com.ruoyi.system.api.model.LoginBusiness
//import com.wnkx.order.config.PayConfig
//import com.wnkx.order.remote.RemoteService
//import com.wnkx.order.service.*
//import com.wnkx.order.service.core.AsyncTaskService
//import com.wnkx.order.service.core.OrderDataScopeService
//import okhttp3.OkHttpClient
//import org.mockito.MockedStatic
//import org.mockito.Mockito
//import org.springframework.transaction.support.TransactionTemplate
//import spock.lang.Shared
//import spock.lang.Specification
//
//class PayServiceImplTest extends Specification {
//
//
//    PayConfig payConfig = new PayConfig(
//            orderPrefix: '1066',
//            mchntId: '0002900F1503036',
//            mchntKey: 'f00dac5077ea11e754e14c9541bc0170',
//            serverIp: '*******',
//            notifyUrl: 'http://example.com/1',
//            baseUrl: 'https://aipaytest.fuioupay.com/aggregatePay/'
//    )
//    PayConfig payConfig2 = new PayConfig(
//            orderPrefix: '10665',
//            mchntId: '0002900F1503036',
//            mchntKey: 'f00dac5077ea11e754e14c9541bc0170',
//            serverIp: '*******',
//            notifyUrl: 'http://example.com/1',
//            baseUrl: 'https://aipaytest.fuioupay.com/aggregatePay/'
//    )
//    OkHttpClient okHttpClient = new OkHttpClient()
//    RedisService redisService = Mock()
//    ThirdPayLogService thirdPayLogService = Mock()
//    ExchangeRateService exchangeRateService = Mock()
//    TransactionTemplate template = Mock()
//    IOrderService orderService = Mock()
//    IOrderVideoService iOrderVideoService = Mock()
//    IOrderMemberService orderMemberService = Mock()
//    IFyOrderTableService fyOrderTableService = Mock()
//    AsyncTaskService asyncTaskService = Mock()
//    RemoteService remoteService = Mock()
//    OrderPayProperties orderPayProperties = Mock()
//    OrderDataScopeService orderDataScopeService = Mock()
//    WeChatService weChatService = Mock()
//    AlipayServiceImpl alipayServiceImpl = Mock()
//    OrderAnotherPayService orderAnotherPayService = Mock()
//
//    PayServiceImpl payService = new PayServiceImpl(
//            payConfig,
//            okHttpClient,
//            redisService,
//            thirdPayLogService,
//            exchangeRateService,
//            template,
//            orderService,
//            iOrderVideoService,
//            orderMemberService,
//            fyOrderTableService,
//            asyncTaskService,
//            remoteService,
//            orderPayProperties,
//            orderDataScopeService,
//            weChatService,
//            alipayServiceImpl,
//            orderAnotherPayService
//    )
//    PayServiceImpl payService2 = new PayServiceImpl(
//            payConfig2,
//            okHttpClient,
//            redisService,
//            thirdPayLogService,
//            exchangeRateService,
//            template,
//            orderService,
//            iOrderVideoService,
//            orderMemberService,
//            fyOrderTableService,
//            asyncTaskService,
//            remoteService,
//            orderPayProperties,
//            orderDataScopeService,
//            weChatService,
//            alipayServiceImpl,
//            orderAnotherPayService
//    )
//    @Shared
//    MockedStatic securityUtils
//    def setupSpec() {
//        securityUtils = Mockito.mockStatic(SecurityUtils.class)
//    }
//
//    /**
//     * 价格计算与生成
//     */
//    def calculateOrderAmount() {
//        given:
//        Mockito.when(SecurityUtils.getLoginBusinessUser()).thenReturn(getLoginUser())
//        exchangeRateService.getCurrentExchange() >> RealTimeExchangeRateVO.builder().realTimeExchangeRate(BigDecimal.valueOf(7.2349)).isDefault(false).build()
//        payService.calculateOrderAmount(data)
//        expect:
//        data.stream().collect { it.amount }.sum() == expectData
//        where:
//        data                                                             | expectData
//        [new OrderVideoDTO(picCount: 1)]                                 | 322.17
//        [new OrderVideoDTO(picCount: 2)]                                 | 402.04
//        [new OrderVideoDTO(picCount: 1), new OrderVideoDTO(picCount: 2)] | 724.21
//        [new OrderVideoDTO(), new OrderVideoDTO(picCount: 2)]            | 644.33
//    }
//
//    def "订单号转换"() {
//        expect:
//        var ds = payService.convertToOutTradeNo(data)
//        exceptData == ds.substring(0, ds.length() - 3)
//        where:
//        data         | exceptData
//        "DDWN1155"   | "10661155"
//        "DDWN11515"  | "106611515"
//        "DDWN115115" | "1066115115"
//    }
//    def "订单号转换-五位商户号"() {
//        expect:
//        var ds = payService2.convertToOutTradeNo(data)
//        exceptData == ds.substring(0, ds.length() - 3)
//        where:
//        data         | exceptData
//        "DDWN1155"   | "106651155"
//        "DDWN11515"  | "1066511515"
//        "DDWN115115" | "10665115115"
//    }
//
//    def "订单号回转"() {
//        expect:
//        payService.resumeOrderNumber(data) == exceptData
//        where:
//        data                            | exceptData
//        "10662024053114110923870052xsf" | "DDWN2024053114110923870052"
//        "10662024053114110923870052xf"  | "HYWN2024053114110923870052"
//    }
//    def "订单号回转-五位商户号"() {
//        expect:
//        payService2.resumeOrderNumber(data) == exceptData
//        where:
//        data                            | exceptData
//        "106652024053114110923870052xsf" | "DDWN2024053114110923870052"
//        "106652024053114110923870052xf"  | "HYWN2024053114110923870052"
//    }
//
//
//    def "视频订单支付页信息:是否对公：#isPublic（1-否，0-是）, 使用余额：#useBalance，结果：taxPointCost = #taxPointCost，payAmount = #payAmount"() {
//        given:
//        def orderVideo1 = new OrderVideo(videoPrice: 300.0, picPrice: 150.0, commissionPaysTaxes: 100.0, exchangePrice: 100.0, servicePrice: 50.0)
//        def orderVideo2 = new OrderVideo(videoPrice: 300.0, picPrice: 150.0, commissionPaysTaxes: 100.0, exchangePrice: 100.0, servicePrice: 50.0)
//        def order = new Order(currentExchangeRate: 7.2000, orderAmount: 1200.0, payAmount: 1200.0, useBalance: orderBalance, isDefaultExchangeRate: false)
//        iOrderVideoService.selectValidByOrderNum(orderNum) >> [orderVideo1, orderVideo2]
//        orderService.getOrderByOrderNum(orderNum) >> order
//        orderPayProperties.getTaxPoint() >> 5.0
//
//        expect:
//        def info = payService.payInfo(orderNum, isPublic, useBalance, false)
////        info.getTaxPointCost() == taxPointCost
//        info.getPayAmount() == payAmount
//        info.getVideoPrice() == videoPrice
//        info.getPicPrice() == picPrice
//        info.getExchangePrice() == exchangePrice
//        info.getServicePrice() == servicePrice
////        info.getOrderAmount() == orderAmount
//        where:
//        type          | orderNum   | isPublic | useBalance | orderBalance || taxPointCost | payAmount | videoPrice | picPrice | exchangePrice | servicePrice | orderAmount
//        "非对公，不使用余额  " | "DDWN1155" | 1        | null       | 0.0          || 0.0          | 1200.0    | 600.0      | 300.0    | 200.0         | 100.0        | 1200.0
//        "对公，不使用余额"    | "DDWN1155" | 0        | null       | 0.0          || 60.0         | 1200.0    | 600.0      | 300.0    | 200.0         | 100.0        | 1200.0
//        "非对公，使用余额"    | "DDWN1155" | 1        | 500.0      | 0.0          || 0.0          | 700.0     | 600.0      | 300.0    | 200.0         | 100.0        | 1200.0
//        "对公，使用余额"     | "DDWN1155" | 0        | 500.0      | 0.0          || 35.0         | 700.0     | 600.0      | 300.0    | 200.0         | 100.0        | 1200.0
//        "非对公，使用余额"    | "DDWN1155" | 1        | 500.0      | 100.0        || 0.0          | 1100.0    | 600.0      | 300.0    | 200.0         | 100.0        | 1200.0
//    }
//
//    def "会员订单支付页信息:是否对公：#isPublic（1-否，0-是）, 使用余额：#useBalance，结果：taxPointCost = #taxPointCost，payAmount = #payAmount"() {
//        given:
//        def order = new Order(currentExchangeRate: 7.2000, orderAmount: 1200.0, payAmount: 1200.0, useBalance: orderBalance, merchantId: 1L, isDefaultExchangeRate: false)
//        Mockito.when(SecurityUtils.getLoginBusinessUser()).thenReturn(getLoginUser())
//        orderService.getOrderByOrderNum(orderNum) >> order
//        orderPayProperties.getTaxPoint() >> 5.0
//        orderMemberService.checkFirstBuy(_, _) >> false
//
//        expect:
//        def info = payService.payMemberInfo(PayMemberInfoDTO.builder().orderNum(orderNum).isPublic(isPublic).useBalance(useBalance).build(),false)
////        info.getTaxPointCost() == taxPointCost
//        info.getPayAmount() == payAmount
//        where:
//        type          | orderNum   | isPublic | useBalance | orderBalance || taxPointCost | payAmount | seedCode
//        "非对公，不使用余额  " | "DDWN1155" | 1        | null       | 0.0          || 0.0          | 1200.0 | ""
//        "对公，不使用余额"    | "DDWN1155" | 0        | null       | 0.0          || 60.0         | 1200.0 | ""
//        "非对公，使用余额"    | "DDWN1155" | 1        | 500.0      | 0.0          || 0.0          | 700.0 | ""
//        "对公，使用余额"     | "DDWN1155" | 0        | 500.0      | 0.0          || 35.0         | 700.0 | ""
//        "非对公，使用余额"    | "DDWN1155" | 1        | 500.0      | 100.0        || 0.0          | 1100.0 | ""
//    }
//
////    def "汇率抓取"() {
////        expect:
////        println new ExchangeRateServiceImpl(Mock(RedisService), new OkHttpClient()).getRealTimeExchangeRate()
////    }
//
//    def "货币单位转换: 美元: #data  结果： #exceptData"() {
//        given:
//        List<BigDecimal> list = new ArrayList<>();
//        list.addAll(data)
//        exchangeRateService.getCurrentExchange() >> RealTimeExchangeRateVO.builder().realTimeExchangeRate(BigDecimal.valueOf(10.0)).isDefault(false).build()
//
//        expect:
//        def rmb = payService.dollarToRmb(list)
//        rmb.getRMB() == exceptData
//        where:
//        data       | exceptData
//        [4.0, 6.0] | 100.0
//        [5.0, 7.0] | 120.0
//    }
//
//
//    def getLoginUser() {
//        def loginBusiness = new LoginBusiness()
//        def businessAccountVO = new BusinessAccountVO()
//        def businessVo = new BusinessVO()
//        businessVo.id = 1002L
//        businessVo.memberCode = "WBMG"
//        businessVo.isProxy = 1
//        businessAccountVO.businessId = 1L
//        businessAccountVO.businessVO = businessVo
//        loginBusiness.businessAccountVO = businessAccountVO
//        return loginBusiness
//    }
//
//    def cleanupSpec() {
//        securityUtils.close()
//    }
//}
