//package com.wnkx.order.service.impl
//
//import com.ruoyi.common.core.enums.OrderMemberStatusEnum
//import com.ruoyi.common.core.exception.ServiceException
//import com.ruoyi.common.core.utils.SpringUtils
//import com.ruoyi.common.redis.service.RedisService
//import com.ruoyi.common.security.utils.SecurityUtils
//import com.ruoyi.system.api.domain.dto.order.CreateOrderMemberDTO
//import com.ruoyi.system.api.domain.dto.order.FlowOrderMemberDTO
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO
//import com.ruoyi.system.api.domain.vo.order.OrderMemberVO
//import com.ruoyi.system.api.domain.vo.order.RealTimeExchangeRateVO
//import com.ruoyi.system.api.model.LoginBusiness
//import com.ruoyi.system.api.config.OrderPayProperties
//import com.wnkx.order.mapper.OrderMapper
//import com.wnkx.order.mapper.OrderNoteMapper
//import com.wnkx.order.remote.RemoteService
//import com.wnkx.order.service.ExchangeRateService
//import com.wnkx.order.service.IOrderInvoiceService
//import com.wnkx.order.service.IOrderMemberService
//import com.wnkx.order.service.IOrderService
//import com.wnkx.order.service.core.AsyncTaskService
//import com.wnkx.order.service.core.SystemCore
//import org.mockito.MockedStatic
//import org.mockito.Mockito
//import spock.lang.Shared
//import spock.lang.Specification
//
//class OrderMemberServiceImplTest extends Specification {
//
//    ExchangeRateService exchangeRateService = Mock();
//    RedisService redisService = Mock();
//    OrderMapper orderMapper = Mock();
//    OrderNoteMapper orderNoteMapper = Mock();
//    RemoteService remoteService = Mock();
//    IOrderInvoiceService orderInvoiceService = Mock();
//    IOrderMemberService self = Mock(IOrderMemberService.class);
//    AsyncTaskService asyncTaskService = Mock()
//    OrderPayProperties orderPayProperties = Mock()
//    SystemCore systemCore = Mock()
//
//    OrderMemberServiceImpl orderMemberService = new OrderMemberServiceImpl(
//            exchangeRateService,
//            redisService,
//            orderMapper,
//            orderNoteMapper,
//            remoteService,
//            orderInvoiceService,
//            self,
//            asyncTaskService,
//            orderPayProperties,
//            systemCore
//    )
//    @Shared
//    MockedStatic securityUtils
//    @Shared
//    MockedStatic springUtils
//
//    def setupSpec() {
//        securityUtils = Mockito.mockStatic(SecurityUtils.class)
//        springUtils = Mockito.mockStatic(SpringUtils.class)
//    }
//
//    def "创建订单"() {
//        given: "设置请求参数"
//        def createOrderMemberDTO = new CreateOrderMemberDTO(packageType);
//        exchangeRateService.getCurrentExchange() >> RealTimeExchangeRateVO.builder().realTimeExchangeRate(BigDecimal.valueOf(7.2473)).isDefault(false).build()
//
//        def orderService = Mockito.mock(OrderServiceImpl.class)
//
//        Mockito.when(SecurityUtils.getUserId()).thenReturn(1002L)
//        Mockito.when(SecurityUtils.getLoginBusinessUser()).thenReturn(getLoginUser())
//        Mockito.when(SecurityUtils.getUsername()).thenReturn("皮皮鹏")
//        Mockito.when(SpringUtils.getBean(IOrderService.class)).thenReturn(orderService)
//
//        expect:
//        def order = orderMemberService.createOrder(createOrderMemberDTO)
//        orderAmount == order.getPayAmount()
//        where:
//        packageType | orderAmount
//        0           | 867.5
//        1           | 1730.65
//        2           | 4670.16
//    }
//
//    def "检查会员订单状态（异常测试） checkMemberStatus():订单状态:#orderMemberCode，要求状态:#orderMemberStatus，抛出异常：#expectedMessage"() {
//        when:
//        orderMemberService.checkMemberStatus(getOrderMemberVO(orderMemberCode), orderMemberStatus)
//
//        then:
//        def exception = thrown(ServiceException)
//        exception.message == expectedMessage
//        where:
//        orderMemberCode | orderMemberStatus                | expectedMessage
//        1               | OrderMemberStatusEnum.UN_MATCH   | "会员订单状态异常，需要是：[交易关闭]，当前是：[待支付]，请刷新页面"
//        1               | OrderMemberStatusEnum.UN_CHECK   | "会员订单状态异常，需要是：[待审核]，当前是：[待支付]，请刷新页面"
//        1               | OrderMemberStatusEnum.UN_CONFIRM | "会员订单状态异常，需要是：[交易成功]，当前是：[待支付]，请刷新页面"
//    }
//
//    def "检查会员订单状态（成功测试） checkMemberStatus():订单状态:#orderMemberCode，要求状态:#orderMemberStatus"() {
//        expect:
//        orderMemberService.checkMemberStatus(getOrderMemberVO(orderMemberCode), orderMemberStatus)
//        if (orderMemberCode == OrderMemberStatusEnum.UN_PAY.code || orderMemberCode == OrderMemberStatusEnum.UN_CONFIRM.code) {
//            orderMemberService.checkMemberStatus(getOrderMemberVO(orderMemberCode), OrderMemberStatusEnum.UN_PAY, OrderMemberStatusEnum.UN_CONFIRM)
//        }
//
//        where:
//        orderMemberCode | orderMemberStatus
//        4               | OrderMemberStatusEnum.UN_MATCH
//        2               | OrderMemberStatusEnum.UN_CHECK
//        3               | OrderMemberStatusEnum.UN_CONFIRM
//        1               | OrderMemberStatusEnum.UN_PAY
//    }
//
//    def "订单状态流转flowOrderMember()：订单状态:#orderMemberCode，流转状态:#orderMemberStatus，抛出异常：#expectedMessage"() {
//        given: "设置请求参数"
//        Mockito.when(SecurityUtils.getUserId()).thenReturn(1002L)
//        Mockito.when(SecurityUtils.getLoginBusinessUser()).thenReturn(getLoginUser())
//        Mockito.when(SecurityUtils.getUsername()).thenReturn("皮皮鹏")
//        Mockito.when(SpringUtils.getBean(IOrderService.class)).thenReturn(Mock(OrderServiceImpl.class))
//        def flowOrderMemberDto = new FlowOrderMemberDTO();
//        flowOrderMemberDto.setOrderMemberStatusEnum(orderMemberStatus);
//        flowOrderMemberDto.setOrderNum(orderNum);
//        flowOrderMemberDto.setUseBalance(useBalance);
//
//
//        redisService.getLock(_, _) >> true
//        redisService.releaseLock(_) >> 1L
//        /**
//         * OrderMemberVO：
//         *      1. == null  报错
//         *      2. orderMember.status = 2(未审核)，flowOrderMemberDto.orderMemberStatusEnum.code = 2(未审核) 报错
//         *      3. orderMember.status = 3(交易成功)， 报错
//         *      4. orderMember.status = 4(交易关闭)， 报错
//         */
//        self.getOrderMember(orderNum) >> getOrderMemberVO(orderMemberCode)
//        remoteService.getBusinessAccountByAccountId(_) >> getBusinessAccountVO()
//        when:
//        orderMemberService.flowOrderMember(flowOrderMemberDto)
//        then:
//        def exception = thrown(Exception)
//        exception.message == expectedMessage
//        where:
//        orderMemberCode | orderMemberStatus                | useBalance | orderNum                     | expectedMessage
//        0               | OrderMemberStatusEnum.UN_MATCH   | 100.0      | "DDWN2024070110531021468160" | "订单信息不存在！"
//        1               | OrderMemberStatusEnum.UN_PAY     | 100.0      | "DDWN2024070110531021468160" | "状态无法转换为待支付，请刷新页面"
//        2               | OrderMemberStatusEnum.UN_PAY     | 100.0      | "DDWN2024070110531021468160" | "状态无法转换为待支付，请刷新页面"
//        2               | OrderMemberStatusEnum.UN_CHECK   | 100.0      | "DDWN2024070110531021468160" | "待审核的数据无法流转为待审核状态"
//        3               | OrderMemberStatusEnum.UN_PAY     | 100.0      | "DDWN2024070110531021468160" | "交易成功的数据无法流转为待支付，请刷新页面"
//        3               | OrderMemberStatusEnum.UN_CHECK   | 100.0      | "DDWN2024070110531021468160" | "交易成功的数据无法流转为待审核，请刷新页面"
//        3               | OrderMemberStatusEnum.UN_CONFIRM | 100.0      | "DDWN2024070110531021468160" | "交易成功的数据无法流转为交易成功，请刷新页面"
//        3               | OrderMemberStatusEnum.UN_MATCH   | 100.0      | "DDWN2024070110531021468160" | "交易成功的数据无法流转为交易关闭，请刷新页面"
//        4               | OrderMemberStatusEnum.UN_PAY     | 100.0      | "DDWN2024070110531021468160" | "交易关闭的数据无法流转为待支付，请刷新页面"
//        4               | OrderMemberStatusEnum.UN_CHECK   | 100.0      | "DDWN2024070110531021468160" | "交易关闭的数据无法流转为待审核，请刷新页面"
//        4               | OrderMemberStatusEnum.UN_CONFIRM | 100.0      | "DDWN2024070110531021468160" | "交易关闭的数据无法流转为交易成功，请刷新页面"
//        4               | OrderMemberStatusEnum.UN_MATCH   | 100.0      | "DDWN2024070110531021468160" | "交易关闭的数据无法流转为交易关闭，请刷新页面"
//    }
//
////    def "基础修改方法："() {
////        given:
////        def lambdaUpdate = Mockito.mock(LambdaUpdateChainWrapper<OrderMember>.class)
////        def orderMemberDto = new OrderMemberDTO()
////        orderMemberDto.setId(id);
////        orderMemberDto.setOrderNum(orderNum);
////        orderMemberDto.setStatus(1);
////        self.lambdaUpdate() >> lambdaUpdate
////
////        when:
////        orderMemberService.update(orderMemberDto)
////
////        then:
////        def exception = thrown(Exception)
////        exception.message == message
////        where:
////        id   | orderNum    | message
////        1    | "DDWN12138" | null
////        1    | null        | null
////        null | null        | "修改会员表数据，修改条件不能都为空"
////    }
//
//    def getOrderMemberVO(orderMemberCode) {
//        def orderMember = new OrderMemberVO();
//        if (orderMemberCode == 0) {
//            return null;
//        }
//        orderMember.status = orderMemberCode
//        orderMember.orderUserId = 1L
//        return orderMember
//    }
//
//    def getLoginUser() {
//        def loginBusiness = new LoginBusiness()
//        def businessAccountVO = new BusinessAccountVO()
//        def businessVo = new BusinessVO()
//        businessVo.id = 1002L
//        businessVo.memberCode = "WBMG"
//        businessAccountVO.businessVO = businessVo
//        loginBusiness.businessAccountVO = businessAccountVO
//        return loginBusiness
//    }
//
//    def getBusinessAccountVO() {
//        def businessAccountVO = new BusinessAccountVO();
//        def businessVo = new BusinessVO()
//        businessVo.memberValidity = new Date();
//        businessAccountVO.businessVO = businessVo
//        return businessAccountVO
//    }
//
//    def cleanupSpec() {
//        securityUtils.close()
//        springUtils.close()
//    }
//}
