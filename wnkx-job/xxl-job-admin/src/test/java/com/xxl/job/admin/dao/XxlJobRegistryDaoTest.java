package com.xxl.job.admin.dao;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class XxlJobRegistryDaoTest {

    @Resource
    private XxlJobRegistryDao xxlJobRegistryDao;

    @Test
    void test(){
        int ret = xxlJobRegistryDao.registryUpdate("g1", "k1", "v1", new Date());
        if (ret < 1) {
            xxlJobRegistryDao.registrySave("g1", "k1", "v1", new Date());
        }

        xxlJobRegistryDao.findAll(1, new Date());

        xxlJobRegistryDao.removeDead(Arrays.asList(1));
    }

}
