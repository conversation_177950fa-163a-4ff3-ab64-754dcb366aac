<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.statistics.mapper.BusinessMemberDataStatisticsMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.statistics.BusinessMemberDataStatistics">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="recordTime" column="record_time" jdbcType="TIMESTAMP"/>
            <result property="memberCount" column="member_count" jdbcType="INTEGER"/>
            <result property="renewMemberCount" column="renew_member_count" jdbcType="INTEGER"/>
            <result property="exitMemberCount" column="exit_member_count" jdbcType="INTEGER"/>
            <result property="expireMemberCount" column="expire_member_count" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,record_time,member_count,
        renew_member_count,exit_member_count,expire_member_count,
        create_time,update_time
    </sql>
    <select id="getBusinessMemberTypeAnalysis" resultType="com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO">
        SELECT
            CASE
                WHEN recharge_count = 1 THEN '${@com.ruoyi.common.core.enums.statistics.BusinessMemberTypeLabelEnum@NEW_MEMBER.getLabel}'
                WHEN recharge_count = 2 THEN '${@com.ruoyi.common.core.enums.statistics.BusinessMemberTypeLabelEnum@FIRST_RENEWAL.getLabel}'
                WHEN recharge_count > 2 THEN '${@com.ruoyi.common.core.enums.statistics.BusinessMemberTypeLabelEnum@MULTIPLE_RENEWAL.getLabel}'
            END AS label,
            COUNT(id) AS count
        FROM
            business
        WHERE
            member_type = ${@<EMAIL>} and recharge_count > 0
        GROUP BY
            label;
    </select>
    <select id="getBusinessExitAnalysis" resultType="com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO">
        SELECT
            CASE
                WHEN change_reason_type = ${@com.ruoyi.common.core.enums.BusinessValidityChangeReasonTypeEnum@SEVEN_DAYS_WITHOUT_REASON.getCode} THEN '${@com.ruoyi.common.core.enums.statistics.BusinessExitEnum@SEVEN_DAYS_NO_REASON.getLabel}'
                WHEN change_reason_type = ${@com.ruoyi.common.core.enums.BusinessValidityChangeReasonTypeEnum@EXIT_MEMBER.getCode} THEN '${@com.ruoyi.common.core.enums.statistics.BusinessExitEnum@NON_SEVEN_DAYS.getLabel}'
                END AS label,
            COUNT(id) AS count
        FROM
            business_member_validity_flow
        <where>
            change_reason_type IN (${@com.ruoyi.common.core.enums.BusinessValidityChangeReasonTypeEnum@SEVEN_DAYS_WITHOUT_REASON.getCode}, ${@com.ruoyi.common.core.enums.BusinessValidityChangeReasonTypeEnum@EXIT_MEMBER.getCode})
            <if test="from != null and to != null">
                and create_time BETWEEN #{from} AND #{to}
            </if>
        </where>
        GROUP BY
            label;
    </select>

    <select id="getBusinessSourceAnalysis" resultType="com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO">
        SELECT
        CASE
        WHEN dc.channel_type = ${@<EMAIL>} THEN '${@<EMAIL>}'
        WHEN dc.channel_type  = ${@<EMAIL>} THEN '${@<EMAIL>}'
        ELSE '${@<EMAIL>}'
        END AS label,
        COUNT(b.id) AS count
        FROM
        business b
        left join distribution_channel dc on b.seed_code = dc.seed_code
        <where>
            b.recharge_count &gt;= 1
            <if test="from != null and to != null">
                and b.create_time BETWEEN #{from} AND #{to}
            </if>
        </where>
        GROUP BY
        label;
    </select>

    <select id="getMemberTrendVO" resultType="com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberTrendVO">
        SELECT
        DATE_FORMAT(record_time, ${dateFormat}) AS recordTime,
        SUM(member_count) AS memberCount,
        SUM(renew_member_count) AS renewMemberCount,
        SUM(exit_member_count) AS exitMemberCount,
        SUM(expire_member_count) AS expireMemberCount
        FROM business_member_data_statistics
        <where>
            <if test="from != null and to != null">
                and record_time BETWEEN #{from} AND #{to}
            </if>
        </where>
        group by DATE_FORMAT(record_time, ${dateFormat})
    </select>

    <select id="getYesterdayMemberRechargeCountVO" resultType="com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberRechargeCountVO">
        SELECT
            COUNT(DISTINCT CASE WHEN f.recharge_count = 1 THEN f.business_id ELSE NULL END) AS yesterdayMemberCount,
            SUM(CASE WHEN f.recharge_count > 1 THEN 1 ELSE 0 END ) AS yesterdayRenewMemberCount
        from business_member_validity_flow f
        <where>
            f.type = ${@<EMAIL>}
            and DATE(f.create_time) = DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
        </where>
    </select>
    <select id="getOverMemberCount" resultType="com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberExpireCountVO">
        SELECT
            SUM(CASE WHEN member_status = ${@<EMAIL>} THEN 1 ELSE 0 END ) as overMemberCount,
            SUM(CASE WHEN member_status = ${@com.ruoyi.common.core.enums.MemberTypeEnum@NO_EXPIRE.getcode} THEN 1 ELSE 0 END ) as noExpireMemberCount,
            SUM(CASE WHEN member_status = ${@<EMAIL>} and DATE(member_validity) = DATE(DATE_SUB(CURDATE(), INTERVAL 1 DAY)) THEN 1 ELSE 0 END ) as yesterdayExpireMemberCount
        FROM
            `business`
    </select>

    <select id="getExitMemberCount" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT business_id)
        FROM business_member_validity_flow
        <where>
            change_reason_type in(${@com.ruoyi.common.core.enums.BusinessValidityChangeReasonTypeEnum@SEVEN_DAYS_WITHOUT_REASON.getCode},
             ${@com.ruoyi.common.core.enums.BusinessValidityChangeReasonTypeEnum@EXIT_MEMBER.getCode})
            <if test="from != null and to != null">
                and create_time BETWEEN #{from} AND #{to}
            </if>
        </where>
        GROUP BY DATE(create_time);
    </select>
</mapper>
