<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.statistics.mapper.BusinessOrderDataStatisticsMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.statistics.BusinessOrderDataStatistics">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="recordTime" column="record_time" jdbcType="TIMESTAMP"/>
            <result property="businessId" column="business_id" jdbcType="BIGINT"/>
            <result property="businessMemberType" column="business_member_type" jdbcType="BOOLEAN"/>
            <result property="videoCount" column="video_count" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,record_time,business_id,
        business_member_type,video_count,create_time,
        update_time
    </sql>

    <select id="getBusinessOrderAnalysisVO" resultType="com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO">
        SELECT
        CASE
        WHEN total_orders = 0 or total_orders is null THEN '${@com.ruoyi.common.core.enums.statistics.BusinessOrderLabelEnum@NO_ORDER.getLabel}'
        WHEN total_orders &lt;= 4 THEN '${@com.ruoyi.common.core.enums.statistics.BusinessOrderLabelEnum@WITHIN_5.getLabel}'
        WHEN total_orders &lt;= 9 THEN '${@com.ruoyi.common.core.enums.statistics.BusinessOrderLabelEnum@BETWEEN_5_9.getLabel}'
        WHEN total_orders &lt;= 19 THEN '${@com.ruoyi.common.core.enums.statistics.BusinessOrderLabelEnum@BETWEEN_10_19.getLabel}'
        WHEN total_orders &lt;= 49 THEN '${@com.ruoyi.common.core.enums.statistics.BusinessOrderLabelEnum@BETWEEN_20_49.getLabel}'
        ELSE '${@com.ruoyi.common.core.enums.statistics.BusinessOrderLabelEnum@OVER_50.getLabel}'
        END AS label,
        COUNT(b.id) AS count
        FROM business b
        left join (
        SELECT
        business_id,
        SUM(video_count) AS total_orders
        FROM business_order_data_statistics
        <where>
            <if test="from != null and to != null">
                and record_time BETWEEN #{from} AND #{to}
            </if>
        </where>
        GROUP BY business_id
        ) AS businessOrderData on b.id = businessOrderData.business_id
        <where>
            <if test="from != null and to != null">
                and b.member_validity &gt;= #{from}
            </if>
        </where>
        GROUP BY label;

    </select>

    <select id="getMemberTypeOrderCountVO" resultType="com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberTypeOrderCountVO">
        select
            DATE_FORMAT(record_time, ${dateFormat}) AS recordTime,
            SUM(CASE WHEN business_member_type = 1 THEN video_count ELSE 0 END) AS newMemberOrderCount,
            SUM(CASE WHEN business_member_type = 2 THEN video_count ELSE 0 END) AS oldMemberOrderCount
        from business_order_data_statistics
        <where>
            <if test="from != null and to != null">
                and record_time BETWEEN #{from} AND #{to}
            </if>
        </where>
        group by DATE_FORMAT(record_time, ${dateFormat})
    </select>
</mapper>
