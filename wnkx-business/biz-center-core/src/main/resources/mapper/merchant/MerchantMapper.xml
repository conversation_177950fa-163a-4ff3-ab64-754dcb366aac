<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.merchant.mapper.MerchantMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.Merchant" id="MerchantResult">
        <result property="merchantId"    column="merchant_id"    />
        <result property="merchantName"    column="merchant_name"    />
        <result property="phonenumber"    column="phonenumber"    />
        <result property="sex"    column="sex"    />
        <result property="dataScope"    column="data_scope"    />
        <result property="avatar"    column="avatar"    />
        <result property="password"    column="password"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="loginIp"    column="login_ip"    />
        <result property="loginDate"    column="login_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMerchantVo">
        select merchant_id, merchant_name,merchant_code, phonenumber, sex, data_scope, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark from merchant
    </sql>

    <select id="selectMerchantList" parameterType="com.ruoyi.system.api.domain.entity.Merchant" resultType="com.ruoyi.system.api.domain.vo.MerchantVO">
        <include refid="selectMerchantVo"/>
        <where>
            <if test="merchantName != null  and merchantName != ''"> and merchant_name like concat('%', #{merchantName}, '%')</if>
            <if test="phonenumber != null  and phonenumber != ''"> and phonenumber = #{phonenumber}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="dataScope != null  and dataScope != ''"> and data_scope = #{dataScope}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="loginIp != null  and loginIp != ''"> and login_ip = #{loginIp}</if>
            <if test="loginDate != null "> and login_date = #{loginDate}</if>
        </where>
    </select>

    <select id="selectMerchantByMerchantId" parameterType="Long" resultMap="MerchantResult">
        <include refid="selectMerchantVo"/>
        where merchant_id = #{merchantId}
    </select>

    <insert id="insertMerchant" parameterType="com.ruoyi.system.api.domain.entity.Merchant" useGeneratedKeys="true" keyProperty="merchantId">
        insert into merchant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="merchantName != null and merchantName != ''">merchant_name,</if>
            <if test="phonenumber != null">phonenumber,</if>
            <if test="sex != null">sex,</if>
            <if test="dataScope != null">data_scope,</if>
            <if test="avatar != null">avatar,</if>
            <if test="password != null">password,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="merchantName != null and merchantName != ''">#{merchantName},</if>
            <if test="phonenumber != null">#{phonenumber},</if>
            <if test="sex != null">#{sex},</if>
            <if test="dataScope != null">#{dataScope},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="password != null">#{password},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateMerchant" parameterType="com.ruoyi.system.api.domain.entity.Merchant">
        update merchant
        <trim prefix="SET" suffixOverrides=",">
            <if test="merchantName != null and merchantName != ''">merchant_name = #{merchantName},</if>
            <if test="phonenumber != null">phonenumber = #{phonenumber},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="dataScope != null">data_scope = #{dataScope},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="password != null">password = #{password},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where merchant_id = #{merchantId}
    </update>

    <delete id="deleteMerchantByMerchantId" parameterType="Long">
        delete from merchant where merchant_id = #{merchantId}
    </delete>

    <delete id="deleteMerchantByMerchantIds" parameterType="String">
        delete from merchant where merchant_id in
        <foreach item="merchantId" collection="array" open="(" separator="," close=")">
            #{merchantId}
        </foreach>
    </delete>
</mapper>