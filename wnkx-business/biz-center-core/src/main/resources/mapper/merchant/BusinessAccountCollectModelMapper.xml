<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessAccountCollectModelMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountCollectModel" id="MerchantCollectResult">
        <result property="id"    column="id"    />
        <result property="bizUserId"    column="biz_user_id"    />
        <result property="modelId"    column="model_id"    />
    </resultMap>

    <sql id="selectMerchantCollectVo">
        select id, biz_user_id, model_id from business_account_collect_model
    </sql>

    <select id="selectMerchantCollectList" parameterType="com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountCollectModel" resultMap="MerchantCollectResult">
        <include refid="selectMerchantCollectVo"/>
        <where>
            <if test="merchantId != null "> and biz_user_id = #{merchantId}</if>
            <if test="modelId != null "> and model_id = #{modelId}</if>
        </where>
    </select>

    <select id="selectMerchantCollectById" parameterType="Long" resultMap="MerchantCollectResult">
        <include refid="selectMerchantCollectVo"/>
        where id = #{id}
    </select>

    <insert id="insertMerchantCollect" parameterType="com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountCollectModel">
        insert into business_account_collect_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="merchantId != null">biz_user_id,</if>
            <if test="modelId != null">model_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="merchantId != null">#{merchantId},</if>
            <if test="modelId != null">#{modelId},</if>
        </trim>
    </insert>

    <update id="updateMerchantCollect" parameterType="com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountCollectModel">
        update business_account_collect_model
        <trim prefix="SET" suffixOverrides=",">
            <if test="merchantId != null">biz_user_id = #{merchantId},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMerchantCollectById" parameterType="Long">
        delete from business_account_collect_model where id = #{id}
    </delete>

    <delete id="deleteMerchantCollectByIds" parameterType="String">
        delete from business_account_collect_model where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>