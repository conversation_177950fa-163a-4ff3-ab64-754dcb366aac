<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessAccountModelLibraryMapper">

<!--    查询商家端模特信息列表-->
    <select id="selectBusinessAccountModelListByCondition" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountCollectModelVO">
        SELECT
        m.id,
        ma.account,
        m.model_pic,
        m.cooperation,
        m.live_pic as live_pic_id,
        m.amazon_video AS amazon_video_id,
        m.tiktok_video AS tiktok_video_id,
        m.`name`,
        m.platform,
        m.type,
        m.age,
        m.sex,
        m.nation,
        m.age_group,
        m.`status`,
        (CASE WHEN m.cooperation = 1 THEN 3
        WHEN m.cooperation = 2 THEN 2
        WHEN m.cooperation = 0 THEN 1 else 0 end) AS cooperationSort,
        IF( bacm.biz_user_id IS NOT NULL, TRUE, FALSE ) AS collect
        FROM
        model m
                LEFT JOIN business_account_collect_model bacm ON m.id = bacm.model_id AND bacm.biz_user_id = #{bizUserId}
                LEFT JOIN model_account ma ON ma.model_id = m.id
        <where>
                    m.is_show = 1
                    <if test="dto.needAllModel != null and dto.needAllModel == 0">
                        m.`status` = '0'
                    </if>
                    <if test="dto.id !=null and dto.id.size() != 0">
                        AND m.id in
                        <foreach item="item" index="index" collection="dto.id" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                    <if test="dto.modelAccount !=null and dto.modelAccount != ''">
                        AND ma.account LIKE concat('%', #{dto.modelAccount}, '%')
                    </if>

                    <if test="dto.name !=null and dto.name != ''">
                        AND m.name LIKE concat('%', #{dto.name}, '%')
                    </if>

                    <if test="dto.keyword != null and dto.keyword != ''">
                        AND (
                              m.`name` LIKE concat('%', #{dto.keyword}, '%')
                              OR ma.account LIKE concat('%', #{dto.keyword}, '%')
                              OR m.platform_dict LIKE concat('%', #{dto.keyword}, '%')
                              OR m.type_dict LIKE concat('%', #{dto.keyword}, '%')
                              OR m.nation_dict LIKE concat('%', #{dto.keyword}, '%')
                              OR m.sex_dict LIKE concat('%', #{dto.keyword}, '%')
                              OR m.id IN (
                                      SELECT
                                          mt.model_id
                                      FROM
                                          model_tag mt
                                      LEFT JOIN tag t ON mt.dict_id = t.id
                                      WHERE
                                          ( t.category_id IN (#{modelCategoryId}, #{modelTagId})
                                          AND t.`name` LIKE concat('%', #{dto.keyword}, '%') )
                                            OR mt.dict_name LIKE concat('%', #{dto.keyword}, '%')
                                        )
                              OR m.id IN (
                                        SELECT
                                            mvr.model_id
                                        FROM
                                            model_video_resource mvr
                                        WHERE mvr.name LIKE concat('%', #{dto.keyword}, '%')
                                        )
                            )
                    </if>

                    <if test="dto.platform !=null and dto.platform.size() != 0">
                        AND (
                        <foreach item="item" index="index" collection="dto.platform" open="" separator=" OR " close="">
                            FIND_IN_SET(#{item}, m.platform)
                        </foreach>
                        )
                    </if>

                    <if test="dto.nation !=null and dto.nation.size() != 0">
                        AND (
                        <foreach item="item" index="index" collection="dto.nation" open="" separator=" OR " close="">
                            FIND_IN_SET(#{item}, m.nation)
                        </foreach>
                        )
                    </if>

                    <if test="dto.type !=null and dto.type.size() != 0">
                        AND (
                        <foreach item="item" index="index" collection="dto.type" open="" separator=" OR " close="">
                            FIND_IN_SET(#{item}, m.type)
                        </foreach>
                        )
                    </if>

                    <if test="dto.sex !=null and dto.sex.size() != 0">
                        AND (
                        <foreach item="item" index="index" collection="dto.sex" open="" separator=" OR " close="">
                            FIND_IN_SET(#{item}, m.sex)
                        </foreach>
                        )
                    </if>

                    <if test="dto.ageGroup !=null and dto.ageGroup.size()!=0">
                        AND m.age_group in
                        <foreach item="item" index="index" collection="dto.ageGroup" open="(" separator="," close=")">
                          #{item}
                        </foreach>
                    </if>

                    <if test="dto.specialtyCategory !=null and dto.specialtyCategory.size() != 0">
                        AND m.id IN (
                                      SELECT
                                          mt.model_id
                                      FROM
                                          model_tag mt
                                      WHERE
                                          mt.dict_id IN
                                          <foreach item="item" index="index" collection="dto.specialtyCategory" open="(" separator="," close=")">
                                              #{item}
                                          </foreach>
                                  )
                    </if>

                    <if test="dto.modelTag !=null and dto.modelTag.size() != 0">
                        AND m.id IN (
                                      SELECT
                                          mt.model_id
                                      FROM
                                          model_tag mt
                                      WHERE
                                          mt.dict_id IN
                                          <foreach item="item" index="index" collection="dto.modelTag" open="(" separator="," close=")">
                                              #{item}
                                          </foreach>
                                  )
                    </if>

                    <if test="dto.cannotModel !=null and dto.cannotModel.size() != 0">
                        AND m.id NOT IN
                        <foreach item="item" index="index" collection="dto.cannotModel" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                </where>
        ORDER BY
            <choose>
                <when test="dto.isIndex != null and dto.isIndex == 2">
                    m.status asc,m.id ASC
                </when>
                <otherwise>
                    m.status_sort,
                    m.cooperation_sort DESC,
                    m.update_time DESC,
                    m.id ASC
                </otherwise>
            </choose>
    </select>
    <select id="selectBusinessAccountCollectModelListByCondition" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountCollectModelVO">
        SELECT
            m.id,
            ma.account,
            m.model_pic,
            m.live_pic as live_pic_id,
            m.amazon_video AS amazon_video_id,
            m.tiktok_video AS tiktok_video_id,
            m.`name`,
            m.platform,
            m.type,
            m.age,
            m.sex,
            m.`status`,
            m.nation,
            m.age_group,
            TRUE AS collect
        FROM
            business_account_collect_model bacm
                JOIN model m ON m.id = bacm.model_id
                LEFT JOIN model_account ma ON ma.model_id = m.id
        WHERE
            bacm.biz_user_id = #{bizUserId}
            <if test="dto.needAllModel != null and dto.needAllModel == 0">
            and m.`status` = '0'
            </if>
            <if test="dto.keyword != null and dto.keyword != ''">
                AND (
                    m.`name` LIKE concat('%', #{dto.keyword}, '%')
                    OR ma.account LIKE concat('%', #{dto.keyword}, '%')
                    OR m.platform_dict LIKE concat('%', #{dto.keyword}, '%')
                    OR m.type_dict LIKE concat('%', #{dto.keyword}, '%')
                    OR m.nation_dict LIKE concat('%', #{dto.keyword}, '%')
                    OR m.sex_dict LIKE concat('%', #{dto.keyword}, '%')
                    OR m.id IN (
                        SELECT
                            mt.model_id
                        FROM
                            model_tag mt
                            LEFT JOIN tag t ON mt.dict_id = t.id
                        WHERE
                            t.category_id IN (#{modelCategoryId}, #{modelTagId})
                            AND t.`name` LIKE concat('%', #{dto.keyword}, '%')
                    )
                )
            </if>

            <if test="dto.type !=null and dto.type.size() != 0">
                AND (
                <foreach item="item" index="index" collection="dto.type" open="" separator=" OR " close="">
                    FIND_IN_SET(#{item}, m.type)
                </foreach>
                )
            </if>


            <if test="dto.nation !=null and dto.nation.size() != 0">
                AND (
                <foreach item="item" index="index" collection="dto.nation" open="" separator=" OR " close="">
                    FIND_IN_SET(#{item}, m.nation)
                </foreach>
                )
            </if>

            <if test="dto.name !=null and dto.name != ''">
                AND m.name LIKE concat('%', #{dto.name}, '%')
            </if>

            <if test="dto.modelAccount !=null and dto.modelAccount != ''">
                AND ma.account LIKE concat('%', #{dto.modelAccount}, '%')
            </if>


            <if test="dto.sex !=null and dto.sex.size() != 0">
                AND (
                <foreach item="item" index="index" collection="dto.sex" open="" separator=" OR " close="">
                    FIND_IN_SET(#{item}, m.sex)
                </foreach>
                )
            </if>

            <if test="dto.ageGroup !=null and dto.ageGroup.size()!=0">
                AND m.age_group in
                <foreach item="item" index="index" collection="dto.ageGroup" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.specialtyCategory !=null and dto.specialtyCategory.size() != 0">
                AND m.id IN (
                    SELECT
                        mt.model_id
                    FROM
                        model_tag mt
                    WHERE
                        mt.dict_id IN
                        <foreach item="item" index="index" collection="dto.specialtyCategory" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                )
            </if>

            <if test="dto.modelTag !=null and dto.modelTag.size() != 0">
                AND m.id IN (
                    SELECT
                        mt.model_id
                    FROM
                        model_tag mt
                    WHERE
                        mt.dict_id IN
                        <foreach item="item" index="index" collection="dto.modelTag" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                )
            </if>
    </select>
    <select id="selectModelStatusByModelIds"
            resultType="java.lang.Long">
        SELECT
            m.id
        FROM model_account ma
        LEFT JOIN model m ON  m.id = ma.model_id
        <where>
            m.status != 0
            <if test="dto.id != null and dto.id.size() > 0">
                AND ma.model_id IN
                <foreach item="item" index="index" collection="dto.id" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>