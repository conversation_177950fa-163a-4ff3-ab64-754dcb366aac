<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.logistic.mapper.LogisticInfoLegacyMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.LogisticInfoLegacy">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="number" column="number" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="mainStatus" column="main_status" jdbcType="VARCHAR"/>
            <result property="subStatus" column="sub_status" jdbcType="VARCHAR"/>
            <result property="curTime" column="cur_time" jdbcType="TIMESTAMP"/>
            <result property="country" column="country" jdbcType="VARCHAR"/>
            <result property="state" column="state" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
            <result property="street" column="street" jdbcType="VARCHAR"/>
            <result property="longitude" column="longitude" jdbcType="VARCHAR"/>
            <result property="latitude" column="latitude" jdbcType="VARCHAR"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,number,description,
        main_status,sub_status,cur_time,
        country,state,city,
        street,longitude,latitude,
        location,create_time
    </sql>
</mapper>
