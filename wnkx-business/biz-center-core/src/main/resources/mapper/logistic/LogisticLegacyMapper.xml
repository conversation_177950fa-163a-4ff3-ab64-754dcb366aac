<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.logistic.mapper.LogisticLegacyMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.LogisticLegacy">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="number" column="number" jdbcType="VARCHAR"/>
            <result property="latestMainStatus" column="latest_main_status" jdbcType="VARCHAR"/>
            <result property="latestSubStatus" column="latest_sub_status" jdbcType="VARCHAR"/>
            <result property="event" column="event" jdbcType="VARCHAR"/>
            <result property="carrier" column="carrier" jdbcType="VARCHAR"/>
            <result property="carrierCode" column="carrier_code" jdbcType="VARCHAR"/>
            <result property="carrierAlias" column="carrier_alias" jdbcType="VARCHAR"/>
            <result property="carrierTel" column="carrier_tel" jdbcType="VARCHAR"/>
            <result property="carrierHomepage" column="carrier_homepage" jdbcType="VARCHAR"/>
            <result property="carrierCountry" column="carrier_country" jdbcType="VARCHAR"/>
            <result property="signTime" column="sign_time" jdbcType="TIMESTAMP"/>
            <result property="saCountry" column="sa_country" jdbcType="VARCHAR"/>
            <result property="saState" column="sa_state" jdbcType="VARCHAR"/>
            <result property="saCity" column="sa_city" jdbcType="VARCHAR"/>
            <result property="saStreet" column="sa_street" jdbcType="VARCHAR"/>
            <result property="saPostalCode" column="sa_postal_code" jdbcType="VARCHAR"/>
            <result property="saLongitude" column="sa_longitude" jdbcType="VARCHAR"/>
            <result property="saLatitude" column="sa_latitude" jdbcType="VARCHAR"/>
            <result property="raCountry" column="ra_country" jdbcType="VARCHAR"/>
            <result property="raState" column="ra_state" jdbcType="VARCHAR"/>
            <result property="raCity" column="ra_city" jdbcType="VARCHAR"/>
            <result property="raStreet" column="ra_street" jdbcType="VARCHAR"/>
            <result property="raPostalCode" column="ra_postal_code" jdbcType="VARCHAR"/>
            <result property="raLongitude" column="ra_longitude" jdbcType="VARCHAR"/>
            <result property="raLatitude" column="ra_latitude" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,number,latest_main_status,
        latest_sub_status,event,carrier,
        carrier_code,carrier_alias,carrier_tel,
        carrier_homepage,carrier_country,sign_time,
        sa_country,sa_state,sa_city,
        sa_street,sa_postal_code,sa_longitude,
        sa_latitude,ra_country,ra_state,
        ra_city,ra_street,ra_postal_code,
        ra_longitude,ra_latitude,create_time,
        update_time
    </sql>
</mapper>
