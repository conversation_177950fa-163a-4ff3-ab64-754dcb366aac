<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.logistic.mapper.LogisticMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.Logistic" id="LogisticResult">
        <result property="id"    column="id"    />
        <result property="number"    column="number"    />
        <result property="carrier"    column="carrier"    />
        <result property="signTime"    column="sign_time"    />
    </resultMap>

    <sql id="selectLogisticVo">
        select id, order_num, number, carrier, description, main_status, sub_status, cur_time, sign_time, country, state, city, street, create_by, create_time, update_by, update_time from logistic
    </sql>
</mapper>