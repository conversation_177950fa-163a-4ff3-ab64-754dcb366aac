<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.logistic.mapper.LogisticInfoMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.LogisticInfo" id="LogisticInfoResult">
        <result property="id" column="id"/>
        <result property="number" column="number"/>
        <result property="mainStatus" column="main_status"/>
        <result property="subStatus" column="sub_status"/>
        <result property="curTime" column="cur_time"/>
        <result property="country" column="country"/>
        <result property="state" column="state"/>
        <result property="city" column="city"/>
        <result property="street" column="street"/>
    </resultMap>

    <sql id="selectLogisticInfoVo">
        select li.id,
               li.`number`,
               li.description,
               li.main_status,
               li.sub_status,
               li.cur_time,
               li.country,
               li.`state`,
               li.city,
               li.street,
               li.longitude,
               li.latitude,
               li.location,
               li.create_time
        from logistic_info li
    </sql>
    <select id="getNumbersByCondition" resultType="java.lang.String">
        select li.`number` from logistic_info li
        JOIN (
            SELECT
                sub.number,
                MAX(sub.cur_time) AS max_cur_time,
                MIN(sub.id) AS min_id
            FROM
                logistic_info sub
            <where>
                <if test="dto.numbers != null and dto.numbers.size() != 0">
                    and sub.`number` in
                    <foreach collection="dto.numbers" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.mainStatus != null and dto.mainStatus.size() != 0">
                    AND sub.main_status in
                    <foreach collection="dto.mainStatus" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
            GROUP BY
                sub.number
        ) latest ON li.number = latest.number  AND li.id = latest.min_id
    </select>
    <select id="getLastLogisticInfo" resultType="com.ruoyi.system.api.domain.vo.LogisticInfoVO">
        <include refid="selectLogisticInfoVo"/>
        JOIN (
            SELECT
                sub.number,
                MAX(sub.cur_time) AS max_cur_time,
                MIN(sub.id) AS min_id
            FROM
                logistic_info sub
            <where>
                <if test="numbers != null and numbers.size() != 0">
                    and sub.`number` in
                    <foreach collection="numbers" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
            GROUP BY
                sub.number
        ) latest ON li.number = latest.number  AND li.id = latest.min_id
    </select>
</mapper>