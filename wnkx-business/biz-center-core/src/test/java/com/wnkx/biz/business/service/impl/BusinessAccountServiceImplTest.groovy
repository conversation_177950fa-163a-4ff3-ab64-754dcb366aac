//package com.wnkx.biz.business.service.impl
//
//import cn.hutool.core.util.RandomUtil
//import com.baomidou.mybatisplus.core.MybatisConfiguration
//import com.baomidou.mybatisplus.core.metadata.TableInfoHelper
//import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
//import com.ruoyi.common.core.constant.TokenConstants
//import com.ruoyi.common.core.constant.WxConstant
//import com.ruoyi.common.core.enums.AuditStatusEnum
//import com.ruoyi.common.core.enums.BalanceSourceTypeEnum
//import com.ruoyi.common.core.enums.BusinessBalanceAuditStatusEnum
//import com.ruoyi.common.core.enums.CustomerTypeEnum
//import com.ruoyi.common.core.enums.StatusEnum
//import com.ruoyi.common.core.enums.StatusTypeEnum
//import com.ruoyi.common.core.enums.WxChatLoginStatusEnum
//import com.ruoyi.common.core.exception.ServiceException
//import com.ruoyi.common.core.utils.SpringUtils
//import com.ruoyi.common.redis.service.RedisService
//import com.ruoyi.common.security.service.TokenService
//import com.ruoyi.common.security.utils.SecurityUtils
//import com.ruoyi.system.api.RemoteOrderService
//import com.ruoyi.system.api.config.WechatConfig
//import com.ruoyi.system.api.domain.dto.BusinessBalanceDTO
//import com.ruoyi.system.api.domain.dto.BusinessBalanceFlowDTO
//import com.ruoyi.system.api.domain.dto.QrCodeDTO
//import com.ruoyi.system.api.domain.dto.biz.business.account.BindingAccountDTO
//import com.ruoyi.system.api.domain.dto.biz.business.account.ChannelPhoneLoginDTO
//import com.ruoyi.system.api.domain.dto.biz.business.account.CheckPhoneDTO
//import com.ruoyi.system.api.domain.dto.biz.business.account.PhoneLoginDTO
//import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserSaveDTO
//import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowAuditDTO
//import com.ruoyi.system.api.domain.entity.biz.business.BizUser
//import com.ruoyi.system.api.domain.entity.biz.business.Business
//import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccount
//import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountApply
//import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow
//import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel
//import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatExternalUser
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO
//import com.ruoyi.system.api.domain.vo.biz.business.ChannelPhoneLoginVO
//import com.ruoyi.system.api.domain.vo.biz.business.PhoneLoginVO
//import com.ruoyi.system.api.domain.vo.biz.business.user.SaveBizUserVO
//import com.ruoyi.system.api.model.LoginBusiness
//import com.wnkx.biz.business.mapper.BusinessAccountMapper
//import com.wnkx.biz.business.mapper.BusinessBalanceAuditFlowMapper
//import com.wnkx.biz.business.mapper.BusinessMapper
//import com.wnkx.biz.business.service.*
//import com.wnkx.biz.channel.service.IBizUserChannelService
//import com.wnkx.biz.channel.service.IOrderMemberChannelService
//import com.wnkx.biz.channel.service.IOrderMemberMarketingChannelService
//import com.wnkx.biz.core.ChannelCore
//import com.wnkx.biz.remote.RemoteService
//import com.wnkx.biz.sms.service.SmsService
//import com.wnkx.biz.wechat.service.IWeChatExternalUserService
//import com.wnkx.biz.wechat.service.WechatService
//import com.wnkx.biz.wechat.service.impl.WorkWechatApiServiceImpl
//import org.apache.ibatis.builder.MapperBuilderAssistant
//import org.mockito.MockedStatic
//import org.mockito.Mockito
//import spock.lang.Shared
//import spock.lang.Specification
//
//import java.lang.reflect.Field
//
//class BusinessAccountServiceImplTest extends Specification {
//    BusinessBalanceAuditFlowMapper businessBalanceAuditFlowMapper = Mock()
//    WorkWechatApiServiceImpl wechatApiService = Mock()
//    WechatService wechatService = Mock()
//    IBusinessService businessService = new BusinessServiceImpl(businessBalanceAuditFlowMapper,wechatApiService)
//    RedisService redisService = Mock(RedisService)
//    TokenService tokenService = Mock()
//    RemoteOrderService remoteOrderService = Mock()
//    RemoteService remoteService = Mock()
//    BusinessMapper businessMapper = Mock(BusinessMapper.class)
//    IBusinessBalanceFlowService businessBalanceFlowService = Mock()
//    IBusinessAccountService self = Mock();
//    LambdaQueryChainWrapper<BusinessAccount> lambdaQueryWrapper = Mock()
//    IBusinessBalanceAuditFlowService businessBalanceAuditFlowService = Mock();
//    WechatConfig wechatConfig = Mock()
//    BusinessAccountMapper businessAccountMapper = Mock()
//    SmsService smsService = Mock();
//    IBusinessOperLogService businessOperLogService = Mock();
//    IWeChatExternalUserService weChatExternalUserService = Mock()
//    ChannelCore channelCore = Mock()
//    IBizUserChannelService bizUserChannelService = Mock()
//    IBizUserService bizUserService = Mock()
//    IOrderMemberChannelService orderMemberChannelService = Mock()
//    IBusinessMemberValidityFlowService businessMemberValidityFlowService = Mock()
//    IBusinessBalancePrepayService businessBalancePrepayService = Mock()
//    IOrderMemberMarketingChannelService orderMemberMarketingChannelService = Mock()
//    IBusinessRemarkFlowService businessRemarkFlowService = Mock()
//    IBusinessAccountService businessAccountService = new BusinessAccountServiceImpl(
//            businessService,
//            redisService,
//            tokenService,
//            remoteOrderService,
//            remoteService,
//            businessMapper,
//            businessBalanceFlowService,
//            self,
//            businessBalanceAuditFlowService,
//            bizUserService,
//            wechatConfig,
//            smsService,
//            businessOperLogService,
//            weChatExternalUserService,
//            channelCore,
//            bizUserChannelService,
//            orderMemberChannelService,
//            orderMemberMarketingChannelService,
//            businessMemberValidityFlowService,
//            businessBalancePrepayService,businessRemarkFlowService
//    )
//
//
//
//    def setup() {
//        // 使用反射注入 baseMapper
//        Field baseMapperField = ServiceImpl.getDeclaredField("baseMapper")
//        baseMapperField.setAccessible(true)
//        baseMapperField.set(businessService, businessMapper)
//        baseMapperField.set(businessAccountService, businessAccountMapper)
//        //将实体类加入mybatis缓存配置
//        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), Business.class)
//    }
//
//    @Shared
//    MockedStatic securityUtils
//    @Shared
//    MockedStatic springUtils
//    @Shared
//    MockedStatic randomUtil
//
//    def setupSpec() {
//        securityUtils = Mockito.mockStatic(SecurityUtils.class)
//        springUtils = Mockito.mockStatic(SpringUtils.class)
//        randomUtil = Mockito.mockStatic(RandomUtil.class)
//    }
//
//    def cleanupSpec() {
//        securityUtils.close()
//        springUtils.close()
//        randomUtil.close()
//    }
//
//
//    def "初始化账号"() {
//        given:
//        4 * redisService.setIncr(_, _, _) >> 1 >> 2 >> 3 >> 4
//        self.lambdaQuery() >> lambdaQueryWrapper
//        lambdaQueryWrapper.eq(_, _) >> lambdaQueryWrapper
//        4 * lambdaQueryWrapper.count() >> 1 >> 1 >> 1 >> 0
//
//        expect:
//        def account = businessAccountService.initAccount(isOwner, ownerAccount, unionid)
//        println account
//
//        where:
//        isOwner                      | ownerAccount | unionid
//        StatusTypeEnum.YES.getCode() | null         | "oftcb6noRSkGOI15cBqtRuTsYe80"
//        StatusTypeEnum.NO.getCode()  | "********"   | "oftcb6noRSkGOI15cBqtRuTsYe80"
//    }
//
//    def "初始化账号异常校验: #expectedMessage"() {
//        given:
//        6 * redisService.setIncr(_, _, _) >> 1 >> 2 >> 3 >> 4 >> 5 >> 6
//        self.lambdaQuery() >> lambdaQueryWrapper
//        lambdaQueryWrapper.eq(_, _) >> lambdaQueryWrapper
//        5 * lambdaQueryWrapper.count() >> 1 >> 1 >> 1 >> 1 >> 1
//        when:
//        businessAccountService.initAccount(StatusTypeEnum.YES.getCode(), null, "oftcb6noRSkGOI15cBqtRuTsYe80")
//
//        then:
//        def exception = thrown(Exception)
//        exception.message == expectedMessage
//
//        where:
//        isOwner || expectedMessage
//        1       || "系统生成随机数失败，请稍后重试！"
//
//    }
//
//    def "余额提现审核异常校验:"() {
//        given:
//        businessBalanceAuditFlowService.getById(_) >> AuditFlow
//        self.updateBusinessBalance(_) >> null
//        redisService.getLock(_, _) >> true
//
//        when:
//        businessAccountService.auditPayOut(auditDto)
//
//        then:
//        def exception = thrown(Exception)
//        exception.message == expectedMessage
//
//        where:
//        auditDto                                                             | AuditFlow                                   || expectedMessage
//        getBusinessBalanceAuditFlowAuditDTO(null, null, null, 1)             | null                                        || "余额提现审核记录不存在！"
//        getBusinessBalanceAuditFlowAuditDTO(null, null, null, 1)             | getBusinessBalanceAuditFlow(1001, 100.0, 1) || "数据库商家数据与请求不一致！"
//        getBusinessBalanceAuditFlowAuditDTO(null, null, null, 1)             | getBusinessBalanceAuditFlow(1002, 100.0, 1) || "只有待审核状态能够进行审核处理！"
//        getBusinessBalanceAuditFlowAuditDTO(null, null, null, 1)             | getBusinessBalanceAuditFlow(1002, 100.0, 2) || "只有待审核状态能够进行审核处理！"
//
//        getBusinessBalanceAuditFlowAuditDTO(null, null, null, 1)             | getBusinessBalanceAuditFlow(1002, 100.0, 0) || "[实付金额]不能为空"
//        getBusinessBalanceAuditFlowAuditDTO(100.0, null, null, 1)            | getBusinessBalanceAuditFlow(1002, 100.0, 0) || "[支付时间]不能为空"
//        getBusinessBalanceAuditFlowAuditDTO(100.0, new Date(), null, 1)      | getBusinessBalanceAuditFlow(1002, 100.0, 0) || "[图片资源地址Url]不能为空"
//        getBusinessBalanceAuditFlowAuditDTO(99.0, new Date(), 'dev/111', 1)  | getBusinessBalanceAuditFlow(1002, 100.0, 0) || "实际提现金额需要与原提现一致"
//        getBusinessBalanceAuditFlowAuditDTO(100.0, new Date(), 'dev/111', 3) | getBusinessBalanceAuditFlow(1002, 100.0, 0) || "审核状态有误！"
//    }
//
//    def "修改商家余额:订单来源：#origin，使用余额：#userBalance,审核状态：#auditStatus,余额审核状态：#balanceAuditStatus | 剩余余额：#resultBanlance，剩余使用余额：#resultUseBanlance"() {
//        given:
//        businessMapper.getBusinessForUpdate(_) >> originBusiness
//        when:
//        businessAccountService.updateBusinessBalance(new BusinessBalanceDTO(businessBalanceFlowDto: new BusinessBalanceFlowDTO(), businessId: 1L, useBalance: userBalance, origin: origin, auditStatus: auditStatus, balanceAuditStatus: balanceAuditStatus, isBalancePay: isBalancePay))
//
//        then:
//        with(originBusiness) {
//            originBusiness.balance == resultBanlance
//            originBusiness.useBalance == resultUseBanlance
//        }
//        where:
//        userBalance | origin                                              | auditStatus                   | balanceAuditStatus                                   | isBalancePay | originBusiness                                  | resultBanlance | resultUseBanlance
//        100.0       | BalanceSourceTypeEnum.CANCEL_ORDER_PART_INCOME.code | AuditStatusEnum.APPROVE.code  | BusinessBalanceAuditStatusEnum.APPROVE.getCode()     | 0            | new Business(balance: 500.0, useBalance: 200.0) | 600.0          | 200.0
//        100.0       | BalanceSourceTypeEnum.CANCEL_CHOOSE_INCOME.code     | AuditStatusEnum.APPROVE.code  | BusinessBalanceAuditStatusEnum.APPROVE.getCode()     | 0            | new Business(balance: 500.0, useBalance: 200.0) | 600.0          | 200.0
//        100.0       | BalanceSourceTypeEnum.CANCEL_ORDER_INCOME.code      | AuditStatusEnum.APPROVE.code  | BusinessBalanceAuditStatusEnum.APPROVE.getCode()     | 0            | new Business(balance: 500.0, useBalance: 200.0) | 600.0          | 200.0
//        100.0       | BalanceSourceTypeEnum.CANCEL_ORDER_INCOME.code      | AuditStatusEnum.UN_CHECK.code | BusinessBalanceAuditStatusEnum.APPROVE.getCode()     | 0            | new Business(balance: 500.0, useBalance: 200.0) | 500.0          | 100.0
//        100.0       | BalanceSourceTypeEnum.ORDER_SPEND.code              | AuditStatusEnum.UN_CHECK.code | BusinessBalanceAuditStatusEnum.APPROVE.getCode()     | 1            | new Business(balance: 500.0, useBalance: 200.0) | 400.0          | 200.0
//        100.0       | BalanceSourceTypeEnum.ORDER_SPEND.code              | AuditStatusEnum.UN_CHECK.code | BusinessBalanceAuditStatusEnum.APPROVE.getCode()     | 0            | new Business(balance: 500.0, useBalance: 200.0) | 500.0          | 300.0
//        100.0       | BalanceSourceTypeEnum.ORDER_SPEND.code              | AuditStatusEnum.APPROVE.code  | BusinessBalanceAuditStatusEnum.APPROVE.getCode()     | 0            | new Business(balance: 500.0, useBalance: 200.0) | 400.0          | 100.0
//        100.0       | BalanceSourceTypeEnum.PAYOUT_SPEND.code             | AuditStatusEnum.APPROVE.code  | BusinessBalanceAuditStatusEnum.APPROVE.getCode()     | 0            | new Business(balance: 500.0, useBalance: 200.0) | 400.0          | 100.0
//        100.0       | BalanceSourceTypeEnum.PAYOUT_SPEND.code             | AuditStatusEnum.APPROVE.code  | BusinessBalanceAuditStatusEnum.CANCEL.getCode()      | 0            | new Business(balance: 500.0, useBalance: 200.0) | 500.0          | 100.0
//        100.0       | BalanceSourceTypeEnum.PAYOUT_SPEND.code             | AuditStatusEnum.APPROVE.code  | BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode() | 0            | new Business(balance: 500.0, useBalance: 200.0) | 500.0          | 300.0
//    }
//
//    def "手机号登录 input #dto"() {
//        given:
//        Mockito.when(SpringUtils.getBean(WechatService.class)).thenReturn(wechatService)
//        smsService.verifyCode(dto.getPhone(), dto.getPhoneCaptcha()) >> smsValid
//        bizUserService.getByPhone(dto.getPhone()) >> bizUserByPhone
//        wechatService.getUnionIdByTicket(dto.getTicket()) >> unionId
//        bizUserService.getByUnionId(unionId) >> bizUserByUnionId
//        wechatService.getTicketByUnionId(unionId) >> dto.getTicket()
//        weChatExternalUserService.getOneByUnionid(unionId) >> weChatExternalUser
//        self.generateQrcode(_, _) >> new QrCodeDTO("url", "ticket")
//        redisService.setCacheObject(_, _, _) >> { String key, String value, Long timeout -> }
//        self.getBusinessAccount(_) >> businessVo
//
//
//        when:
//        PhoneLoginVO result = businessAccountService.phoneLogin(dto)
//
//        then:
//        result.loginStatus == expectedLoginStatus
//        result.msg == expectedMsg
//        result.businessAccountVO == expectedBusinessAccountVO
//
//        where:
//        dto                                                                               | smsValid | bizUserByPhone                  | unionId   | bizUserByUnionId | weChatExternalUser                                                                                         | businessVo             | expectedLoginStatus                       | expectedMsg                                         | expectedBusinessAccountVO
//        //ticket为空 验证码校验成功 登录账号需要初始化  》》 已存在手机号未绑定微信
//        new PhoneLoginDTO(phone: "***********", phoneCaptcha: "123456", ticket: null)     | true     | null                            | null      | null             | null                                                                                                       | getBusinessAccountVo() | WxChatLoginStatusEnum.LOGIN_NO_WE_CHAT    | null                                                | null
//        //ticket为空 验证码校验成功 登录账号已存在微信号 》》登录成功
//        new PhoneLoginDTO(phone: "***********", phoneCaptcha: "123456", ticket: null)     | true     | new BizUser(unionid: "unionId") | null      | null             | null                                                                                                       | getBusinessAccountVo() | WxChatLoginStatusEnum.LOGIN_SUCCESS       | null                                                | getBusinessAccountVo()
//        //验证码校验失败
//        new PhoneLoginDTO(phone: "***********", phoneCaptcha: "wrong_code", ticket: null) | false    | null                            | null      | null             | null                                                                                                       | getBusinessAccountVo() | WxChatLoginStatusEnum.CAPTCHA_FAULT_ERROR | WxChatLoginStatusEnum.CAPTCHA_FAULT_ERROR.getDesc() | null
//        //ticket不为空 验证码校验成功 登录账号不存在微信号 ticket不存在登录账号  >>登录成功
//        new PhoneLoginDTO(phone: "***********", phoneCaptcha: "123456", ticket: "ticket") | true     | new BizUser(unionid: null)      | "unionId" | null             | new WeChatExternalUser(name: "Test", externalUserid: "extUserId", avatar: "avatarUrl", unionid: "unionId") | getBusinessAccountVo() | WxChatLoginStatusEnum.LOGIN_SUCCESS       | null                                                | getBusinessAccountVo()
//        //ticket不为空 验证码校验成功 登录账号存在微信号       >>已绑定
//        new PhoneLoginDTO(phone: "***********", phoneCaptcha: "123456", ticket: "ticket") | true     | new BizUser(unionid: "unionId") | "unionId" | new BizUser()    | new WeChatExternalUser(name: "Test", externalUserid: "extUserId", avatar: "avatarUrl", unionid: "unionId") | getBusinessAccountVo() | WxChatLoginStatusEnum.BINDING             | null                                                | null
//        //ticket不为空 验证码校验成功 登录账号不存在微信号 ticket存在登录账号  >>已绑定
//        new PhoneLoginDTO(phone: "***********", phoneCaptcha: "123456", ticket: "ticket") | true     | new BizUser(unionid: null)      | "unionId" | new BizUser()    | new WeChatExternalUser(name: "Test", externalUserid: "extUserId", avatar: "avatarUrl", unionid: "unionId") | getBusinessAccountVo() | WxChatLoginStatusEnum.BINDING             | null                                                | null
//    }
//
//
//    def "生成二维码 for input #type and #code return #expectedTicket"() {
//        setup:
//        Mockito.when(RandomUtil.randomString(TokenConstants.BASE_CHAR_NUMBER, 6)).thenReturn("acdfhi")
//        redisService.setNx(_, _, _) >> true
//        wechatConfig.getAppId() >> "wx552e48004afb1d3e"
//        wechatConfig.getRedirectUrl() >> "https%3A%2F%2Fwxg.woniu.video%2Fdev%2Fwechat%2Faccredit"
//        wechatConfig.getRedirectUrl() >> expectedTicket
//
//        when:
//        QrCodeDTO result = businessAccountService.generateQrcode(type, code)
//
//        then:
//        result.ticket == expectedTicket
//        result.qrcode == expectedQrCode
//
//        where:
//        type                          | code       | randomString | codeLength | typeEquals | setNx | expectedTicket      | expectedQrCode
//        TokenConstants.TOKEN_REGISTER | ""         | "acdfhi"     | 0          | true       | true  | "REGacdfhi"         | String.format(WxConstant.WEIXIN_OAUTH2_URL, "wx552e48004afb1d3e", "https%3A%2F%2Fwxg.woniu.video%2Fdev%2Fwechat%2Faccredit", "REGacdfhi")
//        TokenConstants.TOKEN_REBIND   | ""         | "acdfhi"     | 0          | false      | true  | "REBacdfhi"         | String.format(WxConstant.WEIXIN_OAUTH2_URL, "wx552e48004afb1d3e", "https%3A%2F%2Fwxg.woniu.video%2Fdev%2Fwechat%2Faccredit", "REBacdfhi")
//        TokenConstants.TOKEN_VERIFY   | ""         | "acdfhi"     | 0          | false      | true  | "VERacdfhi"         | String.format(WxConstant.WEIXIN_OAUTH2_URL, "wx552e48004afb1d3e", "https%3A%2F%2Fwxg.woniu.video%2Fdev%2Fwechat%2Faccredit", "VERacdfhi")
//        TokenConstants.TOKEN_REBIND   | "********" | "acdfhi"     | 8          | true       | true  | "REBacdfhi********" | String.format(WxConstant.WEIXIN_OAUTH2_URL, "wx552e48004afb1d3e", "https%3A%2F%2Fwxg.woniu.video%2Fdev%2Fwechat%2Faccredit", "REBacdfhi********")
//    }
//
//    def "检查当前账号与手机号是否相同:"() {
//        given:
//        Mockito.when(SecurityUtils.getLoginBusinessUser()).thenReturn(new LoginBusiness(new BusinessAccountVO(bizUserId: 1)))
//        bizUserService.getById(_) >> bizUser
//        when:
//        businessAccountService.bizUserCheckPhone(new CheckPhoneDTO(phone: dtoPhone))
//
//        then:
//        def exception = thrown(ServiceException)
//        exception.message == expectedMessage
//
//        where:
//        bizUser                         | dtoPhone    | expectedMessage
//        null                            | "********9" | "登录账号错误，系统不存在当前登录账号"
//        new BizUser(phone: "")          | "********9" | "登录账号手机号为空！"
//        new BizUser(phone: "*********") | "********9" | "手机号错误，请重新填写~"
//
//    }
//
//    def "根据申请列表初始化子账号"() {
//        given:
//        BusinessAccountApply businessAccountApply = new BusinessAccountApply(businessId: businessId, bizUserId: bizUserId, ownerAccount: ownerAccount, unionid: unionid)
//
//        // Mocking the behavior of business service and base mapper
//        businessService.getById(businessId) >> business
//        businessAccountService.baseMapper.getByBizUserId(bizUserId) >> existingAccount
//
//        when:
//        def result = businessAccountService.initBusinessSon(businessAccountApply)
//
//        then:
//        def e = thrown(Exception)
//
//        where:
//        businessId | bizUserId | ownerAccount | unionid  | business                                               | existingAccount                              | expectedException | expectedMessage                | expectedAccount
//        1          | 1         | "owner123"   | "someId" | new Business(memberType: StatusTypeEnum.YES.getCode()) | new BusinessAccount(account: "alreadyBound") | true              | "该登录账号已绑定：alreadyBound，无法再次绑定" | null
//        1          | 1         | "owner123"   | "someId" | null                                                   | null                                         | true              | "主账号不存在，无法添加子账号"               | null
//        1          | 1         | "owner123"   | "someId" | new Business(memberType: StatusTypeEnum.NO.getCode())  | null                                         | true              | "只有会员才能添加子账号"                  | null
//    }
//
//    def "检查账号是否是主账号"() {
//        given:
//        Mockito.when(SpringUtils.getBean(WechatService.class)).thenReturn(wechatService);
//        wechatService.getUnionIdByTicket(ticket) >> unionId
//        self.getBusinessAccount(_) >> businessVo
//        businessService.getBaseMapper().selectById(_) >> business
//
//        when:
//        businessAccountService.checkAccount(ticket, account)
//
//        then:
//        def e = thrown(Exception)
//        e.message == expectedMessage
//
//        where:
//        ticket        | account        | unionId        | businessVo                                                                                         | business                                      | expectedMessage
//        ""            | "validAccount" | "validUnionId" | new BusinessAccountVO()                                                                            | null                                          | "账号ticket不能为空"
//        "validTicket" | "validAccount" | "validUnionId" | null                                                                                               | null                                          | "不存在对应微信的账号"
//        "validTicket" | "validAccount" | "validUnionId" | new BusinessAccountVO(account: "validAccount", isMock: StatusTypeEnum.NO.getCode(), businessId: 1) | null                                          | "数据有误，账号无商家数据"
//    }
//
//    def "获取加密手机号：#phone, #expectedOutput"() {
//        given:
//
//        when:
//        def result = businessAccountService.getPhone(phone)
//
//        then:
//        result == expectedOutput
//
//        where:
//        phone         || expectedOutput
//        "***********" || "138****5678"
//        "***********" || "185****5432"
//    }
//
//    def "余额提现审核:"() {
//        given:
//        def dto = new BusinessBalanceAuditFlowAuditDTO(id: 1, businessId: 123, auditStatus: auditStatus, realAmount: realAmount, payTime: payTime, resourceUrl: resourceUrl)
//        def entity = new BusinessBalanceAuditFlow(businessId: businessId, amount: expectedAmount, auditStatus: entityAuditStatus)
//        redisService.getLock(_, _) >> lockResult
//        businessBalanceAuditFlowService.getById(dto.id) >> entity
//        businessAccountService.updateBusinessBalance(_) >> { BusinessBalanceDTO businessBalanceDTO -> }
//
//        when:
//        businessAccountService.auditPayOut(dto)
//
//        then:
//        def e = thrown(IllegalArgumentException)
//        e.message == expectedMessage
//
//        where:
//        auditStatus                                      | entityAuditStatus                                    | realAmount | lockResult | payTime    | resourceUrl                    | businessId | expectedAmount | expectedMessage
//        BusinessBalanceAuditStatusEnum.APPROVE.getCode() | BusinessBalanceAuditStatusEnum.APPROVE.getCode()     | null       | false      | new Date() | "http://example.com/image.jpg" | 111        | 100.00         | "数据库商家数据与请求不一致！"
//        BusinessBalanceAuditStatusEnum.APPROVE.getCode() | BusinessBalanceAuditStatusEnum.APPROVE.getCode()     | null       | false      | new Date() | "http://example.com/image.jpg" | 123        | 100.00         | "只有待审核状态能够进行审核处理！"
//        BusinessBalanceAuditStatusEnum.APPROVE.getCode() | BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode() | 50.00      | false      | new Date() | "http://example.com/image.jpg" | 123        | 100.00         | "实际提现金额需要与原提现一致"
//        BusinessBalanceAuditStatusEnum.APPROVE.getCode() | BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode() | null       | false      | null       | "http://example.com/image.jpg" | 123        | 100.00         | "[实付金额]不能为空"
//        BusinessBalanceAuditStatusEnum.APPROVE.getCode() | BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode() | 100.00     | false      | null       | "http://example.com/image.jpg" | 123        | 100.00         | "[支付时间]不能为空"
//        BusinessBalanceAuditStatusEnum.APPROVE.getCode() | BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode() | 100.00     | false      | new Date() | null                           | 123        | 100.00         | "[图片资源地址Url]不能为空"
//        BusinessBalanceAuditStatusEnum.CANCEL.getCode()  | BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode() | 100.0      | false      | null       | null                           | 123        | 99.00          | "商家余额处理中，请稍后重试！"   // No exception expected for CANCEL status
//    }
//
//    def "绑定账号"() {
//        given:
//        Mockito.when(SpringUtils.getBean(WechatService.class)).thenReturn(wechatService)
//        wechatService.getRedisUnionIdByTicket(dto.ticket) >> unionId
//        self.getBusinessAccount(unionId) >> accountVo
//        businessAccountService.getBaseMapper().bindingLoginUser(_, _) >> { Long id, Long userId -> }
//
//        when:
//        businessAccountService.bindingLoginUser(dto)
//        then:
//        def e = thrown(Exception.class)
//        e.message == expectedException
//
//        where:
//        dto                                                                                                            | unionId         | accountVo                                                                                                                 | expectedException
//        new BindingAccountDTO(ticket: "invalid_ticket", phone: "***********", businessAccountId: 1, name: "Test Name") | "mock_union_id" | null                                                                                                                      | "微信对应登录账号为空"
//        new BindingAccountDTO(ticket: "valid_ticket", phone: "***********", businessAccountId: 1, name: "Test Name")   | "mock_union_id" | new BusinessAccountVO(bizUserId: 1, account: "bound_account", isMock: StatusTypeEnum.NO.getCode(), phone: "***********")  | "绑定失败！该用户已是子账号，无法成为子账号"
//        new BindingAccountDTO(ticket: "valid_ticket", phone: "***********", businessAccountId: 1, name: "Test Name")   | "mock_union_id" | new BusinessAccountVO(bizUserId: 1, account: "bound_account", isMock: StatusTypeEnum.YES.getCode(), phone: "***********") | "登录账号微信绑定手机号与输入手机号不一致！"
//    }
//
//
//    def "保存登录账号异常测试"() {
//        given:
//        Mockito.when(SpringUtils.getBean(WechatService.class)).thenReturn(wechatService)
//        redisService.getCacheObject(_) >> code
//        wechatService.getRedisUnionIdByTicket(dto.ticket) >> "unionId"
//        businessAccountService.getBusinessAccount(_) >> new BusinessAccountVO()
//        businessAccountService.getBaseMapper().bindingLoginUser(_, _) >> { Long id, Long userId -> }
//        bizUserService.initBizUserBaseOnPhone(_) >> bizUser
//
//        when:
//        businessAccountService.saveBizUser(dto)
//        then:
//        def e = thrown(Exception.class)
//        e.message == expectedException
//
//        where:
//        dto                                                                                                                            | code | bizUser                         | expectedException
//        new BizUserSaveDTO(phone: "***********", ticket: "valid_ticket", customerType: CustomerTypeEnum.NORMAL.getCode())              | null | new BizUser(unionid: "unionid") | "当重要程度为一般客户时，对接客服不能为空！"
//        new BizUserSaveDTO(phone: "***********", ticket: "valid_ticket", customerType: CustomerTypeEnum.VIP.getCode())                 | null | new BizUser(unionid: "unionid") | "当重要程度为重要客户时，对接客服不能为空！"
//        new BizUserSaveDTO(phone: "***********", ticket: "valid_ticket", customerType: CustomerTypeEnum.NORMAL.getCode(), waiterId: 1) | null | new BizUser(unionid: "unionid") | "手机号已绑定其他微信！"
//    }
//
//    def "保存登录账号测试"() {
//        given:
//        Mockito.when(SpringUtils.getBean(WechatService.class)).thenReturn(wechatService)
//        bizUserService.initBizUserBaseOnPhone(_) >> new BizUser(name: "name")
//        redisService.getCacheObject(_) >> code
//        wechatService.getUnionIdByTicket(_) >> "unionId"
//        wechatService.getTicketByUnionId(_) >> "valid_ticket"
//        bizUserService.getByUnionId(_) >> unionIdBizUser
//        weChatExternalUserService.getOneByUnionid(_) >> weChatExternalUser
//
//
//        when:
//        SaveBizUserVO result = businessAccountService.saveBizUser(new BizUserSaveDTO(phone: "***********", ticket: "valid_ticket", customerType: CustomerTypeEnum.NORMAL.getCode(), waiterId: 1))
//        then:
//        result.loginStatus == resultStatus
//
//        where:
//        code | unionIdBizUser                  | weChatExternalUser                         | resultStatus
//        null | new BizUser(unionid: "unionid") | null                                       | WxChatLoginStatusEnum.EXPIRE
//        1    | new BizUser(unionid: "unionid") | null                                       | WxChatLoginStatusEnum.BINDING
//        1    | null                            | null                                       | WxChatLoginStatusEnum.UNKNOWN
//        1    | null                            | new WeChatExternalUser(unionid: "unionId") | WxChatLoginStatusEnum.LOGIN_SUCCESS
//    }
//
//
//    def getBusinessBalanceAuditFlowAuditDTO(realAmount, payTime, resourceId, auditStatus) {
//        BusinessBalanceAuditFlowAuditDTO dto = new BusinessBalanceAuditFlowAuditDTO(id: 1, businessId: 1002)
//        dto.realAmount = realAmount
//        dto.payTime = payTime
//        dto.resourceUrl = resourceId
//        dto.auditStatus = auditStatus
//        return dto
//    }
//
//    def getBusinessBalanceAuditFlow(businessId, amount, auditStatus) {
//        BusinessBalanceAuditFlow entity = new BusinessBalanceAuditFlow();
//        entity.businessId = businessId
//        entity.amount = amount
//        entity.auditStatus = auditStatus
//        return entity
//    }
//
//    def getBusinessAccount() {
//        def businessAccount = new BusinessAccount()
//
//        return businessAccount
//    }
//
//    def getBizUser(unionid) {
//        BizUser user = new BizUser()
//        user.getId() == 1L
//        user.getName() == "张三"
//        user.getNickName() == "小三"
//        user.getPic() == "http://example.com/avatar.jpg"
//        user.getUnionid() == unionid
//        user.getPhone() == "***********"
//        user.getExternalUserId() == "externalId123"
//        user.getIsProxy() == 1
//        user.getAccountType() == 1
//        user.getWaiterId() == 100L
//        user.getStatus() == 0
//        user.getLastLoginTime() != new Date()
//        user.getCreateTime() != new Date()
//        user.getUpdateTime() != new Date()
//        user.getIsDel() == 0
//        return user
//    }
//
//    def getBusinessAccountVo() {
//        def businessAccountVO = new BusinessAccountVO()
//        def businessVo = new BusinessVO()
//        businessVo.id = 1017L
//        businessVo.memberCode = "LJQZ"
//        businessVo.memberType = StatusTypeEnum.YES.getCode()
//        businessVo.name = "润一科技"
//        businessVo.status = 0
//        businessVo.waiterId = 109L
//        businessAccountVO.businessId = 1014L
//        businessAccountVO.status = 0
//        businessAccountVO.account = "123654"
//        businessAccountVO.businessVO = businessVo
//        businessAccountVO.unionid = "unionid"
//        businessAccountVO.isMock = 1
//        return businessAccountVO
//    }
//}
