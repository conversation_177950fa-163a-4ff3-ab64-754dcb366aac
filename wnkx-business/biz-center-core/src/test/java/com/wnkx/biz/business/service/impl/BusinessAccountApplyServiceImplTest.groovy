//package com.wnkx.biz.business.service.impl
//
//import com.ruoyi.common.core.enums.AccountAuditStatusEnum
//import com.ruoyi.common.core.utils.SpringUtils
//import com.ruoyi.common.security.utils.SecurityUtils
//import com.ruoyi.system.api.domain.dto.biz.business.account.AuditAccountApplyDTO
//import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccount
//import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountApply
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO
//import com.ruoyi.system.api.model.LoginBusiness
//import com.wnkx.biz.business.mapper.BusinessAccountApplyMapper
//import com.wnkx.biz.business.service.IBusinessAccountApplyService
//import com.wnkx.biz.business.service.IBusinessAccountService
//import com.wnkx.biz.remote.RemoteService
//import org.mockito.MockedStatic
//import org.mockito.Mockito
//import spock.lang.Shared
//import spock.lang.Specification
//
//class BusinessAccountApplyServiceImplTest extends Specification {
//    IBusinessAccountApplyService self = Mock()
//    IBusinessAccountService businessAccountService = Mock()
//    RemoteService remoteService = Mock()
//    IBusinessAccountApplyService businessAccountApplyService = new BusinessAccountApplyServiceImpl(
//            self,
//            businessAccountApplyService,
//            remoteService
//    )
//
//    @Shared
//    MockedStatic securityUtils
//    @Shared
//    MockedStatic springUtils
//
//    def setupSpec() {
//        securityUtils = Mockito.mockStatic(SecurityUtils.class)
//        springUtils = Mockito.mockStatic(SpringUtils.class)
//    }
//
//    def cleanupSpec() {
//        securityUtils.close()
//        springUtils.close()
//    }
//
//    def "审核账号申请数据"() {
//        given:
//        Mockito.when(SecurityUtils.getLoginBusinessUser()).thenReturn(new LoginBusiness(businessAccountVo))
//        self.getById(dto.id) >> tableEntity // 模拟通过 ID 获取申请数据
//        businessAccountService.initBusinessSon(_) >> new BusinessAccount()
//
//        when:
//        businessAccountApplyService.audit(dto)
//
//        then:
//        def exception = thrown(Exception)
//        exception.message == expectedMessage
//
//        where:
//        dto                                                                                      | businessAccountVo                     | tableEntity                                                                                                           | ownerAccount   | expectedMessage
//        new AuditAccountApplyDTO(id: 999, auditStatus: AccountAuditStatusEnum.APPROVE.getCode()) | new BusinessAccountVO(account: "")    | null                                                                                                                  | null           | "不存在该申请数据"
//        new AuditAccountApplyDTO(id: 1, auditStatus: AccountAuditStatusEnum.APPROVE.getCode())   | new BusinessAccountVO(account: "")    | new BusinessAccountApply(id: 1, ownerAccount: "test_account", auditStatus: AccountAuditStatusEnum.UN_AUDIT.getCode()) | "test_account" | "当前登录人与修改记录账号不一致！"
//        new AuditAccountApplyDTO(id: 2, auditStatus: AccountAuditStatusEnum.APPROVE.getCode())   | new BusinessAccountVO(account: "123") | new BusinessAccountApply(id: 2, ownerAccount: "123", auditStatus: AccountAuditStatusEnum.APPROVE.getCode())           | "test_account" | "非待审核状态数据无法进行账号申请审批！"
//    }
//}
