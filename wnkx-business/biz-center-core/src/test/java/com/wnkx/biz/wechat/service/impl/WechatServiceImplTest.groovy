//package com.wnkx.biz.wechat.service.impl
//
//import cn.hutool.http.HttpUtil
//import com.alibaba.fastjson.JSON
//import com.ruoyi.common.core.constant.CacheConstants
//import com.ruoyi.common.core.constant.UserConstants
//import com.ruoyi.common.core.constant.WxConstant
//import com.ruoyi.common.core.enums.CheckWechatEnum
//import com.ruoyi.common.core.enums.StatusTypeEnum
//import com.ruoyi.common.core.enums.WxChatLoginStatusEnum
//import com.ruoyi.common.redis.service.RedisService
//import com.ruoyi.common.security.service.TokenService
//import com.ruoyi.system.api.config.WechatConfig
//import com.ruoyi.system.api.config.WorkWeChatConfig
//import com.ruoyi.system.api.domain.dto.biz.wechat.JoinBusinessDTO
//import com.ruoyi.system.api.domain.dto.biz.wechat.WeChatChannelLoginRequestDTO
//import com.ruoyi.system.api.domain.dto.biz.wechat.WeChatOauth2LoginRequestDTO
//import com.ruoyi.system.api.domain.dto.biz.wechat.WechatOauth2LoginDTO
//import com.ruoyi.system.api.domain.dto.biz.wechat.WechatTokenDTO
//import com.ruoyi.system.api.domain.entity.biz.business.BizUser
//import com.ruoyi.system.api.domain.entity.biz.business.Business
//import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccount
//import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel
//import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatExternalUser
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountApplyVO
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO
//import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO
//import com.ruoyi.system.api.domain.vo.biz.channel.ChannelVO
//import com.wnkx.biz.business.service.IBizUserService
//import com.wnkx.biz.business.service.IBusinessAccountApplyService
//import com.wnkx.biz.business.service.IBusinessAccountService
//import com.wnkx.biz.business.service.IBusinessService
//import com.wnkx.biz.config.ChannelMarketingProperties
//import com.wnkx.biz.core.ChannelCore
//import com.wnkx.biz.remote.RemoteService
//import com.wnkx.biz.sms.service.SmsService
//import com.wnkx.biz.wechat.service.IWeChatExternalUserService
//import com.wnkx.biz.wechat.service.WechatService
//import org.mockito.MockedStatic
//import org.mockito.Mockito
//import spock.lang.Shared
//import spock.lang.Specification
//
//import java.lang.reflect.Field
//
//class WechatServiceImplTest extends Specification {
//    RedisService redisService = Mock()
//    WechatConfig wechatConfig = Mock()
//    IBusinessAccountService businessAccountService = Mock()
//    IBusinessService businessService = Mock()
//    IBusinessAccountApplyService businessAccountApplyService = Mock()
//    TokenService tokenService = Mock()
//    IWeChatExternalUserService weChatExternalUserService = Mock()
//    IBizUserService bizUserService = Mock()
//    SmsService smsService = Mock()
//    ChannelCore channelCore = Mock()
//    WorkWechatApiServiceImpl wechatApiService = Mock()
//    WorkWeChatConfig workWeChatConfig =Mock();
//    RemoteService remoteService = Mock()
//    ChannelMarketingProperties channelMarketingProperties =Mock()
//    WechatService wechatService = new WechatServiceImpl(
//            redisService,
//            wechatConfig,
//            businessAccountService,
//            businessService,
//            businessAccountApplyService,
//            tokenService,
//            weChatExternalUserService,
//            bizUserService,
//            smsService,
//            channelCore,
//            wechatApiService,
//            remoteService,
//            workWeChatConfig,channelMarketingProperties
//    )
//
//    @Shared
//    MockedStatic httpUtil
//
//    def setupSpec() {
//        httpUtil = Mockito.mockStatic(HttpUtil.class)
//    }
//
//    def setup() {
//        Class<?> clazz = wechatService.getClass()
//        Field workWxtypeField = clazz.getDeclaredField("workWxtype")
//        Field workWxurlField = clazz.getDeclaredField("workWxurl")
//        workWxtypeField.setAccessible(true)
//        workWxurlField.setAccessible(true)
//        workWxtypeField.set(wechatService, 3)
//        workWxurlField.set(wechatService, "https://default")
//    }
//
//    def cleanupSpec() {
//        httpUtil.close()
//    }
//
//    def "oauth2登录"() {
//        given:
//        getWeChatInfo()
//        businessAccountService.getBusinessAccount(_) >> businessAccount
//        channelCore.getChannelByTicket(_) >> new ChannelVO(weChatUrl: weChatUrl)
//        weChatExternalUserService.getOneByUnionid(_) >> weChatExternalUser
//        bizUserService.getByUnionId(_) >> new BizUser()
//        redisService.getCacheObject(_) >> redisValue
//
//        when:
//        WechatOauth2LoginDTO result = wechatService.oauth2(new WeChatOauth2LoginRequestDTO(code: "12312", state: ticket))
//
//        then:
//        result.loginStatus == resultStatus
//        result.getUrl() == resultUrl
//        result.getType() == resultType
//        where:
//        ticket      | businessAccount                                                                        | weChatUrl      | redisValue | weChatExternalUser                       | resultStatus                          | resultUrl         | resultType
//        "REGticket" | null                                                                                   | "https:xxx/xx" | "XXX"      | null                                     | WxChatLoginStatusEnum.LOGINING        | "https:xxx/xx"    | 2
//        "REGticket" | null                                                                                   | ""             | "XXX"      | null                                     | WxChatLoginStatusEnum.LOGINING        | "https://default" | 3
//        "REGticket" | null                                                                                   | ""             | "XXX"      | new WeChatExternalUser(unionid: "13214") | WxChatLoginStatusEnum.LOGIN_SUCCESS   | null              | null
//        "REGticket" | new BusinessAccountVO()                                                                | ""             | "XXX"      | null                                     | WxChatLoginStatusEnum.BINDING         | null              | null
//        "REGticket" | new BusinessAccountVO(status: UserConstants.USER_STATUS_DISABLE)                       | ""             | null       | null                                     | WxChatLoginStatusEnum.ACCOUNT_DISABLE | null              | null
//        "REGticket" | new BusinessAccountVO(status: 0, businessVO: new BusinessVO(status: 1))                | ""             | null       | null                                     | WxChatLoginStatusEnum.ACCOUNT_DISABLE | null              | null
//        "REGticket" | new BusinessAccountVO(status: 0, userStatus: 0, businessVO: new BusinessVO(status: 0)) | ""             | null       | null                                     | WxChatLoginStatusEnum.LOGIN_SUCCESS   | null              | null
//
////        "REBticket" | new BusinessAccountVO()                                                                | ""             | null       | null                                     | WxChatLoginStatusEnum.LOGIN_SUCCESS   | null              | null
//        "REBticket" | null                                                                                   | ""             | null       | null                                     | WxChatLoginStatusEnum.LOGINING        | "https://default" | 3
//        "REBticket" | null                                                                                   | ""             | "XXX"      | new WeChatExternalUser(unionid: "13214") | WxChatLoginStatusEnum.LOGIN_SUCCESS   | null              | null
//
//        "VERticket" | null                                                                                   | ""             | "XXX"      | new WeChatExternalUser(unionid: "13214") | WxChatLoginStatusEnum.UN_REGISTER     | null              | null
//        "VERticket" | new BusinessAccountVO()                                                                | ""             | "XXX"      | new WeChatExternalUser(unionid: "13214") | WxChatLoginStatusEnum.LOGIN_SUCCESS   | null              | null
//    }
//
//    def "渠道端登录"() {
//        given:
//        getWeChatInfo()
//        businessAccountService.getBusinessAccount(_) >> businessAccount
//        channelCore.getChannelByBizUserId(_) >> new DistributionChannel(status: 0)
//        tokenService.createToken(_) >> Map.of("access_token", "xxx")
//
//        when:
//        def result = wechatService.channelLogin(new WeChatChannelLoginRequestDTO(code: "12312"))
//
//        then:
//        result.loginStatus == resultStatus
//
//        where:
//        businessAccount                                                         | resultStatus
//        null                                                                    | WxChatLoginStatusEnum.LOGIN_NO_CHANNEL
//        new BusinessAccountVO(status: 0, businessVO: new BusinessVO(status: 0)) | WxChatLoginStatusEnum.LOGIN_SUCCESS
//    }
//
//    def "添加子账号检查微信是否可用"() {
//        given:
//        getWeChatInfo()
//        businessAccountApplyService.list(_) >> businessAccountApplyVOS
//        businessService.queryOne(_) >> new Business(name: "厦门科技有限公司", memberType: memberType, ownerAccount: ownerAccount)
//        businessAccountService.getBusinessAccountOne(_) >> new BusinessAccountVO(businessVO: new BusinessVO(name: "福州科技有限公司"))
//        businessAccountService.queryOne(_) >> businessAccount
//
//        businessService.getById(_) >> new Business(ownerAccount: "123456", name: "福州科技有限公司")
//        bizUserService.getByUnionId(_) >> bizUserByUnionId
//
//        when:
//        def result = wechatService.checkWechat(new WeChatOauth2LoginRequestDTO(code: "12312", state: "SubAccountVer123"))
//        then:
//        result.status == resultStatus
//        result.message == resultMessage
//        result.applyBusinessName == resultApplyBusinessName
//        result.businessName == resultBusinessName
//        where:
//        businessAccountApplyVOS                                    | businessAccount                          | ownerAccount | bizUserByUnionId | memberType                   | resultStatus                                    | resultApplyBusinessName | resultBusinessName | resultMessage
//        [new BusinessAccountApplyVO(ownerAccount: "ownerAccount")] | new BusinessAccountVO()                  | "123456"     | null             | StatusTypeEnum.NO.getCode()  | CheckWechatEnum.ACCOUNT_APPLY.getCode()         | "厦门科技有限公司"              | "福州科技有限公司"         | "已申请福州科技有限公司子账号，无法加入"
//        null                                                       | new BusinessAccountVO()                  | "123456"     | null             | StatusTypeEnum.NO.getCode()  | CheckWechatEnum.OWNER_BUSINESS_EXPIRE.getCode() | "厦门科技有限公司"              | ""                 | "企业主账号会员已到期，无法加入"
//        null                                                       | new BusinessAccountVO()                  | "123456"     | null             | StatusTypeEnum.YES.getCode() | CheckWechatEnum.NO_BIZ_USER.getCode()           | "厦门科技有限公司"              | ""                 | "不存在登录账号"
//        null                                                       | new BusinessAccountVO(isOwnerAccount: 1) | "123456"     | new BizUser()    | StatusTypeEnum.YES.getCode() | CheckWechatEnum.ALREADY_OWNER.getCode()         | "厦门科技有限公司"              | "福州科技有限公司"         | "您已是平台主账号"
//        null                                                       | new BusinessAccountVO(isOwnerAccount: 0) | "123456"     | new BizUser()    | StatusTypeEnum.YES.getCode() | CheckWechatEnum.ALREADY_SON.getCode()           | "厦门科技有限公司"              | "福州科技有限公司"         | "您已有福州科技有限公司子账号权限"
//        null                                                       | new BusinessAccountVO(isOwnerAccount: 0) | "123466"     | new BizUser()    | StatusTypeEnum.YES.getCode() | CheckWechatEnum.ALREADY_OTHER_SON.getCode()     | "厦门科技有限公司"              | "福州科技有限公司"         | "您已是福州科技有限公司子账号"
////        null                                                       | null                                     | "123456"     | new BizUser()    | StatusTypeEnum.YES.getCode() | CheckWechatEnum.SUCCESS.getCode()               | "厦门科技有限公司"              | ""                 | "有效账号"
//    }
//
//    def "加入商家"() {
//        given:
//        redisService.getCacheObject(CacheConstants.WECHAT_JOIN_BUSINESS_CODE_UNIONID_PREFIX + "12312") >> "unionId"
//        weChatExternalUserService.getOneByUnionid(_) >> weChatExternalUser
//        businessService.queryOne(_) >> new Business()
//        bizUserService.getByUnionId(_) >> new BizUser(phone: "***********")
//        businessAccountService.getByBizUserId(_) >> null
//        remoteService.getValidOrderCount(_) >> 0
//        when:
//        def reslut = wechatService.joinBusiness(new JoinBusinessDTO(code: "12312", state: "SubAccountVer123"))
//        then:
//        reslut.needAddWeChat == needAddWeChat
//        where:
//        weChatExternalUser                         | needAddWeChat
//        new WeChatExternalUser(unionid: "unionId") | StatusTypeEnum.NO.getCode()
//        null                                       | StatusTypeEnum.YES.getCode()
//
//    }
//
//    def "添加申请表数据:"() {
//        given:
//        redisService.getCacheObject(CacheConstants.WECHAT_JOIN_BUSINESS_UNIONI_DTO_PREFIX + "unionId") >> redisJoinBusiness
//        businessService.queryOne(_) >> businessByQuery
//        bizUserService.getByUnionId(_) >> bizUser
//        bizUserService.getByPhone(_) >> phoneBizUser
//        bizUserService.initBizUserBaseOnPhone(_) >> initBizUser
//        businessAccountService.getByBizUserId(_) >> businessAccount
//
//        when:
//        wechatService.addBusinessAccountApply(weChatExternalUser, joinBusinessDTO)
//        then:
//        def exception = thrown(Exception)
//        exception.message == expectedMessage
//        where:
//        weChatExternalUser                         | joinBusinessDTO | redisJoinBusiness                                                    | bizUser | phoneBizUser  | initBizUser                 | businessAccount       | businessByQuery | expectedMessage
//        null                                       | null            | null                                                                 | null    | null          | null                        | new BusinessAccount() | null            | "外部联系人信息不能为空！"
//        new WeChatExternalUser()                   | null            | null                                                                 | null    | null          | null                        | new BusinessAccount() | null            | "外部联系人unionid不能为空！"
//        new WeChatExternalUser(unionid: "unionId") | null            | null                                                                 | null    | null          | null                        | new BusinessAccount() | null            | "添加信息不能为空！"
//        new WeChatExternalUser(unionid: "unionId") | null            | new JoinBusinessDTO(state: "SubAccountVer132")                       | null    | null          | null                        | new BusinessAccount() | null            | "获取商家数据失败！"
//        new WeChatExternalUser(unionid: "unionId") | null            | new JoinBusinessDTO(state: "SubAccountVer132")                       | null    | null          | null                        | new BusinessAccount() | new Business()  | "请求参数手机号不能为空！"
//        new WeChatExternalUser(unionid: "unionId") | null            | new JoinBusinessDTO(state: "SubAccountVer132", phone: "***********") | null    | new BizUser() | null                        | new BusinessAccount() | new Business()  | "手机号已注册！"
//        new WeChatExternalUser(unionid: "unionId") | null            | new JoinBusinessDTO(state: "SubAccountVer132", phone: "***********") | null    | null          | new BizUser(phone: "")      | new BusinessAccount() | new Business()  | "登录账号手机号不能为空！"
//        new WeChatExternalUser(unionid: "unionId") | null            | new JoinBusinessDTO(state: "SubAccountVer132", phone: "***********") | null    | null          | new BizUser(phone: "186..") | new BusinessAccount() | new Business()  | "登录账号已存在"
//    }
//
//    void getWeChatInfo() {
//        WechatTokenDTO dto = new WechatTokenDTO();
//        dto.setOpenid("ovHbx6mcZ3nvMhB8")
//        dto.setSessionKey("null")
//        dto.setUnionid("oftcb6noRSkGOI15")
//        dto.setErrcode(null)
//        dto.setErrmsg(null)
//        String weChatTokenDto = JSON.toJSONString(dto)
//        Mockito.when(HttpUtil.get(String.format(WxConstant.WEIXIN_OAUTH2_TOKEN_URL, wechatConfig.getAppId(), wechatConfig.getSecret(), "12312"))).thenReturn(weChatTokenDto)
//
//    }
//}
