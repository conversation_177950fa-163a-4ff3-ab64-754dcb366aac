//package com.wnkx.biz.page.service
//
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
//import com.ruoyi.common.redis.service.RedisService
//import com.ruoyi.system.api.domain.entity.biz.page.PageConfig
//import com.ruoyi.system.api.domain.entity.biz.page.PageConfigInfo
//import com.ruoyi.system.api.domain.entity.order.casus.CasusGroup
//import com.ruoyi.system.api.domain.vo.biz.page.PageChooseCaseDetailVO
//import com.ruoyi.system.api.domain.vo.biz.page.PageChooseCaseVO
//import com.ruoyi.system.api.domain.vo.biz.page.VideoShopWindowVO
//import com.wnkx.biz.casus.service.ICasusGroupService
//import com.wnkx.biz.page.mapper.PageConfigMapper
//import com.wnkx.biz.page.service.impl.PageConfigServiceImpl
//import com.wnkx.biz.tag.service.ITagService
//import spock.lang.Specification
//
//import java.lang.reflect.Field
//
//class PageConfigServiceTest extends Specification {
//    RedisService redisService = Mock()
//    ICasusGroupService casusGroupService = Mock()
//    ITagService tagService = Mock()
//    PageConfigMapper pageConfigMapper = Mock()
//    IPageConfigService pageConfigService = new PageConfigServiceImpl(redisService, casusGroupService, tagService)
//
//    def setup() {
//        // 使用反射注入 baseMapper
//        Field baseMapperField = ServiceImpl.getDeclaredField("baseMapper")
//        baseMapperField.setAccessible(true)
//        baseMapperField.set(pageConfigService, pageConfigMapper)
//    }
//
//    def "保存精选案例页面配置: 请求参数：#pageConfigInfo 异常：#expectedMessage"() {
//        given:
//        pageConfigMapper.selectById(_) >> new PageConfig(platform: 0)
//        casusGroupService.listByIds(_) >> [new CasusGroup(id: 1, name: "分组A"), new CasusGroup(id: 2, name: "分组B")]
//        when:
//        pageConfigService.saveChooseCase(pageConfigInfo)
//
//        then:
//        def exception = thrown(Exception)
//        exception.message == expectedMessage
//
//        where:
//        pageConfigInfo                                      | expectedMessage
//        getPageConfigInfo(0,    1, 1, "海报标题", "图片地址", 1, "跳转uri", 2,    "橱窗名称", 1, 1, "跳转uri") | "不存在对应分组~"
//    }
//
//    def getPageConfigInfo(platform,
//                          moduleType, layoutType, name, imageUrl, skipType, skipUri, detailSize,
//                          videoShopWindowName, videoShopWindowLayoutType, videoShopWindowSkipType, videoShopWindowSkipUri
//
//    ) {
//        PageConfigInfo pageConfigInfo = new PageConfigInfo();
//        pageConfigInfo.id = 1
//        pageConfigInfo.platform = platform
//        def pageChooseCaseVO = getPageChooseCaseVO(moduleType, layoutType, name, imageUrl, skipType, skipUri, detailSize)
//        List<PageChooseCaseVO> pageChooseCaseList = new ArrayList<>()
//        pageChooseCaseList.add(pageChooseCaseVO)
//        pageConfigInfo.pageChooseCaseList = pageChooseCaseList
//        VideoShopWindowVO videoShopWindow = new VideoShopWindowVO()
//        videoShopWindow.name = videoShopWindowName
//        videoShopWindow.layoutType = videoShopWindowLayoutType
//        videoShopWindow.skipType = videoShopWindowSkipType
//        videoShopWindow.skipUri = videoShopWindowSkipUri
//        pageConfigInfo.videoShopWindow = videoShopWindow
//
//        return pageConfigInfo
//    }
//
//    def getPageChooseCaseVO(moduleType, layoutType, name, imageUrl, skipType, skipUri, detailSize) {
//        PageChooseCaseVO pageChooseCaseVO = new PageChooseCaseVO()
//        pageChooseCaseVO.moduleType = moduleType
//        pageChooseCaseVO.layoutType = layoutType
//
//        List<PageChooseCaseDetailVO> list = new ArrayList<>()
//
//        for (int i = 0; i < detailSize; i++) {
//            PageChooseCaseDetailVO pageChooseCaseDetailVO = new PageChooseCaseDetailVO()
//            pageChooseCaseDetailVO.name = name + i
//            pageChooseCaseDetailVO.imageUrl = imageUrl
//            pageChooseCaseDetailVO.skipType = skipType
//            pageChooseCaseDetailVO.skipUri = skipUri
//            list.add(pageChooseCaseDetailVO)
//        }
//        pageChooseCaseVO.pageChooseCaseDetailList = list
//
//        return pageChooseCaseVO
//    }
//}
