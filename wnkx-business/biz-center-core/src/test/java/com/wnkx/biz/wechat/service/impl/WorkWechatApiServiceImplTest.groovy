//package com.wnkx.biz.wechat.service.impl
//
//import com.ruoyi.common.redis.service.RedisService
//import com.ruoyi.system.api.config.WorkWeChatConfig
//import okhttp3.OkHttpClient
//import spock.lang.Specification
//
//import java.util.concurrent.TimeUnit
//
//class WorkWechatApiServiceImplTest extends Specification {
//    WorkWeChatConfig weChatConfig = new WorkWeChatConfig()
//
//    RedisService redisService = Mock()
//
////    Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("************",10809))
//    WeChatContactConfigServiceImpl weChatContactConfigService = Mock()
//    OkHttpClient okHttpClient = new OkHttpClient().newBuilder()
//            .connectTimeout(5, TimeUnit.SECONDS)
//            .readTimeout(10, TimeUnit.SECONDS)
//            .writeTimeout(10, TimeUnit.SECONDS)
//            .proxy(proxy)
//            .build()
//
//    WorkWechatApiServiceImpl workWechatApiService = new WorkWechatApiServiceImpl(
//            weChatConfig,
//            redisService,
//            okHttpClient,
//            weChatContactConfigService
//    )
//
////    def "ContactMeQrcode"() {
////        given:
////        weChatConfig.corpId = "ww2432208455bd86fe"
////        weChatConfig.encodingAESKey = "ocvI35tpHwXBVLkC695lVfilJyWnBzte6R7JuHadcVo"
////        weChatConfig.token = "m2bawBgcOTU9B"
////        weChatConfig.corpSecret = "xyg-k-icSH_18m-K92uxoErfWGx4pCGNIlQ5Jns4fwk"
////        weChatConfig.contactUser = "YeYuJiBei"
////        weChatConfig.distribution = "etPyXlDQAAUiqKqrRgieiPEQj7xqSFAg"
////        weChatConfig.marketing = "etPyXlDQAA35iL_EbCe_4iDfNTybTa0w"
////        when:
////        def qrcode = workWechatApiService.contactMeQrcode(ChannelTypeEnum.DISTRIBUTION, 0)
////        then:
////        qrcode != null
////        qrcode.startsWith("https://wework.qpic.cn")
////
////    }
//
////    def "addTag"() {
////        given:
////        weChatConfig.corpId = "ww2432208455bd86fe"
////        weChatConfig.encodingAESKey = "ocvI35tpHwXBVLkC695lVfilJyWnBzte6R7JuHadcVo"
////        weChatConfig.token = "m2bawBgcOTU9B"
////        weChatConfig.corpSecret = "xyg-k-icSH_18m-K92uxoErfWGx4pCGNIlQ5Jns4fwk"
////        weChatConfig.contactUser = "YeYuJiBei"
////        weChatConfig.distribution = "etPyXlDQAAUiqKqrRgieiPEQj7xqSFAg"
////        weChatConfig.marketing = "etPyXlDQAA35iL_EbCe_4iDfNTybTa0w"
////        when:
////        def qrcode = workWechatApiService.addTag(ChannelTypeEnum.DISTRIBUTION, "---分销渠道")
////        then:
////        qrcode != null
////        print(qrcode)
////
////    }
//
////    def "tagList"() {
////        given:
////        weChatConfig.corpId = "ww2432208455bd86fe"
////        weChatConfig.encodingAESKey = "ocvI35tpHwXBVLkC695lVfilJyWnBzte6R7JuHadcVo"
////        weChatConfig.token = "m2bawBgcOTU9B"
////        weChatConfig.corpSecret = "xyg-k-icSH_18m-K92uxoErfWGx4pCGNIlQ5Jns4fwk"
////        weChatConfig.contactUser = "YeYuJiBei"
////        weChatConfig.distribution = "etPyXlDQAAUiqKqrRgieiPEQj7xqSFAg"
////        weChatConfig.marketing = "etPyXlDQAA35iL_EbCe_4iDfNTybTa0w"
////        when:
////        def qrcode = workWechatApiService.tagList(null, Arrays.asList("etPyXlDQAAUiqKqrRgieiPEQj7xqSFAg", "etPyXlDQAA35iL_EbCe_4iDfNTybTa0w"))
////        then:
////        print(qrcode.toString())
////
////    }
//
////    def "editTag"() {
////        given:
////        weChatConfig.corpId = "ww2432208455bd86fe"
////        weChatConfig.encodingAESKey = "ocvI35tpHwXBVLkC695lVfilJyWnBzte6R7JuHadcVo"
////        weChatConfig.token = "m2bawBgcOTU9B"
////        weChatConfig.corpSecret = "xyg-k-icSH_18m-K92uxoErfWGx4pCGNIlQ5Jns4fwk"
////        weChatConfig.contactUser = "YeYuJiBei"
////        weChatConfig.distribution = "etPyXlDQAAUiqKqrRgieiPEQj7xqSFAg"
////        weChatConfig.marketing = "etPyXlDQAA35iL_EbCe_4iDfNTybTa0w"
////        when:
////        workWechatApiService.editTag(ChannelTypeEnum.DISTRIBUTION, "etPyXlDQAA-01geuSMt_mJiGqKwyqoKA", "FX--分销渠道（改）")
////
////        then:
////        print("")
////        }
//
////    def "getExternalUserInfo"() {
////        given:
////        weChatConfig.corpId = "ww2432208455bd86fe"
////        weChatConfig.encodingAESKey = "ocvI35tpHwXBVLkC695lVfilJyWnBzte6R7JuHadcVo"
////        weChatConfig.token = "m2bawBgcOTU9B"
////        weChatConfig.corpSecret = "xyg-k-icSH_18m-K92uxoErfWGx4pCGNIlQ5Jns4fwk"
////        weChatConfig.contactUser = "YeYuJiBei"
////        weChatConfig.distribution = "etPyXlDQAAUiqKqrRgieiPEQj7xqSFAg"
////        weChatConfig.marketing = "etPyXlDQAA35iL_EbCe_4iDfNTybTa0w"
////        when:
////        def ExternalUserInfoDTO = workWechatApiService.getExternalUserInfo("wmPyXlDQAAAqa6_EK-OrLaMpQXRcZ1pA")
////
////        then:
////        print(ExternalUserInfoDTO.toString())
////        }
//
////    def "markTag"() {
////        given:
////        weChatConfig.corpId = "ww2432208455bd86fe"
////        weChatConfig.encodingAESKey = "ocvI35tpHwXBVLkC695lVfilJyWnBzte6R7JuHadcVo"
////        weChatConfig.token = "m2bawBgcOTU9B"
////        weChatConfig.corpSecret = "xyg-k-icSH_18m-K92uxoErfWGx4pCGNIlQ5Jns4fwk"
////        weChatConfig.contactUser = "YeYuJiBei"
////        weChatConfig.distribution = "etPyXlDQAAUiqKqrRgieiPEQj7xqSFAg"
////        weChatConfig.marketing = "etPyXlDQAA35iL_EbCe_4iDfNTybTa0w"
////
////        CustomerTagEdit customerTagEdit = new CustomerTagEdit()
////        customerTagEdit.userid = "YeYuJiBei"
////        customerTagEdit.external_userid = "wmPyXlDQAAAqa6_EK-OrLaMpQXRcZ1pA"
////        customerTagEdit.add_tag = ["etPyXlDQAA-01geuSMt_mJiGqKwyqoKA"]
////
////
////        when:
////        workWechatApiService.markTag(customerTagEdit)
////
////        then:
////        print("成功")
////        }
//
//}
