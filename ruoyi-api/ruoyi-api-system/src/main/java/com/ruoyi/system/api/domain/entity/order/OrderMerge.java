package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2025-03-04 09:58:55
 */
@Data
@TableName("order_merge")
public class OrderMerge implements Serializable {
    private static final long serialVersionUID = 4915049263251867490L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 支付单号
     */
    @ApiModelProperty(value = "支付单号")
    private String payNum;

    /**
     * 合并人biz_user_id
     */
    @ApiModelProperty(value = "合并人biz_user_id")
    private Long mergeBizUserId;

    /**
     * 合并人微信名
     */
    @ApiModelProperty(value = "合并人微信名")
    private String mergeNickBy;

    /**
     * 合并人姓名
     */
    @ApiModelProperty(value = "合并人姓名")
    private String mergeBy;

    /**
     * 合并人ID
     */
    @ApiModelProperty(value = "合并人ID")
    private Long mergeById;

    /**
     * 合并人business_id
     */
    @ApiModelProperty(value = "合并人business_id")
    private Long mergeBusinessId;

    /**
     * 合并时间
     */
    @ApiModelProperty(value = "合并时间")
    private Date mergeTime;

    /**
     * 合并状态(1：正常，2：关闭，3：完成)
     */
    @ApiModelProperty(value = "合并状态(1：正常，2：关闭，3：完成)")
    private Integer status;

    /**
     * 合并单完成时间
     */
    @ApiModelProperty(value = "合并单完成时间")
    private Date completeTime;

    /**
     * 关闭合并时间
     */
    @ApiModelProperty(value = "关闭合并时间")
    private Date closeMergeTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
