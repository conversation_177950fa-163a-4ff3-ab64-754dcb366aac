package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 标记发货
 *
 * <AUTHOR>
 * @date 2024/12/26 14:17
 */
@Data
public class FlagShippingDTO implements Serializable {
    private static final long serialVersionUID = 8555899163149109637L;

    /**
     * 视频订单主键id
     */
    @ApiModelProperty(value = "视频订单主键id", required = true)
    @NotNull(message = "[视频订单主键id]不能为空")
    private Long videoId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 150, message = "[备注]长度不能超过150个字符")
    private String remark;

    @Null(message = "请勿传递[rollbackId]")
    private Long rollbackId;
}
