package com.ruoyi.system.api.domain.dto.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 17:47
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberStatusDto implements Serializable {
    private static final long serialVersionUID = -7924549156541405839L;


    @NotNull(message="[商家id]不能为空")
    @ApiModelProperty("商家id")
    private Long businessId;

    @ApiModelProperty("登录账号id")
    @NotNull(message="[登录账号id]不能为空")
    private Long bizUserId;

    @ApiModelProperty("订单号")
    private String orderNum;

    /**
     * 会员编码
     */
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("会员编码")
    @Length(max= 10,message="编码长度不能超过10")
    private String memberCode;
    /**
     * 会员类型: 0-非会员，1-会员
     */
    @ApiModelProperty("会员类型: 0-非会员，1-会员")
    private Integer memberType;
    /**
     * 会员状态：0-非会员1-正常，2-即将过期，3-已过期
     */
    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;
    /**
     * 会员套餐名称
     */
    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberPackageType;
    /**
     * 会员首次购买时间
     */
    @ApiModelProperty("会员首次购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberFirstTime;
    /**
     * 会员最近购买时间
     */
    @ApiModelProperty("会员最近购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberLastTime;
    /**
     * 会员有效期
     */
    @ApiModelProperty("会员有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberValidity;

    @ApiModelProperty("购买会员次数")
    private Long rechargeCount;

    @ApiModelProperty("选择套餐类型: 0-季度套餐，1-一年会员，2-三年会员")
    private Integer choosePackageType;

    @ApiModelProperty("选择会员状态:0-非会员1-正常，2-即将过期，3-已过期'")
    private Integer chooseMemberStatus;

    @ApiModelProperty("会员添加月份时间")
    private Integer monthNum;

    @ApiModelProperty("加赠时间")
    private Integer presentedTime;

    @ApiModelProperty("加赠时间类型（1-天,2-月,3-年）")
    private Integer presentedTimeType;

    @ApiModelProperty("实际支付金额")
    private BigDecimal realPayAmount;

    /**
     * 订单实付金额（对应币种实付）
     */
    @ApiModelProperty("订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    /**
     * 币种（详见sys_dict_type.dict_type = sys_money_type）
     */
    @ApiModelProperty("币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

}
