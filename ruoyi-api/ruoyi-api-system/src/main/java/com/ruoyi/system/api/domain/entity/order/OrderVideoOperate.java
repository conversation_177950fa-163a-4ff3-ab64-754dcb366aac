package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/14 9:33
 */
@Data
@TableName("order_video_operate")
public class OrderVideoOperate implements Serializable {
    private static final long serialVersionUID = -5779651359277400774L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty("视频id (FK:order_video.id)")
    private Long videoId;

    /**
     * 事件名称
     */
    @ApiModelProperty("事件名称")
    private String eventName;

    /**
     * 事件执行对象（1:商家,2:运营,3:模特,9:系统）
     */
    @ApiModelProperty("事件执行对象（1:商家,2:运营,3:模特,9:系统）")
    private Integer eventExecuteObject;

    /**
     * 事件执行人用户id
     */
    @ApiModelProperty("事件执行人用户id")
    private Long eventExecuteUserId;

    /**
     * 事件执行人用户名称
     */
    @ApiModelProperty("事件执行人用户名称")
    private String eventExecuteUserName;

    /**
     * 事件执行人微信昵称
     */
    @ApiModelProperty("事件执行人微信昵称")
    private String eventExecuteNickName;

    /**
     * 事件执行人手机号
     */
    @ApiModelProperty("事件执行人手机号")
    private String eventExecutePhone;

    /**
     * 事件执行时间
     */
    @ApiModelProperty("事件执行时间")
    private Date eventExecuteTime;

    /**
     * 事件内容
     */
    @ApiModelProperty("事件内容")
    private String eventContent;

    /**
     * 事件内容（商家看的）
     */
    @ApiModelProperty("事件内容（商家看的）")
    private String eventContentCompany;

    /**
     * 是否是购物车(1:是)
     */
    @ApiModelProperty("是否是购物车(1:是)")
    private Integer isCart;

    /**
     * 是否公开（0:公开,1:只显示给运营）
     */
    @ApiModelProperty("是否公开（0:公开,1:只显示给运营）")
    private Integer isPublic;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
