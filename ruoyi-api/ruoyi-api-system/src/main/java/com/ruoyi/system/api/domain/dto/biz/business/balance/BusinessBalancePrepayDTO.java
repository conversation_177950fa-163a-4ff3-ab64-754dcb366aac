package com.ruoyi.system.api.domain.dto.biz.business.balance;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.PayTypeEnum;
import com.ruoyi.common.core.enums.PrepayPayTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商家预付表
 *
 * <AUTHOR>
 * @TableName business_balance_prepay
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalancePrepayDTO implements Serializable {

    private static final long serialVersionUID = -981735608546630181L;

    @NotNull(message = "[商家ID]不能为空")
    @ApiModelProperty("商家ID")
    private Long businessId;

    @NotNull(message = "[申请增加金额（单位：￥）]不能为空")
    @ApiModelProperty("申请增加金额（单位：￥）")
    private BigDecimal amount;

    @NotNull(message = "[含赠送金额]不能为空")
    @ApiModelProperty(value = "包含赠送金额")
    @DecimalMin(value = "0.00", message = "[含赠送金额]不能小于0")
    private BigDecimal containPresentedAmount;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payAmountDollar;

    @Size(max = 5, message = "[支付凭证]最多上传5张")
    @Size(min = 1, message = "至少需要有一条支付凭证数据")
    private List<String> resourceIds;

    @ApiModelProperty("支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账，7-全币种,99-其他)")
    @NotNull(message = "[支付方式]不能为空")
    @EnumValid(enumClass = PrepayPayTypeEnum.class, message = "[支付方式]输入错误")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    @EnumValid(enumClass = PayTypeEnum.PayTypeDetailEnum.class, message = "[支付方式明细]输入错误")
    private Integer payTypeDetail;

    @ApiModelProperty("收款账号ID")
    private Long accountId;

    @Size(max = 100, message = "[申请备注]不能超过100")
    @ApiModelProperty("申请备注")
    private String applyRemark;

    @AssertTrue(message = "[收款账号ID]不能为空")
    private boolean isCheckAccountId() {
        if (!PrepayPayTypeEnum.OTHER.getCode().equals(payType)) {
            return ObjectUtil.isNotNull(accountId);
        }
        return true;
    }
    @AssertTrue(message = "[含赠送金额]不能大于[申请增加金额]")
    private boolean isCheckContainPresentedAmount() {
        if (ObjectUtil.isNull(containPresentedAmount) || ObjectUtil.isNull(amount)){
            return true;
        }
        if (containPresentedAmount.subtract(amount).compareTo(BigDecimal.ZERO) <= 0) {
            return true;
        }
        return false;
    }
    @AssertTrue(message = "[应付金额]不能为空")
    private boolean isCheckPayAmountDollar() {
        if (PrepayPayTypeEnum.FULL_CURRENCY.getCode().equals(payType)) {
            return ObjectUtil.isNotNull(payAmountDollar);
        }
        return true;
    }

    @AssertTrue(message = "选择全币种支付时，[支付方式明细]不能为空")
    private boolean isPayTypeDetail() {
        if (PayTypeEnum.FULL_CURRENCY.getCode().equals(payType) || PayTypeEnum.FULL_CURRENCY_BALANCE.getCode().equals(payType)) {
            return ObjectUtil.isNotNull(payTypeDetail);
        } else {
            return ObjectUtil.isNull(payTypeDetail);
        }
    }
}
