package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PastOrPresent;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/10 16:15
 */
@Data
public class UploadInvoiceDTO implements Serializable {
    private static final long serialVersionUID = -5698245338795885324L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "[主键]不能为空")
    private Long id;

    /**
     * 发票objectKey
     */
//    @ApiModelProperty(value = "发票objectKey")
//    @NotBlank(message = "[发票]不能为空")
//    private String objectKey;

    @Size(max = 5, message = "[发票]最多可以传5张")
    @Size(min = 1, message = "[发票]至少需要传1张")
    @NotNull(message = "[发票]不能为空")
    private List<String> objectKeys;

    /**
     * 发票号
     */
    @ApiModelProperty(value = "发票号")
    @NotBlank(message = "[发票号]不能为空")
    @Size(max = 100, message = "[发票号]长度不能超过100个字符")
    private String number;

    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    @NotNull(message = "[开票时间]不能为空")
    @PastOrPresent(message = "[开票时间]不能为未来时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date invoicingTime;
}
