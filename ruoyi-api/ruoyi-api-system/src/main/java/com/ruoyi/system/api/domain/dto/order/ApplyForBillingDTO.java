package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.OrderInvoiceTitleTypeEnum;
import com.ruoyi.common.core.enums.OrderInvoiceTypeEnum;
import com.ruoyi.common.core.enums.OrderTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 15:35
 */
@Data
public class ApplyForBillingDTO implements Serializable {
    private static final long serialVersionUID = -3909507981805903420L;

    /**
     * 大订单ID
     */
    @ApiModelProperty(value = "大订单ID")
    @NotEmpty(message = "[大订单ID]不能为空")
    private List<Long> orderId;

    /**
     * 发票类型（1：增值税普通发票，2：形式发票）
     */
    @ApiModelProperty(value = "发票类型（1：增值税普通发票，2：形式发票）")
    @NotNull(message = "[发票类型]不能为空")
    @EnumValid(enumClass = OrderInvoiceTypeEnum.class, message = "[发票类型]输入错误")
    private Integer invoiceType;

    /**
     * 抬头类型（1：企业单位）
     */
    @ApiModelProperty(value = "抬头类型（1：企业单位）")
    @EnumValid(enumClass = OrderInvoiceTitleTypeEnum.class, message = "[抬头类型]输入错误")
    private Integer titleType;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    @Size(max = 100, message = "[发票抬头]长度不能超过100")
    private String title;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    @Size(max = 100, message = "[企业税号]长度不能超过100")
    private String dutyParagraph;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @Size(max = 100, message = "[公司名称]长度不能超过100")
    private String companyName;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    @Size(max = 150, message = "[公司地址]长度不能超过150")
    private String companyAddress;

    /**
     * 公司联系电话
     */
    @ApiModelProperty(value = "公司联系电话")
    @Size(max = 150, message = "[联系电话]长度不能超过150")
    @Pattern(regexp = "^[0-9]+(-[0-9]+)?$", message = "[联系电话]只能包含数字和一个'-'")
    private String companyPhone;

    /**
     * 公司联系人
     */
    @ApiModelProperty(value = "公司联系人")
    @Size(max = 150, message = "[联系人]长度不能超过150")
    private String companyContact;

    /**
     * 附件URI
     */
    @ApiModelProperty(value = "附件URI")
    private String attachmentObjectKey;

    /**
     * 发票内容
     */
    @ApiModelProperty(value = "发票内容")
    @NotBlank(message = "[发票内容]不能为空")
    @Size(max = 7, message = "[发票内容]长度不能超过7")
    private String content;

    /**
     * 发票备注
     */
    @ApiModelProperty(value = "发票备注")
    @Size(max = 300, message = "[发票备注]长度不能超过300")
    private String invoiceRemark;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    @NotNull(message = "[订单类型]不能为空")
    private Integer orderType;

    @AssertTrue(message = "[增值税普通发票]抬头类型、发票抬头、企业税号不能为空")
    private boolean isValueAddedTaxInvoice() {
        if (OrderInvoiceTypeEnum.VALUE_ADDED_TAX_INVOICE.getCode().equals(invoiceType)) {
            return ObjectUtil.isNotNull(titleType)
                    && CharSequenceUtil.isNotBlank(title)
                    && CharSequenceUtil.isNotBlank(dutyParagraph)
                    && CharSequenceUtil.isBlank(companyName)
                    && CharSequenceUtil.isBlank(companyAddress)
                    && CharSequenceUtil.isBlank(companyPhone)
                    && CharSequenceUtil.isBlank(companyContact)
                    && CharSequenceUtil.isBlank(attachmentObjectKey)
                    ;
        }
        return true;
    }

    @AssertTrue(message = "[形式发票]公司名称、公司地址不能为空")
    private boolean isProformaInvoice() {
        if (OrderInvoiceTypeEnum.PROFORMA_INVOICE.getCode().equals(invoiceType)) {
            return CharSequenceUtil.isNotBlank(companyName)
                    && CharSequenceUtil.isNotBlank(companyAddress)
                    && ObjectUtil.isNull(titleType)
                    && CharSequenceUtil.isBlank(title)
                    && CharSequenceUtil.isBlank(dutyParagraph)
                    ;
        }
        return true;
    }

    @AssertTrue(message = "[订单类型]只能选择视频订单、会员订单、预付款订单")
    private boolean isOrderType() {
        return OrderTypeEnum.VIDEO_ORDER.getCode().equals(orderType) || OrderTypeEnum.VIP_ORDER.getCode().equals(orderType)|| OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(orderType);
    }
}
