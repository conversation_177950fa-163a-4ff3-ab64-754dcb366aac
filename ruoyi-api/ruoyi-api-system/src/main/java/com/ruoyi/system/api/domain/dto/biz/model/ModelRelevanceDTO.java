package com.ruoyi.system.api.domain.dto.biz.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21 19:07
 */
@Data
@ApiModel("模特修改关联人员入参")
public class ModelRelevanceDTO implements Serializable {
    private static final long serialVersionUID = 2241064957065820064L;
    @ApiModelProperty(value = "用户id")
    @NotEmpty(message = "[英文客服]不能为空")
    @Size(max = 1, message = "[英文客服]只能选择一个")
    private List<Long> userIds;
    @ApiModelProperty(value = "模特id")
    @NotEmpty(message = "[模特id]不能为空")
    @Size(max = 50, message = "[模特]最多选择50个")
    private List<Long> modelIds;
}
