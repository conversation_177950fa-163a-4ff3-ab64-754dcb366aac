package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/15 16:36
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SaveBatchOrderVideoMatchDTO {
    private Long videoId;
    private Long modelId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 当前操作是否是回退
     */
    private Boolean isRollback;
}
