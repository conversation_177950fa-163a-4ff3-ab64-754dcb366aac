package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/6 17:28
 */
@Data
public class OrderVideoRefundSuccessListDTO implements Serializable {
    private static final long serialVersionUID = -3541007859715155018L;
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @ApiModelProperty(value = "退款类型（1:补偿,2:取消订单,3:取消选配）")
    private List<Integer> refundType;

    /**
     * 时间-开始
     */
    @ApiModelProperty(value = "时间-开始")
    private Date timeBegin;

    /**
     * 时间-结束
     */
    @ApiModelProperty(value = "时间-结束")
    private Date timeEnd;
}
