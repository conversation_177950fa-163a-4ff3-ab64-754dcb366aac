package com.ruoyi.system.api.domain.entity.biz.page;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
* 页面配置表
* <AUTHOR>
 * @TableName page_config
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageConfig implements Serializable {

    private static final long serialVersionUID = -7053268823004100831L;
    @NotNull(message="[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotBlank(message="[页面名称]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("页面名称")
    @Length(max= 20,message="编码长度不能超过20")
    private String name;

    @NotNull(message="[1-首页，2-精选案例，3-其他]不能为空")
    @ApiModelProperty("1-首页，2-精选案例，3-其他")
    private Integer type;

    @ApiModelProperty("链接地址")
    private String link;

    @NotBlank(message="[页面配置]不能为空")
    @Size(max= -1,message="编码长度不能超过-1")
    @ApiModelProperty("页面配置")
    @Length(max= -1,message="编码长度不能超过-1")
    private String content;

    @ApiModelProperty("平台类型：0:Amazon,1:tiktok,2:其他")
    private Integer platform;

    @ApiModelProperty("权限标识")
    private String userPerms;

    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
