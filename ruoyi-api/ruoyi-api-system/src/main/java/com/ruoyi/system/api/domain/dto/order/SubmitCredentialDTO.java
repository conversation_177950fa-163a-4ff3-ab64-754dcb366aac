package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.PayTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 提交凭证DTO
 *
 * <AUTHOR>
 * @date 2024/6/15 10:12
 */
@Data
public class SubmitCredentialDTO implements Serializable {
    private static final long serialVersionUID = 390584696113684004L;

    /**
     * 合并单ID
     */
    @ApiModelProperty(value = "合并单ID")
    private Long mergeId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 支付账户
     */
    @ApiModelProperty(value = "支付账户", required = true)
    // @NotBlank(message = "[支付账户]不能为空")
    @Size(max = 100, message = "[支付账户]不能超过100个字符")
    private String payAccount;

    /**
     * 资源URI
     */
    @ApiModelProperty(value = "资源URI", required = true)
    @NotEmpty(message = "[转账截图]不能为空")
    @Size(min = 1, max = 5, message = "[转账截图]只允许上传1~5张")
    private List<String> objectKeys;

    /**
     * 支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额）
     */
    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额）")
    @NotNull(message = "[支付方式]不能为空")
    @EnumValid(enumClass = PayTypeEnum.class, message = "[支付方式]输入错误")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    @EnumValid(enumClass = PayTypeEnum.PayTypeDetailEnum.class, message = "[支付方式明细]输入错误")
    private Integer payTypeDetail;

    @ApiModelProperty(value = "税点费用（单位：￥）")
    private BigDecimal taxPointCost;

    @ApiModelProperty(value = "还需支付金额（单位：￥）")
    @NotNull(message = "[还需支付金额]不能为空")
    private BigDecimal payAmount;

    /**
     * 还需支付金额，计算优惠与额外收费（单位：$）
     */
    @ApiModelProperty(value = "还需支付金额，计算优惠与额外收费（单位：$）")
    @NotNull(message = "[payAmountDollar]不能为空")
    private BigDecimal payAmountDollar;

    /**
     * 种草码
     */
    @ApiModelProperty("种草码")
    @Size(min = 1, max = 10, message = "[种草码]长度应在1~10个字符之间")
    private String seedCode;

    @AssertTrue(message = "[支付账户]不能为空")
    private boolean isFullCurrency() {
        if (PayTypeEnum.FULL_CURRENCY.getCode().equals(payType)) {
            return CharSequenceUtil.isNotBlank(payAccount);
        }
        return true;
    }


    @AssertTrue(message = "[合并单ID]和[订单编号]不能同时为空")
    private boolean isMerge() {
        return CharSequenceUtil.isNotBlank(orderNum) || ObjectUtil.isNotNull(mergeId);
    }
    @AssertTrue(message = "[合并单ID]和[订单编号]两者二选一")
    private boolean isMergeParam() {
        if (ObjectUtil.isNotNull(mergeId)) {
            return CharSequenceUtil.isBlank(orderNum);
        }
        if (CharSequenceUtil.isNotBlank(orderNum)) {
            return ObjectUtil.isNull(mergeId);
        }
        return true;
    }

    @AssertTrue(message = "选择全币种支付时，[支付方式明细]不能为空")
    private boolean isPayTypeDetail() {
        if (PayTypeEnum.FULL_CURRENCY.getCode().equals(payType) || PayTypeEnum.FULL_CURRENCY_BALANCE.getCode().equals(payType)) {
            return ObjectUtil.isNotNull(payTypeDetail);
        } else {
            return ObjectUtil.isNull(payTypeDetail);
        }
    }
}
