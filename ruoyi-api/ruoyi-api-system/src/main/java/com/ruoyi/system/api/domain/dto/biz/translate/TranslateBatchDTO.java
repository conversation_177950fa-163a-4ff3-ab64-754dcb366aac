package com.ruoyi.system.api.domain.dto.biz.translate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 文本翻译DTO
 *
 * <AUTHOR>
 * @date 2024/6/11
 */
@Data
public class TranslateBatchDTO implements Serializable {
    private static final long serialVersionUID = -2136444697070484402L;
    @ApiModelProperty(value = "原文列表")
    List<String> wordList;
    @ApiModelProperty(value = "语言0:en,1:zh")
    Integer language;
}
