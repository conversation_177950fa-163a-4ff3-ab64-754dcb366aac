package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单_会员订单_流转记录表
 *
 * <AUTHOR>
 * @TableName order_member_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderMemberFlow implements Serializable {


    private static final long serialVersionUID = 1713626471416659741L;
    @TableId(type = IdType.AUTO)
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[订单号]不能为空")
    @ApiModelProperty("订单号")
    private String orderNum;

    @ApiModelProperty("处理人ID")
    private Long userId;

    @NotBlank(message = "[事件名称]不能为空")
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("事件名称")
    @Length(max = 20, message = "编码长度不能超过20")
    private String eventName;

    @NotNull(message = "[事件执行对象（1:商家,9:系统）]不能为空")
    @ApiModelProperty("事件执行对象（1:商家,9:系统）")
    private Integer eventExecuteObject;

    @NotBlank(message = "[事件执行人名称]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("事件执行人名称")
    @Length(max = 30, message = "编码长度不能超过30")
    private String eventExecuteUser;

    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("事件执行人微信昵称")
    @Length(max = 50, message = "编码长度不能超过50")
    private String eventExecuteNickName;

    @Size(max = 15, message = "编码长度不能超过15")
    @ApiModelProperty("事件执行人手机号")
    @Length(max = 15, message = "编码长度不能超过15")
    private String eventExecutePhone;

    @NotNull(message = "[事件执行时间]不能为空")
    @ApiModelProperty("事件执行时间")
    private Date eventExecuteTime;

    @ApiModelProperty("原先订单状态（1待支付、2待审核、3交易成功、4交易关闭）")
    private Integer originStatus;

    @NotNull(message = "[目标订单状态（1待支付、2待审核、3交易成功、4交易关闭）]不能为空")
    @ApiModelProperty("目标订单状态（1待支付、2待审核、3交易成功、4交易关闭）")
    private Integer targetStatus;

    @Size(max = 300, message = "编码长度不能超过300")
    @ApiModelProperty("备注")
    @Length(max = 300, message = "编码长度不能超过300")
    private String remark;

    @ApiModelProperty("创建时间")
    private Date createTime;

}
