package com.ruoyi.system.api.domain.entity.biz.common;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:25
 */
@ApiModel(value = "文本历史对象")
@TableName("text_table_history")
@Data
public class TextHistory implements Serializable {

    private static final long serialVersionUID = 4521104681199548181L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 文本表主键 (FK:text_table.id)
     */
    @ApiModelProperty(value = "文本表主键 (FK:text_table.id)")
    private Long textId;

    /**
     * 文本名称
     */
    @ApiModelProperty(value = "文本名称")
    private String name;

    /**
     * 文本内容
     */
    @ApiModelProperty(value = "文本内容")
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
