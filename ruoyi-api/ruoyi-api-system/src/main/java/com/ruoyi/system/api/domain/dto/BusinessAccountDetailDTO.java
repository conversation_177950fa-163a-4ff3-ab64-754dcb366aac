package com.ruoyi.system.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 商家账号表
 * <AUTHOR>
 * @TableName business_account
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessAccountDetailDTO implements Serializable {

    private static final long serialVersionUID = 5765176570342930295L;
    @ApiModelProperty("账号ID列表")
    private Collection<Long> ids;

    @ApiModelProperty("账号列表")
    private Set<String> accounts;

    @ApiModelProperty("商家id")
    private Long businessId;

    @ApiModelProperty("商家ID列表")
    private Collection<Long> businessIds;

    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("名称")
    @Length(max= 20,message="编码长度不能超过20")
    private String name;

    @Size(max = 32, message = "微信昵称不能超过32个字")
    @ApiModelProperty("微信昵称")
    @Length(max = 32, message = "微信昵称不能超过32个字")
    private String nickName;

    @Size(max= 16,message="编码长度不能超过16")
    @ApiModelProperty("unionId")
    @Length(max= 16,message="编码长度不能超过16")
    private String unionid;

    @NotNull(message="[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;

    @NotNull(message="[商家主账号（FK：business_account.account）]不能为空")
    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;

    @ApiModelProperty("商家账号")
    private String account;

    @NotBlank(message="[商家名称]不能为空")
    @Size(max= 50,message="[商家名称]长度不能超过50")
    @ApiModelProperty("商家名称")
    private String businessName;

    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("会员编码")
    @Length(max= 10,message="编码长度不能超过10")
    private String memberCode;

    @ApiModelProperty("会员状态列表")
    private List<Integer> memberStatusList;

    @ApiModelProperty("登录用户账号ID列表")
    private Collection<Long> userIds;

    @ApiModelProperty("模糊搜索：账号微信名、企业名称、会员账号")
    private String searchNameMemberCodeAccount;

    @ApiModelProperty("是否主账号")
    private Integer isOwnerAccount;

    @ApiModelProperty("种草官ID列表")
    private Collection<String> seedIds;
}