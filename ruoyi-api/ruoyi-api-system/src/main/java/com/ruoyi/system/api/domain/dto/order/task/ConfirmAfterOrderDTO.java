package com.ruoyi.system.api.domain.dto.order.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-13 11:19
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfirmAfterOrderDTO implements Serializable {
    private static final long serialVersionUID = -5115718959215670985L;

    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "[主键]不能为空")
    private Long id;

    @ApiModelProperty(value = "售后内容", required = true)
    @Size(max = 5000, message = "[售后内容]长度不能超过5000字符")
    @NotBlank(message = "[售后内容]不能为空")
    private String remark;
}
