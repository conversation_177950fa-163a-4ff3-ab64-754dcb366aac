package com.ruoyi.system.api.domain.dto.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/5
 */
@Data
public class EditOrderChangeModelListDTO implements Serializable {
    private static final long serialVersionUID = -4923590806494525412L;
    /**
     * 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "平台（0:Amazon,1:tiktok,2:其他,3:APP/解说类）", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    @NotNull(message = "[平台]不能为空")
    private Integer platform;

    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    @NotNull(message = "[国家]不能为空")
    private Integer nation;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    private Integer type;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String account;

    /**
     * 性别(0:男,1:女)
     */
    @ApiModelProperty(value = "性别（1:男,0:女）", notes = "1:男,0:女")
    private List<Integer> sex;

    /**
     * 模特标签
     */
    @ApiModelProperty(value = "模特标签")
    private List<Long> tags;

    /**
     * 擅长品类
     */
    @ApiModelProperty(value = "擅长品类")
    private List<Long> specialtyCategory;

    /**
     * 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
     */
    @ApiModelProperty(value = "年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）", notes = "1:婴幼儿,2:儿童,3:成年人,4:老年人")
    private List<Integer> ageGroup;

    @Null(message = "请勿传递[cannotModel]参数")
    private List<Long> cannotModel;

    @ApiModelProperty(value = "需要过滤黑名单的登录账号ID")
    private Long filterBlackListBizUserId;
}
