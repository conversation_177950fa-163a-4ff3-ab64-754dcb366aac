package com.ruoyi.system.api.domain.dto.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;

/**
 * <AUTHOR>
 * @date 2024/11/4 18:00
 */
@Data
public class ModelSortDTO {
    /**
     * 模特ID
     */
    @ApiModelProperty(value = "模特ID")
    @NotNull(message = "[模特ID]不能为空")
    private Long id;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @Max(value = 999999, message = "[排序值]必须小于等于999999")
    @PositiveOrZero(message = "[排序值]必须大于等于0")
    @NotNull(message = "[排序值]不能为空")
    private Integer sort;
}
