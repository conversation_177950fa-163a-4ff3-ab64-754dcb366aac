package com.ruoyi.system.api.domain.dto.biz.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-10 10:24
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VisitMarketingChannelDTO implements Serializable {

    @ApiModelProperty("渠道code")
    private String dedicatedLinkCode;

    @ApiModelProperty("浏览器指纹")
    private String fingerprint;
}
