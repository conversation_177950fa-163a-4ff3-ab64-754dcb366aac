package com.ruoyi.system.api.domain.dto.order.pay;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/21 19:46
 */
@Data
public class PayInfoExportDTO implements Serializable {
    private static final long serialVersionUID = -2562029730616158864L;

    /**
     * 会员编码
     */
    private String merchantCode;

    /**
     * 订单合计
     */
    private String mergePayAmount;

    /**
     * 余额抵扣
     */
    private String mergeUseBalance;

    /**
     * 优惠合计
     */
    private String mergePromotionAmountSum;

    /**
     * 还需支付
     */
    private String mergeNeedPayAmount;

    /**
     * 大订单明细
     */
    private List<PayInfoOrderExportDTO> payInfoOrderExportDTOS;
}
