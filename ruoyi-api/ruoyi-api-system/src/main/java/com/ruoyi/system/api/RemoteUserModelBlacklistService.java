package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.entity.biz.model.UserModelBlacklist;
import com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO;
import com.ruoyi.system.api.factory.RemoteUserModelBlacklistFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/21 10:00
 */
@FeignClient(contextId = "remoteUserModelBlacklistService", value = ServiceNameConstants.BIZ_SERVICE, fallbackFactory = RemoteUserModelBlacklistFactory.class)
public interface RemoteUserModelBlacklistService {

    /**
     * 获取当前用户拉黑模特列表
     */
    @GetMapping("/business/blacklist/black-model-list")
    List<UserModelBlacklist> selectBlackModelListByBizUserId(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取用户拉黑模特列表
     */
    @GetMapping("/business/blacklist/black-model-list/by-biz-user-id")
    List<UserBlackModelVO> userBlackModelListByBizUserId(@RequestParam("bizUserId") Long bizUserId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取拉黑该模特用户列表
     */
    @GetMapping("/business/blacklist/black-model-list/by-model-id")
    List<UserModelBlacklist> userBlackModelListByModelId(@RequestParam("modelId") Long modelId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取拉黑该模特的商家ID
     */
    @GetMapping("/business/blacklist/get-black-model-business-ids-by-model-id")
    List<Long> getBlackModelBusinessIdsByModelId(@RequestParam("modelId") Long modelId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 批量获取多个用户的拉黑模特列表
     */
    @PostMapping("/business/blacklist/batch-black-model-list")
    List<UserBlackModelVO> batchUserBlackModelListByBizUserIds(@RequestBody List<Long> bizUserIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
