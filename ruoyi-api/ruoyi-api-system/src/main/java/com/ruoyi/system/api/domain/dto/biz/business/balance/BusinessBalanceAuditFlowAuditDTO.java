package com.ruoyi.system.api.domain.dto.biz.business.balance;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 余额提现审核表
 *
 * <AUTHOR>
 * @TableName business_balance_audit_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceAuditFlowAuditDTO implements Serializable {

    private static final long serialVersionUID = -2421547779501655219L;

    @ApiModelProperty("主键ID")
    @NotNull(message = "[主键ID]不能为空")
    private Long id;

    @ApiModelProperty("商家ID")
    @NotNull(message = "[商家ID]不能为空")
    private Long businessId;

    @ApiModelProperty("实付金额（单位：￥）")
    @DecimalMin(value = "0", message = "实付金额不能小于0")
    private BigDecimal realAmount;

    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty("图片资源地址url")
    private String resourceUrl;

    @NotNull(message = "[审核状态]不能为空")
    @ApiModelProperty("审核状态（0:待处理,1:已提现,2.已取消）")
    private Integer auditStatus;


    @Size(max = 1000, message = "备注不能超过1000")
    @ApiModelProperty("备注")
    private String remark;

}
