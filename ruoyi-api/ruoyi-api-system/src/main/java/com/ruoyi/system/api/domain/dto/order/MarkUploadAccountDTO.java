package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/24 13:54
 */
@Data
public class MarkUploadAccountDTO implements Serializable {
    private static final long serialVersionUID = -384683109574586653L;

    /**
     * 上传链接ID
     */
    @ApiModelProperty(value = "上传链接ID", required = true)
    @NotNull(message = "[上传链接ID]不能为空")
    private Long uploadLinkId;

    /**
     * 上传账号
     */
    @ApiModelProperty(value = "上传账号", required = true)
    @NotBlank(message = "[上传账号]不能为空")
    @Size(max = 50, message = "[上传账号]长度不能超过50个字符")
    private String uploadAccount;
}
