package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.PayTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 获取二维码
 *
 * <AUTHOR>
 * @date 2024/6/15 10:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PayCodeDTO implements Serializable {
    private static final long serialVersionUID = 8935275558208962078L;

    /**
     * 匹配单ID
     */
    @ApiModelProperty(value = "匹配单ID")
    private Long mergeId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 种草码
     */
    @ApiModelProperty("种草码")
    @Size(min = 1, max = 10, message = "[种草码]长度应在1~10个字符之间")
    private String seedCode;

    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额）")
    @EnumValid(enumClass = PayTypeEnum.class, message = "[支付方式]输入错误")
    private Integer payType;

    @AssertTrue(message = "[合并单ID]和[订单编号]不能同时为空")
    private boolean isMerge() {
        return CharSequenceUtil.isNotBlank(orderNum) || ObjectUtil.isNotNull(mergeId);
    }

    @AssertTrue(message = "[合并单ID]和[订单编号]两者二选一")
    private boolean isMergeParam() {
        if (ObjectUtil.isNotNull(mergeId)) {
            return CharSequenceUtil.isBlank(orderNum);
        }
        if (CharSequenceUtil.isNotBlank(orderNum)) {
            return ObjectUtil.isNull(mergeId);
        }
        return true;
    }
}
