package com.ruoyi.system.api.domain.dto.biz.channel.fission;

import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :提现初始化
 * @create :2025-05-19 15:37
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MemberSeedRecordWithdrawalDTO implements Serializable {
    private static final long serialVersionUID = -4782827301571875862L;

    @ApiModelProperty("提现的会员种草记录")
    @NotNull(message = "[提现的会员种草记录]不能为空")
    @Size(min = 1, message = "[提现的会员种草记录]至少要有一条")
    private List<MemberSeedRecord> memberSeedRecordList;

    @ApiModelProperty("提现信息")
    @NotNull(message = "[提现信息]不能为空")
    private WithdrawalDTO withdrawalDTO;
}
