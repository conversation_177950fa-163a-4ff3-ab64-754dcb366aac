package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/7 19:14
 */
@Data
public class AuditInvoiceDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "[主键]不能为空")
    private Long id;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    @DecimalMax(value = "9999999.99", message = "[开票金额]不能超过9999999.99")
    @DecimalMin(value = "0.01", message = "[开票金额]不能低于0.01")
    private BigDecimal invoiceAmount;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    @Size(max = 100, message = "[发票抬头]长度不能超过100")
    private String title;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    @Size(max = 100, message = "[企业税号]长度不能超过100")
    private String dutyParagraph;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @Size(max = 100, message = "[公司名称]长度不能超过100")
    private String companyName;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    @Size(max = 150, message = "[公司地址]长度不能超过150")
    private String companyAddress;

    /**
     * 公司联系电话
     */
    @ApiModelProperty(value = "公司联系电话")
    @Size(max = 150, message = "[公司联系电话]长度不能超过150")
    private String companyPhone;

    /**
     * 公司联系人
     */
    @ApiModelProperty(value = "公司联系人")
    @Size(max = 150, message = "[公司联系人]长度不能超过150")
    private String companyContact;

    /**
     * 附件URI
     */
    @ApiModelProperty(value = "附件URI")
    private String attachmentObjectKey;

    /**
     * 注意事项
     */
    @ApiModelProperty(value = "注意事项")
    @Size(max = 1000, message = "注意事项长度不能超过1000个字符")
    private String cautions;
}
