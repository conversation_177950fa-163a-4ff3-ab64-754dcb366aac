package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21 9:10
 */
@Data
public class GetEditDTO implements Serializable {
    private static final long serialVersionUID = 1427298216386811112L;

    /**
     * 模特反馈素材详情ID
     */
    @ApiModelProperty(value = "模特反馈素材详情ID")
    private List<Long> ids;

    /**
     * 是否选择全部
     */
    @ApiModelProperty(value = "是否选择全部")
    private Boolean selectAll;

    @AssertTrue(message = "[ids]和[selectAll]两者二选一")
    private boolean isCheckParam() {
        if (CollUtil.isNotEmpty(ids)) {
            return ObjectUtil.isNull(selectAll);
        } else {
            return ObjectUtil.isNotNull(selectAll);
        }
    }
}
