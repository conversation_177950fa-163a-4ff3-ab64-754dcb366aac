package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/11/19 11:37
 */
@Data
public class AddPreselectModelDTO {
    /**
     * 匹配单ID
     */
    @ApiModelProperty(value = "匹配单ID", required = true)
    @NotNull(message = "[匹配单ID]不能为空")
    private Long matchId;
    /**
     * 模特ID
     */
    @ApiModelProperty(value = "模特ID", required = true)
    @NotEmpty(message = "[模特ID]不能为空")
    private List<Long> modelIds;
}
