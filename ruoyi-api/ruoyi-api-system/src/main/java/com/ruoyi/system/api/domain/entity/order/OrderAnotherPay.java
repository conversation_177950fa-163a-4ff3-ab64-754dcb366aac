package com.ruoyi.system.api.domain.entity.order;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/6 13:32
 */
@Data
public class OrderAnotherPay implements Serializable {
    private static final long serialVersionUID = 9044027241775309059L;

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * uuid
     */
    @ApiModelProperty("uuid")
    private String uuid;

    /**
     * 合并单ID
     */
    @ApiModelProperty("合并单ID")
    private Long mergeId;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNum;

    /**
     * 订单类型（0-视频订单，1-会员订单）
     */
    @ApiModelProperty("订单类型（0-视频订单，1-会员订单）")
    private Integer orderType;

    /**
     * 是否有效（1-有效， 0-无效）
     */
    @ApiModelProperty("是否有效（1-有效， 0-无效）")
    private Integer status;

    /**
     * 是否已支付（1：已支付，0：未支付）
     */
    @ApiModelProperty("是否已支付（1：已支付，0：未支付）")
    private Integer isPay;

    /**
     * 种草码
     */
    @ApiModelProperty("种草码")
    private String seedCode;

    /**
     * 创建人id biz_user.id
     */
    @ApiModelProperty("创建人id")
    private Long bizUserId;

    /**
     * 创建人微信名
     */
    @ApiModelProperty("创建人微信名")
    private String createNickBy;

    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    private String createBy;

    /**
     * 创建人ID
     */
    @ApiModelProperty("创建人ID")
    private Long createById;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}
