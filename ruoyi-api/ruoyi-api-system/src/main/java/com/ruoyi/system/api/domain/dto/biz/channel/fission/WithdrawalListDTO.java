package com.ruoyi.system.api.domain.dto.biz.channel.fission;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :裂变拉新结算
 * @create :2025-05-19 17:38
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawalListDTO implements Serializable {
    private static final long serialVersionUID = 7767322172055493214L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("套餐类型：0-季度套餐,1-年度套餐,2-三年套餐")
    private Integer memberPackageType;

    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @ApiModelProperty("结算类型（1-固定金额，2-固定比例）")
    private Integer settleType;

    @ApiModelProperty("提现账号类型(2-支付宝，3-银行卡，6-公户收款)")
    private Integer withdrawalAccountType;

    @ApiModelProperty("状态（2-待审核，3-待打款，4-已打款，5-审核不通过，6-打款异常）")
    private Integer memberSeedRecordWithdrawalStatus;

    @ApiModelProperty("状态（2-待审核，3-待打款，4-已打款，5-审核不通过，6-打款异常）")
    private List<Integer> memberSeedRecordWithdrawalStatusList;

    @ApiModelProperty(value = "申请时间开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date startTime;

    @ApiModelProperty(value = "申请时间结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date endTime;

    @ApiModelProperty("关键字搜索登录账号列表")
    private List<Long> keywordBizUserIds;

    @ApiModelProperty(value = "结算时间开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date settleStartTime;

    @ApiModelProperty(value = "结算时间结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date settleEndTime;

}
