package com.ruoyi.system.api.domain.entity.biz.model;

import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模特黑名单
 *
 * <AUTHOR>
 * @TableName user_model_blacklist
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserModelBlacklist implements Serializable {

    private static final long serialVersionUID = 3652122233544723341L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[登录用户ID FK biz_user.id]不能为空")
    @ApiModelProperty("登录用户ID FK biz_user.id")
    private Long bizUserId;

    @NotNull(message = "[模特ID  FK model.id]不能为空")
    @ApiModelProperty("模特ID  FK model.id")
    private Long modelId;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;
}
