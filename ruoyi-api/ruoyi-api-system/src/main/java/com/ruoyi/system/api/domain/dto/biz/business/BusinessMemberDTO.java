package com.ruoyi.system.api.domain.dto.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 10:19
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessMemberDTO implements Serializable {
    private static final long serialVersionUID = -3920762309739600680L;

    @NotNull(message="[商家主账号（FK：business_account.account）]不能为空")
    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;

    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("会员编码")
    @Length(max= 10,message="编码长度不能超过10")
    private String memberCode;

    @NotNull(message="[会员类型: 0-非会员，1-会员]不能为空")
    @ApiModelProperty("会员类型: 0-非会员，1-会员")
    private Integer memberType;

    @NotNull(message="[会员状态：0-非会员1-正常，2-即将过期，3-已过期]不能为空")
    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;

    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberPackageType;

    @ApiModelProperty("会员最近购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberLastTime;

    @ApiModelProperty("会员有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberValidity;
}
