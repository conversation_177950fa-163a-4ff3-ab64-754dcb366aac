package com.ruoyi.system.api.domain.dto.order.logistic;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-04-27 17:41
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ModelUpdateAddressDTO implements Serializable {
    private static final long serialVersionUID = -516278210031568331L;
    @ApiModelProperty("模特ID")
    @NotNull(message = "[模特ID]不能为空")
    private Long modelId;

    @ApiModelProperty("模特名称")
    @NotBlank(message = "[模特名称]不能为空")
    private String modelName;
}
