package com.ruoyi.system.api.domain.dto.biz.business.balance;

import com.ruoyi.common.core.enums.PrepayPayTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :修改appId
 * @create :2025-04-11 15:34
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PrepayUpdateAppIdDTO implements Serializable {
    private static final long serialVersionUID = -7643379086614238828L;

    @ApiModelProperty(value = "预付单号列表")
    @Size(min = 1, message = "[预付单号列表]至少需要一条数据")
    @NotNull(message = "[预付单号列表]不能为空")
    private List<String> prepayNums;

    @ApiModelProperty("支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账，7-全币种,99-其他)")
    @EnumValid(enumClass = PrepayPayTypeEnum.class, message = "[支付方式]输入错误")
    private Integer payType;

    @ApiModelProperty(value = "appid")
    private String appId;

    @ApiModelProperty(value = "支付用户ID")
    private Long payUserId;

    @ApiModelProperty(value = "收款账号ID")
    private Long accountId;

    @ApiModelProperty(value = "是否是其他支付方式")
    private Integer isAnother;
}
