package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 10:40
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessAccountSonDTO implements Serializable {
    private static final long serialVersionUID = 4789382275049058921L;

    @NotNull(message="[商家主账号（FK：business_account.account）]不能为空")
    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;

    @Size(max= 20,message="编码长度不能超过20")
    @Size(min= 2,message="编码长度不能小于2")
    @ApiModelProperty("名称")
    @Length(max= 20,message="编码长度不能超过20")
    @Length(min= 2,message="编码长度不能小于2")
    @NotBlank(message = "[员工名称]不能为空")
    private String name;

    @NotNull(message="[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;

    @ApiModelProperty("ticket")
    @NotBlank(message = "[ticket]不能为空")
    private String ticket;
}
