package com.ruoyi.system.api.domain.dto.biz.business.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :换绑主账号
 * @create :2025-01-16 11:48
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnBindOwnerDTO implements Serializable {
    private static final long serialVersionUID = 6285199993372363696L;

    @ApiModelProperty("换绑账号ID")
    @NotNull(message = "[换绑账号ID]不能为空")
    private Long accountId;

    @ApiModelProperty("商家ID")
    @NotNull(message = "[换绑账号]不能为空")
    private Long businessId;
}
