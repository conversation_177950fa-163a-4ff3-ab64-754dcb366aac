package com.ruoyi.system.api.domain.dto.biz.business;

import com.ruoyi.common.core.enums.BusinessIdentifierEnum;
import com.ruoyi.common.core.enums.BusinessScaleTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 09:40
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EditBusinessDTO implements Serializable {
    private static final long serialVersionUID = 8093391892514113380L;

    @NotNull(message="[商家主账号（FK：business_account.account）]不能为空")
    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;
    /**
     * 商家名称
     */
    @NotBlank(message="[商家名称]不能为空")
    @Size(max= 50,message="[商家名称]长度不能超过50")
    @ApiModelProperty("商家名称")
    private String name;
    /**
     * 是否为代理(0:否,1:是)
     */
    @NotNull(message="[是否为代理(0:否,1:是)]不能为空")
    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;
    /**
     * 是否展示手机号(0:否,1:是)
     */
    @NotNull(message="[是否展示手机号(0:否,1:是)]不能为空")
    @ApiModelProperty("是否展示手机号(0:否,1:是)")
    private Integer phoneVisible;
    /**
     * 客户类型 （0-一般客户 1-重要客户）
     */
    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;

    @ApiModelProperty("商家标识 （0-新客 1-老客 2-'-'）")
    @NotNull(message="[商家标识]不能为空")
    @EnumValid(enumClass = BusinessIdentifierEnum.class, message = "[商家标识]输入错误")
    private Integer businessIdentifier;

    @NotNull(message="[对接客服  FK：sys_user.user_id]不能为空")
    @ApiModelProperty("对接客服  FK：sys_user.user_id")
    private Long waiterId;

    @ApiModelProperty("规模")
    @EnumValid(enumClass = BusinessScaleTypeEnum.class, message = "[商家规模]输入错误")
    private String scale;
}
