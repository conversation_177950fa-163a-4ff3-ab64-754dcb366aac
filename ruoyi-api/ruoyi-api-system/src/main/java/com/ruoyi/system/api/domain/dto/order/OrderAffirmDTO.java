package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.GroupSequence;
import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import javax.validation.groups.Default;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/9 14:57
 */
@Data
public class OrderAffirmDTO implements Serializable {
    private static final long serialVersionUID = -494225177168261824L;

    @GroupSequence({Default.class, ConditionalValidationSequence.class})
    public interface ValidationSequence {
    }

    public interface ConditionalValidationSequence {
    }

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    @NotNull(message = "[视频id]不能为空", groups = Default.class)
    private Long videoId;

    /**
     * 商家是否需要运营上传素材至平台（0:需要,1:不需要）
     */
    @ApiModelProperty(value = "商家是否需要运营上传素材至平台（0:需要,1:不需要）", required = true)
    @NotNull(message = "[商家是否需要运营上传素材至平台]不能为空", groups = Default.class)
    private Integer needUpload;

    /**
     * 需要上传的链接
     */
    @ApiModelProperty(value = "需要上传的链接")
    @Pattern(regexp = "^https://www\\.amazon\\.com.*$", message = "[上传的链接]非Amazon链接", groups = ConditionalValidationSequence.class)
    private String needUploadLink;

    /**
     * 视频标题
     */
    @ApiModelProperty(value = "视频标题")
    @Size(min = 10, max = 60, message = "[视频标题]长度应在10~60个字符之间", groups = ConditionalValidationSequence.class)
    @Pattern.List({
            // 1. 至少需要包含3个单词（根据中间空格来识别）
            @Pattern(regexp = "^(?=(.*\\b[A-Za-z]+\\b.*){3,}).*$", message = "[视频标题]至少需要包含3个完整的单词", groups = ConditionalValidationSequence.class),

            // 2. 不允许包含输入*表情符号（匹配所有可能的 emoji）
            @Pattern(regexp = "^(?:(?![\\p{So}\\p{Cn}\\p{Cs}]).)*$", message = "[视频标题]不允许包含表情符号", groups = ConditionalValidationSequence.class),

            // 3. 不允许包含“$”
            @Pattern(regexp = "^[^$]*$", message = "[视频标题]不允许包含'$'", groups = ConditionalValidationSequence.class),

            // 4. 不允许包含外语字符（非ASCII字符）
            @Pattern(regexp = "^[\\x00-\\x7F]*$", message = "[视频标题]不允许包含外语字符（非ASCII字符）", groups = ConditionalValidationSequence.class),

            // 5. 不允许包含产品ASIN（连续全大写+数字混合10位）
            @Pattern(regexp = "^(?!.*\\b(?=[A-Z0-9]{10}\\b)(?!(?:[A-Z]{10}|\\d{10})\\b)[A-Z0-9]{10}\\b).*$", message = "[视频标题]不允许包含产品ASIN", groups = ConditionalValidationSequence.class),

            // 6. 不允许包含全大写或全数字
            @Pattern(regexp = "^(?!^[A-Z\\d\\s]+$)(?!^\\d+$).*$", message = "[视频标题]不允许包含全大写或全数字", groups = ConditionalValidationSequence.class)
    })
    private String videoTitle;

    /**
     * 视频封面图URI
     */
    @ApiModelProperty(value = "视频封面图（资源URI）")
    private String videoCover;

    /**
     * 视频订单吐槽对象
     */
    @ApiModelProperty(value = "视频订单吐槽对象")
    @Valid
    private OrderVideoRoastDTO roast;

    // 使用@AssertTrue注解来实现条件验证
    @AssertTrue(message = "[视频标题]和[上传的链接]在需要上传时不能为空", groups = Default.class)
    private boolean isNeedUploadValid() {
        if (needUpload != null && needUpload == 0) {
            return videoTitle != null && !videoTitle.trim().isEmpty() &&
                    needUploadLink != null && !needUploadLink.trim().isEmpty();
        }
        return true;
    }
}
