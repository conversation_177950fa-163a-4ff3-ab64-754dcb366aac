package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 收款人账号配置列表
 */
@Data
public class OrderPayeeAccountConfigDTO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "主体名称")
    private String accountName;

    @ApiModelProperty(value = "状态")
    private Integer status;

    private Long detailId;

    @ApiModelProperty(value = "类型(1-微信,2-支付宝,7-全币种,6-对公账户)")
    private Integer type;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    @ApiModelProperty(value = "创建人姓名")
    private String createBy;

    @ApiModelProperty(value = "更新人姓名")
    private String updateBy;

    @ApiModelProperty(value = "更新人ID")
    private Long updateById;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    @ApiModelProperty(value = "收款人账号信息")
    private OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO;

}