package com.ruoyi.system.api.domain.dto.order.casus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-10 15:55
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GroupVideoDTO implements Serializable {
    private static final long serialVersionUID = -9029205125377495324L;

    @NotNull(message="[分组ID]不能为空")
    @ApiModelProperty("分组ID")
    private Long groupId;

    @NotNull(message="[视频id列表]不能为空")
    @ApiModelProperty("视频id列表")
    @Size(min = 1, message = "至少需要有一条视频数据")
    private List<Long> videoIds;
}
