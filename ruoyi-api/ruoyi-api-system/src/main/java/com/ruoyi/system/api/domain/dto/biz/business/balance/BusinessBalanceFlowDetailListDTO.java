package com.ruoyi.system.api.domain.dto.biz.business.balance;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 余额流水表
 *
 * <AUTHOR>
 * @TableName business_balance_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceFlowDetailListDTO implements Serializable {
    private static final long serialVersionUID = -4222816942427326172L;
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeBegin;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeEnd;

}
