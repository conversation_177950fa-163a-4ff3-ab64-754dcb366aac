package com.ruoyi.system.api.domain.dto.biz.tag;

import com.ruoyi.common.core.enums.ModelTagEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 标签列表入参对象
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@ApiModel(value = "标签列表入参对象")
@Data
public class TagListDTO implements Serializable {
    private static final long serialVersionUID = 6285873997017304347L;
    /**
     * 分类id（1009:模特标签,1008:类目标签）
     */
    @ApiModelProperty(value = "分类id（1009:模特标签,1008:类目标签）", required = true)
    @NotNull(message = "[分类id]不能为空")
    @EnumValid(enumClass = ModelTagEnum.class, message = "[分类id]输入错误")
    private Long categoryId;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String name;

    /**
     * 状态（0:启用,1:禁用）
     */
    @ApiModelProperty(value = "状态（0:启用,1:禁用）")
    private List<Integer> status;
}
