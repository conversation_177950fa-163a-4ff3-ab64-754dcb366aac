package com.ruoyi.system.api.domain.entity.order.casus;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 案例视频表
 *
 * <AUTHOR>
 * @TableName case_video
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CasusVideo implements Serializable {

    private static final long serialVersionUID = 3829202046537531601L;
    @NotNull(message = "[主键id]不能为空")
    @ApiModelProperty("主键id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotBlank(message = "[视频名称]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("视频名称")
    @Length(max = 32, message = "编码长度不能超过32")
    private String name;

    @NotBlank(message = "[封面图片]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("封面图片")
    @Length(max = 255, message = "编码长度不能超过255")
    private String pic;

    @NotBlank(message = "[视频链接]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("视频链接")
    @Length(max = 255, message = "编码长度不能超过255")
    private String link;

    @NotNull(message = "[平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类]不能为空")
    @ApiModelProperty("平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    @NotBlank(message = "[创建人]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("创建人")
    @Length(max = 32, message = "编码长度不能超过32")
    private String createBy;

    @ApiModelProperty("创建人id")
    private Long createId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @NotBlank(message = "[修改人]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("修改人")
    @Length(max = 32, message = "编码长度不能超过32")
    private String updateBy;

    @ApiModelProperty("修改人id")
    private Long updateId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
