package com.ruoyi.system.api.domain.entity.biz.channel;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
* 会员市场渠道记录表
* @TableName order_member_marketing_channel
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderMemberMarketingChannel implements Serializable {

    @NotNull(message="[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message="[渠道id（marketing_channel.id）]不能为空")
    @ApiModelProperty("渠道id（marketing_channel.id）")
    private Long channelId;

    @NotNull(message="[市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)]不能为空")
    @ApiModelProperty("市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)")
    private Integer marketingPlatform;

    @NotBlank(message="[渠道名称（marketing_channel.channel_name）]不能为空")
    @Size(max= 27,message="编码长度不能超过27")
    @ApiModelProperty("渠道名称（marketing_channel.channel_name）")
    @Length(max= 27,message="编码长度不能超过27")
    private String channelName;

    @NotNull(message="[商家id（business.id）]不能为空")
    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    @NotBlank(message="[商家名称（business.name）]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("商家名称（business.name）")
    @Length(max= 50,message="编码长度不能超过50")
    private String businessName;

    @NotNull(message="[商家端登录用户id FK:biz_user.id]不能为空")
    @ApiModelProperty("商家端登录用户id FK:biz_user.id")
    private Long bizUserId;

    @NotBlank(message="[商家端登录用户微信昵称]不能为空")
    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("商家端登录用户微信昵称")
    @Length(max= 32,message="编码长度不能超过32")
    private String bizUserNickName;

    @NotBlank(message="[商家端登录用户手机号]不能为空")
    @Size(max= 15,message="编码长度不能超过15")
    @ApiModelProperty("商家端登录用户手机号")
    @Length(max= 15,message="编码长度不能超过15")
    private String bizUserPhone;

    @NotBlank(message="[会员编码（business.member_code）]不能为空")
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("会员编码（business.member_code）")
    @Length(max= 10,message="编码长度不能超过10")
    private String memberCode;

    @NotNull(message="[套餐类型：0=季度会员,1=年度会员,2=三年会员]不能为空")
    @ApiModelProperty("套餐类型：0=季度会员,1=年度会员,2=三年会员")
    private Integer memberPackageType;

    @ApiModelProperty("订单号")
    private String orderNum;

    @NotNull(message="[订单实付金额（单位：￥）]不能为空")
    @ApiModelProperty("订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @NotBlank(message="[备注]不能为空")
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("备注")
    @Length(max= 100,message="编码长度不能超过100")
    private String remark;

    @ApiModelProperty("购买时间")
    private Date createTime;

}
