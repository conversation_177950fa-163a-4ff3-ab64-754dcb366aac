package com.ruoyi.system.api.domain.entity.biz.translate;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @TableName translation_cache
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TranslationCache implements Serializable {

    private static final long serialVersionUID = 8576209006242862690L;
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotBlank(message = "[原文]不能为空")
    @Size(max = 8000, message = "[原文]长度不能超过8000")
    @ApiModelProperty("原文")
    @Length(max = 8000, message = "[原文]长度不能超过8000")
    private String sourceText;

    @ApiModelProperty("译文")
    private String translatedText;

    @ApiModelProperty("原文sha256")
    private String sourceSha256;

}
