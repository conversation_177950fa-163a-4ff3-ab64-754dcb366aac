package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024-06-20
 */
@Data
public class OrderVideoRefundListDTO implements Serializable {
    private static final long serialVersionUID = -8276781625786446602L;
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 退款审批号
     */
    @ApiModelProperty(value = "退款审批号")
    private String refundNum;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    @Excel(name = "视频编码")
    private String videoCode;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 拍摄模特名称
     */
    @ApiModelProperty(value = "拍摄模特名称")
    private String shootModelName;

    /**
     * 拍摄模特账号
     */
    @ApiModelProperty(value = "拍摄模特账号")
    private String modelAccount;

    /**
     * 退款状态（0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）
     */
    @ApiModelProperty(value = "退款状态（0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）")
    private List<Integer> refundStatus;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @ApiModelProperty(value = "退款类型（1:补偿,2:取消订单,3:取消选配）")
    private List<Integer> refundType;

    /**
     * 时间-开始
     */
    @ApiModelProperty(value = "时间-开始")
    private Date timeBegin;

    /**
     * 时间-结束
     */
    @ApiModelProperty(value = "时间-结束")
    private Date timeEnd;

    /**
     * 根据拍摄模特名称获取的模特id
     */
    private Set<Long> modelIds;

    /**
     * 根据关键字获取的运营id
     */
    private Set<Long> backUserIds;

    /**
     * 当前登录商家id
     */
    private Long loginBusinessId;
}
