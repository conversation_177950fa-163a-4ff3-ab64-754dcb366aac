package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.RoastObjectEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27
 */
@Data
public class OrderVideoRoastListDTO implements Serializable {

    @ApiModelProperty(value = "吐槽对象(1:视频,2:客服,3:其他)")
    @EnumValid(enumClass = RoastObjectEnum.class, message = "[吐槽对象]输入错误")
    private Integer object;

    @ApiModelProperty(value = "处理状态（0:待处理,1:已处理）")
    @EnumValid(enumClass = StatusTypeEnum.class, message = "[处理状态]输入错误")
    private Integer handleStatus;

    @ApiModelProperty(value = "吐槽类型(0:视频吐槽,1:系统吐槽)")
    @NotNull(message = "吐槽类型不能为空")
    @EnumValid(enumClass = StatusTypeEnum.class, message = "[吐槽类型]输入错误")
    private Integer roastType;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @ApiModelProperty(value = "中文客服")
    private List<Long> chineseWaiterIds;

    @ApiModelProperty(value = "商家id列表")
    private List<Long> businessIds;

    @ApiModelProperty(value = "英文客服")
    private List<Long> englishWaiterIds;
}
