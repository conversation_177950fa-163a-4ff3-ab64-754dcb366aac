package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.ReplyEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 修改拍摄要求DTO入参
 *
 * <AUTHOR>
 * @date 2024/6/1 18:17
 */
@Data
public class ReplyVideoCaseDTO implements Serializable {

    private static final long serialVersionUID = -6203387401094323822L;
    /**
     * 反馈情况主键
     */
    @ApiModelProperty(value = "反馈情况主键", required = true)
    @NotNull(message = "[反馈情况主键]不能为空")
    private Long id;

    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id", required = true)
    @NotNull(message = "[视频订单id]不能为空")
    private Long videoId;

    /**
     * 回复内容(0:待反馈,1:同意,2:不同意)
     */
    @ApiModelProperty(value = "回复内容(0:待反馈,1:同意,2:不同意)", required = true)
    @NotNull(message = "[回复内容]不能为空")
    @EnumValid(enumClass = ReplyEnum.class, message = "[回复内容]输入错误")
    private Integer replyContent;

    /**
     * 原因
     */
    @ApiModelProperty(value = "拒绝原因", required = false)
    private String reason;
}
