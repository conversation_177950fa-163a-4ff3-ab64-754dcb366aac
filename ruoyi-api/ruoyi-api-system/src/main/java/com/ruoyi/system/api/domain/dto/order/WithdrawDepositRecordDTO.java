package com.ruoyi.system.api.domain.dto.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2025/1/7 16:45
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawDepositRecordDTO {

    private Collection<String> videoCodes;
    private Collection<String> prepayNums;
    private Collection<String> videoCodePrepayNums;

    private Collection<Integer> status;
    private Long businessId;
    private Collection<Long> businessIds;
}
