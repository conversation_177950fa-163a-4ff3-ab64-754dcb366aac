
package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoOperateDTO implements Serializable {

    private static final long serialVersionUID = 7451178175768605967L;
    /**
     * 视频id (FK:order_video.id)
     */
    @NotNull(message = "[视频id]不能为空")
    private Long videoId;

    /**
     * 事件内容
     */
    @NotBlank(message = "[事件内容]不能为空")
    private String eventContent;

    /**
     * 事件内容（商家看的）
     */
    private String eventContentCompany;

    /**
     * 事件执行时间
     */
    @ApiModelProperty("事件执行时间")
    private Date eventExecuteTime;
}
