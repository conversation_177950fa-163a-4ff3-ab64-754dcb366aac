package com.ruoyi.system.api.domain.dto.biz.translate;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class TranslateDTO implements Serializable {

    private static final long serialVersionUID = 2483530621863424276L;
    /**
     * 原文
     */
    @ApiModelProperty("原文")
    @NotBlank(message = "原文不能为空")
    private String originText;

    @ApiParam("语言0:en,1:zh")
    private Integer language;
}
