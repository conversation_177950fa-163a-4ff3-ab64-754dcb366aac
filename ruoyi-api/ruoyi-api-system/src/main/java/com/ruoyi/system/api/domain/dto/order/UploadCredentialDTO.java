package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.text.CharSequenceUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 上传凭证DTO
 *
 * <AUTHOR>
 * @date 2024/6/15 10:12
 */
@Data
public class UploadCredentialDTO implements Serializable {
    private static final long serialVersionUID = 4735597341045633767L;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 支付单号
     */
    @ApiModelProperty(value = "支付单号")
    private String payNum;

    /**
     * 0-商家上传， 1-平台上传
     */
    @ApiModelProperty(value = "0-商家上传， 1-平台上传", required = true)
    @NotNull(message = "[上传类型]不能为空")
    private Integer uploadType;

    /**
     * 文件资源
     */
    @ApiModelProperty(value = "文件资源", required = true)
    private List<String> objectKeys;

    @AssertTrue(message = "[支付单号]和[订单编号]不能同时为空")
    private boolean isMerge() {
        return CharSequenceUtil.isNotBlank(orderNum) || CharSequenceUtil.isNotBlank(payNum);
    }
}
