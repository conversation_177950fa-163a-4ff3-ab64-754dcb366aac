package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/28 9:51
 */
@Data
public class AddRecommendDTO implements Serializable {
    private static final long serialVersionUID = -6964226112674980196L;

    /**
     * 模特ID
     */
    @NotNull(message = "[模特ID]不能为空")
    @ApiModelProperty("模特ID")
    private Long modelId;

    /**
     * 视频订单ID
     */
    @NotNull(message = "[视频订单ID]不能为空")
    @ApiModelProperty("视频订单ID")
    private Long videoId;

    /**
     * 要添加的视频订单ID
     */
    @NotEmpty(message = "[要添加的视频订单ID]不能为空")
    @ApiModelProperty("要添加的视频订单ID")
    private List<Long> addRecommendVideoIds;
}
