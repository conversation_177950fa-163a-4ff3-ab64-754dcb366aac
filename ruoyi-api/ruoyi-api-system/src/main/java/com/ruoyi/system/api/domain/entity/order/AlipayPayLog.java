package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付宝回调记录表实体类
 */
@Data
@TableName("alipay_pay_log")
public class AlipayPayLog implements Serializable {

    private static final long serialVersionUID = 338135722948934182L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 内部订单号
     */
    private String order_num;

    /**
     * 通知的发送时间
     */
    private Date notify_time;

    /**
     * 通知类型
     */
    private String notify_type;

    /**
     * 通知校验 ID
     */
    private String notify_id;

    /**
     * 编码格式
     */
    private String charset;

    /**
     * 调用的接口版本
     */
    private String version;

    /**
     * 签名类型
     */
    private String sign_type;

    /**
     * 签名
     */
    private String sign;

    /**
     * 授权方的APPID
     */
    private String auth_app_id;

    /**
     * 支付宝交易号
     */
    private String trade_no;

    /**
     * 支付宝应用的APPID
     */
    private String app_id;

    /**
     * 商家订单号
     */
    private String out_trade_no;

    /**
     * 商家业务号
     */
    private String out_biz_no;

    /**
     * 买家支付宝用户号
     */
    private String buyer_id;

    /**
     * 卖家支付宝账号 ID
     */
    private String seller_id;

    /**
     * 交易状态
     */
    private String trade_status;

    /**
     * 订单金额
     */
    private BigDecimal total_amount;

    /**
     * 实收金额
     */
    private BigDecimal receipt_amount;

    /**
     * 开票金额
     */
    private BigDecimal invoice_amount;

    /**
     * 用户支付金额
     */
    private BigDecimal buyer_pay_amount;

    /**
     * 使用集分宝支付金额
     */
    private BigDecimal point_amount;

    /**
     * 总退款金额
     */
    private BigDecimal refund_fee;

    /**
     * 订单标题
     */
    private String subject;

    /**
     * 商品描述
     */
    private String body;

    /**
     * 交易创建时间
     */
    private Date gmt_create;

    /**
     * 交易付款时间
     */
    private Date gmt_payment;

    /**
     * 交易退款时间
     */
    private Date gmt_refund;

    /**
     * 交易结束时间
     */
    private Date gmt_close;

    /**
     * 支付金额信息
     */
    private String fund_bill_list;

    /**
     * 优惠券信息
     */
    private String vocher_detail_list;

    /**
     * 回传参数
     */
    private String passback_params;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date create_time;
}
