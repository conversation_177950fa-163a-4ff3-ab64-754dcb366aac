package com.ruoyi.system.api.domain.entity.order.casus;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 分组视频关联表
 *
 * <AUTHOR>
 * @TableName group_video
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GroupVideo implements Serializable {

    private static final long serialVersionUID = 7725426643036189558L;
    @NotNull(message = "[分组ID]不能为空")
    @ApiModelProperty("分组ID")
    private Long groupId;

    @NotNull(message = "[视频ID]不能为空")
    @ApiModelProperty("视频ID")
    private Long videoId;

    @NotNull(message = "[排序]不能为空")
    @ApiModelProperty("排序")
    private Integer sort;

    @NotBlank(message = "[创建人]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("创建人")
    @Length(max = 32, message = "编码长度不能超过32")
    private String createBy;

    @ApiModelProperty("创建人id")
    private Long createId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
}
