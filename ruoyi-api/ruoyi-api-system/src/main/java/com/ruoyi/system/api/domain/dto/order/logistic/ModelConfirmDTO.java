package com.ruoyi.system.api.domain.dto.order.logistic;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.ModelResultEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 模特确认
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ModelConfirmDTO implements Serializable {

    private static final long serialVersionUID = -7228672116695046317L;
    @ApiModelProperty("物流跟进Id")
    @NotNull(message = "物流跟进Id不能为空")
    private Long id;

    @ApiModelProperty("模特结果枚举(0-待处理、1-已询问、2-已收货、3-丢件、4-订单回退、5-待通知拍摄、6-已通知拍摄)")
    @NotNull(message = "模特结果不能为空")
    @EnumValid(enumClass = ModelResultEnum.class, message = "[模特结果]输入错误")
    private Integer modelResult;

    @ApiModelProperty("实际签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signTime;

    @ApiModelProperty("说明")
    private String remark;

    @ApiModelProperty("图片地址")
    private List<String> resourceIds;

}
