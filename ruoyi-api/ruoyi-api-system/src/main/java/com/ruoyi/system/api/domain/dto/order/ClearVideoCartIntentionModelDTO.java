package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :清楚视频订单参数
 * @create :2025-01-09 13:53
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClearVideoCartIntentionModelDTO implements Serializable {
    private static final long serialVersionUID = 4900228990377152993L;

    @ApiModelProperty(value = "登录账号")
    @NotNull(message = "[登录账号]不能为空")
    private Long bizUserId;

    @ApiModelProperty(value = "意向模特Id")
    @NotNull(message = "[意向模特Id]不能为空")
    private Long intentionModelId;
}
