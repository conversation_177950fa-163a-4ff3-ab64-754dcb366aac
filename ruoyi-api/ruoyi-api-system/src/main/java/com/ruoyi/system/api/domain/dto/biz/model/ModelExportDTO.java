package com.ruoyi.system.api.domain.dto.biz.model;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.StrPool;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ModelExportDTO implements Serializable {

    private static final long serialVersionUID = 5201338986988302994L;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Excel(name = "模特名字")
    private String name;

    /**
     * 模特id
     */
    @ApiModelProperty(value = "主键")
    @Excel(name = "模特ID")
    private String account;

    /**
     * 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
     */
    @ApiModelProperty(value = "年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）", notes = "1:婴幼儿,2:儿童,3:成年人,4:老年人")
    @Excel(name = "年龄层", readConverterExp = "1=婴幼儿,2=儿童,3=成年人,4=老年人")
    private String ageGroup;

    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    @Excel(name = "国家", readConverterExp = "1=英国,2=加拿大,3=德国,4=法国,5=意大利,6=西班牙,7=美国")
    private String nation;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    @Excel(name = "模特类型", readConverterExp = "0=影响者,1=素人")
    private String type;

    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度（0:一般模特,1:优质模特,2:中度模特）", notes = "0:一般模特,1:优质模特,2:中度模特")
    @Excel(name = "模特等级", readConverterExp = "0=一般模特,1=优质模特,2=中度模特")
    private String cooperation;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)")
    @Excel(name = "模特评分", width = 20)
    private BigDecimal cooperationScore;

    @ApiModelProperty(value = "被收藏数")
    @Excel(name = "收藏数")
    private Long collected;

    /**
     * 售后率
     */
    @ApiModelProperty(value = "售后率")
    @Excel(name = "售后率", suffix = "%")
    private BigDecimal afterSaleRate;


    /**
     * 超时率
     */
    @ApiModelProperty(value = "超时率")
    @Excel(name = "超时率", suffix = "%")
    private BigDecimal overtimeRate;

    /**
     * 可拍数
     */
    @ApiModelProperty(value = "可拍数")
    @Excel(name = "可拍数")
    private Long can;

    /**
     * 待拍数
     */
    @ApiModelProperty(value = "待拍数")
    @Excel(name = "待拍数")
    private Long waits;
    /**
     * 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "平台（0:Amazon,1:tiktok,2:其他,3:APP/解说类）", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    @Excel(name = "合作平台", readConverterExp = "0=Amazon,1=tiktok,2=其他,3:APP/解说类")
    private String platform;

    /**
     * 关联人员
     */
    @Excel(name = "关联人员")
    private String personsExportStr;

    /**
     * 擅长品类
     */
    @Excel(name = "擅长品类")
    private String specialtyCategoryExportStr;

    /**
     * 模特标签
     */
    @Excel(name = "模特标签", height = 100)
    private String tagsExportStr;

    /**
     * 模特标签
     */
    @Excel(name = "模特地址", height = 100, width = 50)
    private String modelAddress;
    /**
     * 亚马逊案例视频数量
     */
    @ApiModelProperty(value = "亚马逊案例视频数量")
    @Excel(name = "亚马逊案例视频数量", height = 100, defaultValue = "0")
    private Integer amazonVideoCount;

    /**
     * Tiktok案例视频数量
     */
    @ApiModelProperty(value = "Tiktok案例视频数量")
    @Excel(name = "Tiktok案例视频数量", height = 100, defaultValue = "0")
    private Integer tikTokVideoCount;

    /**
     * 模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)
     */
    @ApiModelProperty(value = "模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)")
    @Excel(name = "模特状态", readConverterExp = "0=正常合作,1=暂停合作,2=行程中,3=取消合作")
    private String status;

    /**
     * 状态变更日期
     */
    @Excel(name = "状态变更日期", dateFormat = DatePattern.NORM_DATETIME_PATTERN, defaultValue = StrPool.DASHED)
    private Date statusTime;

    @ApiModelProperty(name = "取消合作原因分类")
    @Excel(name = "取消合作原因分类", readConverterExp = "0=我们取消合作,1=模特取消合作", defaultValue = StrPool.DASHED)
    private Integer cancelCooperationType;

    @ApiModelProperty(name = "取消/暂停/行程中原因")
    @Excel(name = "取消/暂停/行程中原因", defaultValue = StrPool.DASHED)
    private String cancelCooperationSubType;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createUserName;

    /**
     * 创建人手机号
     */
    @Excel(name = "创建人手机号")
    private String createUserPhone;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    @Excel(name = "出生日期", dateFormat = "yyyy-MM-dd", defaultValue = StrPool.DASHED)
    private Date birthday;

    /**
     * 家庭成员数量
     */
    @ApiModelProperty(value = "家庭成员数量")
    @Excel(name = "家庭成员数量", defaultValue = StrPool.DASHED)
    private Integer familyNum;
}
