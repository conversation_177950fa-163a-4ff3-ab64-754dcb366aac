package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/31 9:40
 */
@ApiModel("视频_关联内容入参")
@Data
public class VideoContentDTO implements Serializable {
    private static final long serialVersionUID = -871804160156894031L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 视频id
     */
    private Long videoId;

    /**
     * 类型（1:拍摄建议（原拍摄要求）,2:模特要求（原匹配模特注意事项）,3:剪辑要求）
     */
    @ApiModelProperty(value = "类型（1:拍摄建议（原拍摄要求）,2:模特要求（原匹配模特注意事项）,3:剪辑要求）")
    private Integer type;

    /**
     * 内容
     */
    @NotNull(message = "[内容]不能为空")
    @ApiModelProperty(value = "内容", required = true)
    private String content;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", required = true)
    @PositiveOrZero(message = "[排序]必须为正整数")
    private Integer sort;

    @ApiModelProperty(value = "不需要传递")
    private String firstContent;

    //0未改1改
    @ApiModelProperty(value = "修改标志")
    private Integer firstEdit;
}
