package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.system.api.domain.entity.order.OrderVideoMatch;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/29 11:18
 */
@Data
public class AddMatchRecommendDTO implements Serializable {
    private static final long serialVersionUID = 5814753318345258728L;

    /**
     * 模特ID
     */
    @NotNull(message = "[模特ID]不能为空")
    @ApiModelProperty("模特ID")
    private Long modelId;

    /**
     * 要添加的视频订单ID
     */
    @NotEmpty(message = "[要添加的视频订单ID]不能为空")
    @ApiModelProperty("要添加的视频订单ID")
    private List<Long> addRecommendVideoIds;

    @Null(message = "请勿传递[orderVideoMatches]")
    private List<OrderVideoMatch> orderVideoMatches;
}
