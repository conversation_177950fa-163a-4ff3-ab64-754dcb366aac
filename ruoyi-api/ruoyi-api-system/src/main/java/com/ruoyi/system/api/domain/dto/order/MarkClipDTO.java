package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21 14:47
 */
@Data
public class MarkClipDTO implements Serializable {
    private static final long serialVersionUID = 3029960365073751705L;

    /**
     * 模特反馈素材详情ID
     */
    @ApiModelProperty(value = "模特反馈素材详情ID", required = true)
    @NotNull(message = "[模特反馈素材详情ID]不能为空")
    private Long id;

    /**
     * 视频订单ID
     */
    @ApiModelProperty(value = "视频订单ID", required = true)
    @NotNull(message = "[视频订单ID]不能为空")
    private Long videoId;

    /**
     * 视频评分
     */
    @ApiModelProperty("视频评分")
    @Digits(integer = 2, fraction = 1, message = "[视频评分]只能是整数或小数点后一位")
    @DecimalMin(value = "0.5", message = "[视频评分]不能小于0.5")
    @DecimalMax(value = "10.0", message = "[视频评分]不能大于10.0")
    private Float videoScore;

    /**
     * 评价内容
     */
    @ApiModelProperty("评价内容")
    @Size(max = 300, message = "[评价内容]长度不能超过300个字符")
    private String videoScoreContent;

    /**
     * 关联任务ID
     */
    @ApiModelProperty(value = "关联任务ID")
    private List<Long> taskDetailIds;
}
