package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :重新绑定微信
 * @create :2024-06-21 09:29
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RebindDTO implements Serializable {
    private static final long serialVersionUID = -7674948381041908733L;

    @NotNull(message="[账号]不能为空")
    @ApiModelProperty("账号")
    private String account;

    @NotNull(message="[手机号]不能为空")
    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("换绑ticket")
    @NotBlank(message = "[换绑ticket]不能为空")
    private String ticket;

    @ApiModelProperty("账号ticket")
    private String accountTicket;
}
