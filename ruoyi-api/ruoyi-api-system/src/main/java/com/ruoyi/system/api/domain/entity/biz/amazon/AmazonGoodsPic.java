package com.ruoyi.system.api.domain.entity.biz.amazon;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/8 11:48
 */
@Data
@TableName("amazon_goods_pic")
public class AmazonGoodsPic implements Serializable {
    private static final long serialVersionUID = -8200463631245640400L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 亚马逊商品id
     */
    private String goodsId;

    private String productName;

    private String productChineseName;
    /**
     * OSS的相对路径
     */
    private String objectKey;

    private String specInfo;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
