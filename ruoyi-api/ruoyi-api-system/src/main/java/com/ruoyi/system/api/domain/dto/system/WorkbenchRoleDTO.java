package com.ruoyi.system.api.domain.dto.system;

import com.ruoyi.common.core.enums.SelectionManagementEnum;
import com.ruoyi.common.core.enums.WorkbenchRoleTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :工作台角色
 * @create :2025-03-28 18:31
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkbenchRoleDTO implements Serializable {
    private static final long serialVersionUID = -6938036304724346208L;

    @ApiModelProperty(value = "用户Id")
    @NotNull(message = "[用户Id]不能为空")
    private Long userId;

    @ApiModelProperty(value = "工作台角色类型：0-无,1-中文部,2-英文部,3-财务部,4-剪辑部")
    @NotNull(message = "[工作台角色类型]不能为空")
    @EnumValid(enumClass = WorkbenchRoleTypeEnum.class, message = "[工作台角色类型]格式有误")
    private Integer workbenchRoleType;

    @ApiModelProperty(value = "预选管理-沟通中权限：0-查看自己，1-查看全部")
    @EnumValid(enumClass = SelectionManagementEnum.class, message = "[预选管理-沟通中权限]格式有误")
    private Integer selectionManagement;
}
