package com.ruoyi.system.api.domain.dto.biz.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-09 16:27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DistributionChannelOrderDTO implements Serializable {
    private static final long serialVersionUID = -4399285279536398770L;

    @ApiModelProperty("种草码")
    private String seedCode;

    @NotNull(message = "[商家ID]不能为空")
    @ApiModelProperty("商家ID")
    private Long businessId;

    @NotBlank(message = "[订单号]不能为空")
    @ApiModelProperty("订单号")
    private String orderNum;

    @NotNull(message = "[订单金额]不能为空")
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @NotNull(message = "[订单实付金额（单位：￥）]不能为空")
    @ApiModelProperty("订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @ApiModelProperty("支付时间")
    @NotNull(message = "[支付时间]不能为空")
    private Date payTime;
}
