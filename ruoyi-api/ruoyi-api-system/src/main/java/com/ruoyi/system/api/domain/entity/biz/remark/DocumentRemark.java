package com.ruoyi.system.api.domain.entity.biz.remark;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 单据备注对象 document_remark
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@ApiModel(value = "单据备注对象 document_remark")
@TableName("document_remark")
@Data
public class DocumentRemark extends BaseEntity
{

    private static final long serialVersionUID = -7202826798179546844L;
    /** 主键 */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /** 单据id */
    @ApiModelProperty(value = "单据id")
    @Excel(name = "单据id")
    private String documentId;

    /** 单据类型（1:订单、2:工单、3:售后单……） */
    @ApiModelProperty(value = "单据类型",notes = "1:订单、2:工单、3:售后单……")
    @Excel(name = "单据类型", readConverterExp = "1:订单、2:工单、3:售后单……")
    private String documentType;

    /** 备注内容 */
    @ApiModelProperty(value = "备注内容")
    @Excel(name = "备注内容")
    private String content;

}
