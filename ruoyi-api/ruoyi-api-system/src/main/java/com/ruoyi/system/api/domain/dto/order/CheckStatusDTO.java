package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.system.api.domain.entity.order.OrderMerge;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4 15:03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckStatusDTO implements Serializable {
    private static final long serialVersionUID = -6412439150544233789L;

    /**
     * 合并单ID
     */
    @ApiModelProperty(value = "合并单ID")
    private Long mergeId;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNum;

    /**
     * 支付平台
     */
    @ApiModelProperty("支付平台1:微信,2:支付宝")
    private Integer platform;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    @Null(message = "请勿传递[orderNums]")
    private List<String> orderNums;

    /**
     * 合并单
     */
    @Null(message = "请勿传递[orderMerge]")
    private OrderMerge orderMerge;

    @AssertTrue(message = "[合并单ID]和[订单编号]不能同时为空")
    private boolean isMerge() {
        return CharSequenceUtil.isNotBlank(orderNum) || ObjectUtil.isNotNull(mergeId);
    }
    @AssertTrue(message = "[合并单ID]和[订单编号]两者二选一")
    private boolean isMergeParam() {
        if (ObjectUtil.isNotNull(mergeId)) {
            return CharSequenceUtil.isBlank(orderNum);
        }
        if (CharSequenceUtil.isNotBlank(orderNum)) {
            return ObjectUtil.isNull(mergeId);
        }
        return true;
    }
}
