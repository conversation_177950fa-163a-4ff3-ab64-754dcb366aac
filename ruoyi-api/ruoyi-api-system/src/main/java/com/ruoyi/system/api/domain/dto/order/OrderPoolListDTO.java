package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.OrderPoolMatchCountEnum;
import com.ruoyi.common.core.enums.OrderPoolPreselectedTimeEnum;
import com.ruoyi.common.core.enums.OrderPoolPreselectionEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/16 17:26
 */
@Data
public class OrderPoolListDTO implements Serializable {
    private static final long serialVersionUID = 2170988872521856245L;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keyword;

    /**
     * 预选人数
     */
    @ApiModelProperty("预选人数(1:0人,2:1~3人,3:4~9人,4:10人以上)")
    @EnumValid(enumClass = OrderPoolPreselectionEnum.class, message = "[预选人数]输入错误")
    private Integer preselection;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）")
    private Integer modelType;

    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）")
    private List<Integer> modelTypes;

    /**
     * 匹配次数
     */
    @ApiModelProperty(value = "匹配次数（1:一,2:,二,3:三,4:四,5:五,6:五次以上）")
    @EnumValid(enumClass = OrderPoolMatchCountEnum.class, message = "[匹配次数]输入错误")
    private List<Integer> counts;

    /**
     * 匹配单开始时间
     */
    @ApiModelProperty(value = "匹配单开始时间（1:24小时内,2:,1~2天,3:2~3天,4:3天以上）")
    @EnumValid(enumClass = OrderPoolPreselectedTimeEnum.class, message = "[匹配单开始时间]输入错误")
    private List<Integer> matchStartTimes;

    /**
     * 匹配单开始时间-之前
     */
    @ApiModelProperty(value = "匹配单开始时间-之前")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date beforeMatchStartTime;
    /**
     * 匹配单开始时间-之前
     */
    @ApiModelProperty(value = "匹配单开始时间-之前-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date beforeMatchStartTimeStart;
    /**
     * 匹配单开始时间-之前
     */
    @ApiModelProperty(value = "匹配单开始时间-之前-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date beforeMatchStartTimeEnd;

    /**
     * 匹配单结束时间-之前-开始
     */
    @ApiModelProperty(value = "匹配单结束时间-之前-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date beforeMatchEndTimeStart;
    /**
     * 匹配单开始时间-之前-结束
     */
    @ApiModelProperty(value = "匹配单结束时间-之前-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date beforeMatchEndTimeEnd;

    /**
     * 是否照顾单
     */
    @ApiModelProperty(value = "是否照顾单（0=否,1=是）")
    private Integer isCare;

    /**
     * 根据关键字查询出的模特ID
     */
    @Null(message = "请勿传递[intentionModelIds]")
    private Collection<Long> intentionModelIds;

    /**
     * 预选人数开始人数
     */
    @Null(message = "请勿传递[preselectionBegin]")
    private Integer preselectionBegin;

    /**
     * 预选人数结束人数
     */
    @Null(message = "请勿传递[preselectionEnd]")
    private Integer preselectionEnd;

    /**
     * 匹配次数逻辑符sql
     */
    @Null(message = "请勿传递[countSqlList]")
    private List<String> countSqlList;

    /**
     * 当前运营关联模特
     */
    @Null(message = "请勿传递[backUserRelevanceModelIds]")
    private Collection<Long> backUserRelevanceModelIds;


    /**
     * 匹配单开始时间
     */
    @Null(message = "请勿传递[matchStartTimeMap]")
    private Map<String, String> matchStartTimeMap;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private List<Integer> shootingCountries;

    private Boolean currentUserIsAdmin;

    /**
     * 预选管理权限：true-可以查看全部，false-只能查看自己关联的模特
     */
    @Null(message = "请勿传递[canViewAllPreselection]")
    private Boolean canViewAllPreselection;

    @ApiModelProperty("是否待提交-待提交页面需传入")
    private Boolean isPendingSubmit = false;

    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度(0:一般模特,1:优质模特,2:中度模特)")
    private List<Integer> modelCooperations;

    /**
     * 预选模特ID
     */
    @Null(message = "请勿传递[preselectModelIds]")
    private Collection<Long> preselectModelIds;

    @ApiModelProperty(value = "英文部客服名称")
    private List<String> englishCustomerServiceNames;

    @ApiModelProperty(value = "英文部客服id")
    private List<Long> issueIds;

    /**
     * 英文部客服关联模特Id
     */
    @Null(message = "请勿传递[englishCustomerServiceModelIds]")
    private Collection<Long> englishCustomerServiceModelIds;

    /**
     * 淘汰原因
     */
    @ApiModelProperty(value = "淘汰原因")
    private List<Integer> preselectModelOustType;
}
