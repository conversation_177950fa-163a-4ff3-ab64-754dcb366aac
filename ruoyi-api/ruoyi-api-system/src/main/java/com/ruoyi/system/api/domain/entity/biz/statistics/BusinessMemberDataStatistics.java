package com.ruoyi.system.api.domain.entity.biz.statistics;

import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* 商家会员数据统计
* <AUTHOR>
 * @TableName business_member_data_statistics
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class BusinessMemberDataStatistics implements Serializable {

    private static final long serialVersionUID = 2082396825008100971L;

    @NotNull(message="[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message="[记录时间]不能为空")
    @ApiModelProperty("记录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recordTime;

    @NotNull(message="[新会员数]不能为空")
    @ApiModelProperty("新会员数")
    private Long memberCount;

    @NotNull(message="[续费会员数]不能为空")
    @ApiModelProperty("续费会员数")
    private Long renewMemberCount;

    @NotNull(message="[退回会员数]不能为空")
    @ApiModelProperty("退回会员数")
    private Long exitMemberCount;

    @NotNull(message="[到期会员数]不能为空")
    @ApiModelProperty("到期会员数")
    private Long expireMemberCount;

    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
