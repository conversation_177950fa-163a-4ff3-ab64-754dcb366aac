package com.ruoyi.system.api.domain.entity.biz.business.balance;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 商家余额详情表
 *
 * <AUTHOR>
 * @TableName business_balance_detail
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceDetail implements Serializable {

    private static final long serialVersionUID = -4387545116525571523L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[商家id（business.id）]不能为空")
    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    @NotBlank(message = "[单号（自动生成：TK、YF前缀）]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("单号（自动生成：TK、YF前缀）")
    @Length(max = 32, message = "编码长度不能超过32")
    private String number;

    @ApiModelProperty("来源订单号（退款审批号、预付审批单号）")
    private String originNumber;

    @NotBlank(message = "[视频编码]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("视频编码")
    @Length(max = 30, message = "编码长度不能超过30")
    private String videoCode;

    @NotNull(message = "[1-退款订单,2-预付款订单]不能为空")
    @ApiModelProperty("1-退款订单,2-预付款订单")
    private Integer numberType;

    @NotNull(message = "[订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、7.线下钱包充值收入、8.线上钱包充值收入)]不能为空")
    @ApiModelProperty("订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、7.线下钱包充值收入、8.线上钱包充值收入)")
    private Integer origin;

    @NotNull(message = "[订单余额]不能为空")
    @ApiModelProperty("订单余额")
    private BigDecimal balance;

    @NotNull(message = "[使用余额]不能为空")
    @ApiModelProperty("使用余额")
    private BigDecimal useBalance;

    @NotNull(message = "[锁定余额]不能为空")
    @ApiModelProperty("锁定余额")
    private BigDecimal lockBalance;

    @NotNull(message = "[有效余额]不能为空")
    @ApiModelProperty("有效余额")
    private BigDecimal validBalance;

    @ApiModelProperty("下单运营")
    private String createOrderUserName;

    @ApiModelProperty("下单运营微信名")
    private String createOrderUserNickName;

    @ApiModelProperty("支付方式")
    private Integer payType;

    @ApiModelProperty("订单下单时间")
    private Date orderTime;

    @ApiModelProperty("创建人id")
    private Long createById;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("创建人名称")
    @Length(max = 30, message = "编码长度不能超过30")
    private String createBy;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @NotNull(message = "[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    private Date updateTime;
}
