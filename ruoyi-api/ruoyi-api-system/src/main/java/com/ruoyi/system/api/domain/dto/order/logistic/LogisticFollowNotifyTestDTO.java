package com.ruoyi.system.api.domain.dto.order.logistic;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :物流跟进回调服务
 * @create :2025-04-25 10:51
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LogisticFollowNotifyTestDTO implements Serializable {
    private static final long serialVersionUID = -7143496351735969102L;

    @ApiModelProperty("物流单号")
    @NotNull(message = "物流单号不能为空")
    private String number;

    @ApiModelProperty(value = "物流主状态")
    private String logisticMainStatus;

    @ApiModelProperty(value = "物流时间")
    @NotNull(message = "物流时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date logisticUpdateTime;
}
