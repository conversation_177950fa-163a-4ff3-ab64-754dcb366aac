package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单_发票对象DTO
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@ApiModel(value = "订单_发票对象DTO")
@Data
public class OrderInvoiceDTO implements Serializable {
    private static final long serialVersionUID = -1173164000995177071L;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 订单类型（0:视频订单,1:会员订单）
     */
    @ApiModelProperty(value = "订单类型（0:视频订单,1:会员订单）")
    private Integer type;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家id")
    private Long merchantId;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    private String title;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    private String dutyParagraph;

    /**
     * 发票内容
     */
    @ApiModelProperty(value = "发票内容")
    private String content;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private Long operatorBy;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private Long auditBy;

}
