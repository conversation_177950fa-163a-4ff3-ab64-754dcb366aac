package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/21 13:44
 */
@Data
public class RejectRefundDTO implements Serializable {
    private static final long serialVersionUID = 291405992319950801L;
    /**
     * id
     */
    @ApiModelProperty(value = "id", required = true)
    @NotEmpty(message = "[id]不能为空")
    private List<Long> id;
    /**
     * 拒绝理由
     */
    @ApiModelProperty(value = "拒绝理由", required = true)
    @NotBlank(message = "[拒绝理由]不能为空")
    @Size(max = 64, message = "[拒绝理由]长度不能超过64个字符")
    private String rejectCause;
}
