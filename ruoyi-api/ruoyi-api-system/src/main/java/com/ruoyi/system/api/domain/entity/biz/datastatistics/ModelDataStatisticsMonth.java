package com.ruoyi.system.api.domain.entity.biz.datastatistics;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2025-05-07 18:08:14
 */
@Data
@TableName("model_data_statistics_month")
public class ModelDataStatisticsMonth implements Serializable {
    private static final long serialVersionUID = 6687741401693019766L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 记录时间-开始
     */
    @ApiModelProperty(value = "记录时间-开始")
    private Date writeTimeBegin;

    /**
     * 记录时间-结束
     */
    @ApiModelProperty(value = "记录时间-结束")
    private Date writeTimeEnd;

    /**
     * 模特每月新增模特分析JSON
     */
    @ApiModelProperty(value = "模特每月新增模特分析JSON")
    private String newModelAnalysisJson;

    /**
     * 模特每月淘汰模特分析JSON
     */
    @ApiModelProperty(value = "模特每月淘汰模特分析JSON")
    private String oustModelAnalysisJson;

    /**
     * 模特排行榜JSON
     */
    @ApiModelProperty(value = "模特排行榜JSON")
    private String rankingListJson;

    /**
     * 模特新增数量
     */
    @ApiModelProperty(value = "模特新增数量")
    private Long modelNewNumber;

    /**
     * 模特淘汰数量
     */
    @ApiModelProperty(value = "模特淘汰数量")
    private Long modelOustNumber;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
