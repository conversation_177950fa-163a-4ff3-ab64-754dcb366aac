package com.ruoyi.system.api.domain.entity.biz.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 登录用户表
 *
 * <AUTHOR>
 * @TableName biz_user
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class BizUser implements Serializable {

    private static final long serialVersionUID = 5040642647752241015L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotBlank(message = "[员工名称]不能为空")
    @ApiModelProperty("员工名称")
    private String name;

//    @NotBlank(message = "[微信昵称]不能为空")
    @Size(max = 32, message = "微信昵称不能超过32个字")
    @ApiModelProperty("微信昵称")
    @Length(max = 32, message = "微信昵称不能超过32个字")
    private String nickName;

    @NotBlank(message = "[头像]不能为空")
    @Size(max = 500, message = "编码长度不能超过500")
    @ApiModelProperty("头像")
    @Length(max = 500, message = "编码长度不能超过500")
    private String pic;

    @NotBlank(message = "[unionId]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("unionId")
    @Length(max = 32, message = "编码长度不能超过32")
    private String unionid;

    @NotBlank(message = "[手机号]不能为空")
    @Size(max = 15, message = "编码长度不能超过15")
    @ApiModelProperty("手机号")
    @Length(max = 15, message = "编码长度不能超过15")
    private String phone;

    @NotBlank(message = "[ExternalUserID企业微信外部联系人id]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("ExternalUserID企业微信外部联系人id")
    @Length(max = 32, message = "编码长度不能超过32")
    private String externalUserId;

    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;

    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;

    @ApiModelProperty("账号状态：0-普通账号，1-主账号，2-子账号")
    private Integer accountType;

    @ApiModelProperty("对接客服")
    private Long waiterId;

    @NotNull(message = "[账号状态（0正常 1禁用 2被删除）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用 2被删除）")
    private Integer status;

    @ApiModelProperty("最后登录时间")
    private Date lastLoginTime;

    @ApiModelProperty("解绑时间")
    private Date unbindTime;

    @ApiModelProperty("种草官ID")
    private String seedId;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @NotNull(message = "[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    private Date updateTime;

    @NotNull(message = "[是否删除（0-未删除，1-已删除）]不能为空")
    @ApiModelProperty("是否删除（0-未删除，1-已删除）")
    private Integer isDel;

    @ApiModelProperty("对接客服名称")
    private String connectUserName;
}
