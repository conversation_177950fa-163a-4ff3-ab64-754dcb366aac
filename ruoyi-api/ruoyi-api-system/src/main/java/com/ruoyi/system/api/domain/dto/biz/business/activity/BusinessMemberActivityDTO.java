package com.ruoyi.system.api.domain.dto.biz.business.activity;

import com.ruoyi.common.core.enums.PackageTypeEnum;
import com.ruoyi.common.core.enums.PresentedTimeTypeEnum;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 商家会员活动
 *
 * <AUTHOR>
 * @TableName business_member_activity
 */
@Data
public class BusinessMemberActivityDTO implements Serializable {

    private static final long serialVersionUID = 9063909604573514664L;
    @NotNull(message = "修改活动[主键]不能为空", groups = CommonValidatedGroup.EditValidatedGroup.class)
    @Null(message = "保存活动[主键]必须为空", groups = CommonValidatedGroup.SaveValidatedGroup.class)
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[套餐类型]不能为空")
    @ApiModelProperty("套餐类型：0-季度套餐,1-年度套餐,2-三年套餐")
    @EnumValid(enumClass = PackageTypeEnum.class, message = "套餐类型不合法")
    private Integer memberPackageType;

    @ApiModelProperty("加赠时间")
    @NotNull(message = "[加赠时间]不能为空")
    @Min(value = 0, message = "[加赠时间]不能大于小于0")
    private Integer presentedTime;

    @ApiModelProperty("加赠时间类型（1-天,2-月,3-年）")
    @NotNull(message = "[加赠时间类型]不能为空")
    @EnumValid(enumClass = PresentedTimeTypeEnum.class, message = "套餐类型不合法")
    private Integer presentedTimeType;

    @NotNull(message = "[活动开始时间]不能为空")
    @ApiModelProperty("活动开始时间")
    @Max(value = 31, message = "[活动开始时间]不可高于31")
    @Min(value = 1, message = "[活动开始时间]不能大于小于1")
    private Integer startTime;

    @NotNull(message = "[活动结束时间]不能为空")
    @ApiModelProperty("活动结束时间")
    @Max(value = 31, message = "[活动结束时间]不可高于31")
    @Min(value = 1, message = "[活动结束时间]不能大于小于1")
    private Integer endTime;
}
