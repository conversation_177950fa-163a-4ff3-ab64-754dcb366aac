package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 视频订单列表条件入参
 *
 * <AUTHOR>
 * @date 2024/6/1 11:07
 */
@Data
public class OrderVideoListDTO implements Serializable {
    private static final long serialVersionUID = 2677376598400293654L;
    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private List<Integer> status;
    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;
}
