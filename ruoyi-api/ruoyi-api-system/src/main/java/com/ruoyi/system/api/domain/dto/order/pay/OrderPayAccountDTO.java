package com.ruoyi.system.api.domain.dto.order.pay;

import com.ruoyi.common.core.enums.PayTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-18 16:23
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderPayAccountDTO implements Serializable {
    private static final long serialVersionUID = 799906341246605306L;

    @ApiModelProperty("收款人账号配置Id")
    @NotNull(message = "[收款人账号配置Id]不能为空")
    private Long payeeAccountConfigInfoId;

    @ApiModelProperty("订单号")
    @NotBlank(message="[订单号]不能为空")
    private String orderNum;

    @ApiModelProperty("账号类型:1-微信,2-支付宝,7-全币种,6-对公账户")
    @NotNull(message="[账号类型]不能为空")
    @EnumValid(enumClass = PayTypeEnum.class, message = "[账号类型]输入错误")
    private Integer accountType;
}
