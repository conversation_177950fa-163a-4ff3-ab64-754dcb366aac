package com.ruoyi.system.api.domain.dto.biz.business.activity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :修改会员活动状态
 * @create :2025-01-17 10:16
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateMemberActivityDTO implements Serializable {
    private static final long serialVersionUID = -3503225910241614273L;
    @NotNull(message = "[主键]不能为空")
    private Long id;

    @NotNull(message = "[状态]不能为空")
    private Integer status;

}
