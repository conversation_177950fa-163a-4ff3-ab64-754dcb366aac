package com.ruoyi.system.api.domain.dto.biz.common;

import com.ruoyi.common.core.validated.CommonValidatedGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:25
 */
@Data
public class TextDTO implements Serializable {

    private static final long serialVersionUID = 1587621843157411079L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "[主键]不能为空", groups = CommonValidatedGroup.EditValidatedGroup.class)
    private Long id;

    /**
     * 文本名称
     */
    @ApiModelProperty(value = "文本名称", required = true)
    @NotBlank(message = "[文本名称]不能为空")
    @Size(max = 30, message = "[文本名称]长度不能超过30")
    private String name;

    /**
     * 文本内容
     */
    @ApiModelProperty(value = "文本内容")
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 300, message = "[备注]长度不能超过300")
    private String remark;
}
