package com.ruoyi.system.api.domain.entity.biz.channel;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商家渠道信息表
 *
 * <AUTHOR>
 * @TableName business_channel
 */
@Data
public class BusinessChannel implements Serializable {

    private static final long serialVersionUID = 311022142206555115L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[用户ID (FK:business.id)]不能为空")
    @ApiModelProperty("用户ID (FK:business.id)")
    private Long businessId;

    @NotNull(message = "[用户ID (FK:biz_user.id)]不能为空")
    @ApiModelProperty("用户ID (FK:biz_user.id)")
    private Long bizUserId;

    @ApiModelProperty("注册渠道类型(0=普通,1=分销，2=市场,7=裂变)")
    private Integer registerChannelType;

    @ApiModelProperty("注册渠道账户id")
    private Long registerChannelId;

    @ApiModelProperty("注册时间")
    private Date registerTime;

    @ApiModelProperty("企微渠道类型(0=普通,1=分销，2=市场,7=裂变)")
    private Integer wechatChannelType;

    @ApiModelProperty("添加企微渠道账户id")
    private Long wechatChannelId;

    @ApiModelProperty("添加企微时间")
    private Date addWechatTime;

}
