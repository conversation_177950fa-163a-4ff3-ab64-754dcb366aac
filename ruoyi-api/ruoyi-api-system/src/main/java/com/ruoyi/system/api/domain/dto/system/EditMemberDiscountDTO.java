package com.ruoyi.system.api.domain.dto.system;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.ChannelDiscountTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-02 17:11
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EditMemberDiscountDTO implements Serializable {
    private static final long serialVersionUID = 2897836668727339009L;

    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @NotNull(message = "[会员折扣类型]不能为空")
    @ApiModelProperty("会员折扣类型（1-固定金额，2-固定比例）")
    @EnumValid(enumClass = ChannelDiscountTypeEnum.class, message = "会员折扣类型不合法")
    private Integer memberDiscountType;

    @NotNull(message = "[会员折扣]不能为空")
    @ApiModelProperty("会员折扣")
    private BigDecimal memberDiscount;


    @AssertTrue(message = "[会员折扣]有误")
    private boolean isMemberDiscountParam() {
        if (ObjectUtil.isNull(memberDiscount)){
            return Boolean.TRUE;
        }
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType)){
            //固定金额
            return memberDiscount.compareTo(BigDecimal.ZERO) >= 0 &&  memberDiscount.compareTo(BigDecimal.valueOf(999)) <= 0;
        }else {
            //固定比例
            return memberDiscount.compareTo(BigDecimal.ZERO) >= 0 &&  memberDiscount.compareTo(BigDecimal.valueOf(99)) <= 0;
        }
    }
}
