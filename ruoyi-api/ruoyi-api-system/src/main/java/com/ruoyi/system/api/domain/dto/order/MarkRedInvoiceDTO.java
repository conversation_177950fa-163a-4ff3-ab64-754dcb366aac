package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.OrderInvoiceRedStatusEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/8 10:00
 */
@Data
public class MarkRedInvoiceDTO implements Serializable {
    private static final long serialVersionUID = 6960533933334978039L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 是否红冲（2：不红冲，3：已红冲）
     */
    @ApiModelProperty(value = "是否红冲（2：不红冲，3：已红冲）")
    @NotNull(message = "[是否红冲]不能为空")
    private Integer invoiceRedStatus;

    /**
     * 红冲票号
     */
    @ApiModelProperty(value = "红冲票号")
    @Size(max = 100, message = "[红冲票号]长度不能超过100")
    private String invoiceRedNumber;

    /**
     * 红冲金额
     */
    @ApiModelProperty(value = "红冲金额")
    @DecimalMax(value = "9999999.99", message = "[红冲金额]不能超过9999999.99")
    @DecimalMin(value = "0.01", message = "[红冲金额]不能低于0.01")
    private BigDecimal invoiceRedAmount;

    /**
     * 发票URI
     */
    @ApiModelProperty(value = "发票URI")
    private String objectKey;

    /**
     * 是否重开（1：重开，0：不重开）
     */
    @ApiModelProperty(value = "是否重开（1：重开，0：不重开）")
    @EnumValid(enumClass = StatusTypeEnum.class, message = "[是否重开]输入错误")
    private Integer isReopen;

    /**
     * 重开金额
     */
    @ApiModelProperty(value = "重开金额")
    @DecimalMax(value = "9999999.99", message = "[重开金额]不能超过9999999.99")
    @DecimalMin(value = "0.01", message = "[重开金额]不能低于0.01")
    private BigDecimal reopenAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 1000, message = "[备注]长度不能超过1000")
    private String remark;

    @AssertTrue(message = "[选择红冲]红冲票号、红冲金额、发票、是否重开 不能为空")
    private boolean isRedStamp() {
        if (OrderInvoiceRedStatusEnum.RED_STAMP.getCode().equals(invoiceRedStatus)) {
            return CharSequenceUtil.isNotBlank(invoiceRedNumber)
                    && ObjectUtil.isNotNull(invoiceRedAmount)
                    && CharSequenceUtil.isNotBlank(objectKey)
                    && ObjectUtil.isNotNull(isReopen);
        }
        return true;
    }

    @AssertTrue(message = "[重开金额]不能为空")
    private boolean isReopen() {
        if (StatusTypeEnum.YES.getCode().equals(isReopen)) {
            return ObjectUtil.isNotNull(reopenAmount);
        }
        return true;
    }

    @AssertTrue(message = "[选择不红冲]其他数据须为空")
    private boolean isNoRedPunch() {
        if (OrderInvoiceRedStatusEnum.NO_RED_PUNCH.getCode().equals(invoiceRedStatus)) {
            return CharSequenceUtil.isBlank(invoiceRedNumber)
                    && ObjectUtil.isNull(invoiceRedAmount)
                    && CharSequenceUtil.isBlank(objectKey)
                    && ObjectUtil.isNull(isReopen)
                    && ObjectUtil.isNull(reopenAmount)
                    ;
        }
        return true;
    }

    @AssertTrue(message = "[红冲状态]输入错误")
    private boolean isCheckInvoiceRedStatus() {
        return OrderInvoiceRedStatusEnum.NO_RED_PUNCH.getCode().equals(invoiceRedStatus)
                || OrderInvoiceRedStatusEnum.RED_STAMP.getCode().equals(invoiceRedStatus);
    }
}
