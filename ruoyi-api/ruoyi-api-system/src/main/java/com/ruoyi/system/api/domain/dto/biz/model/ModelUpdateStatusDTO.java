package com.ruoyi.system.api.domain.dto.biz.model;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.ModelStatusEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/14 9:46
 */
@Data
public class ModelUpdateStatusDTO implements Serializable {
    private static final long serialVersionUID = -7219283447317846489L;
    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id", required = true)
    @NotNull(message = "[模特id]不能为空")
    private Long id;

    /**
     * 模特状态
     */
    @ApiModelProperty(value = "模特状态", required = true)
    @NotNull(message = "[模特状态]不能为空")
    @EnumValid(enumClass = ModelStatusEnum.class, message = "[模特状态]输入错误")
    private Integer status;

    /**
     * 取消合作类型
     */
    @ApiModelProperty(value = "取消合作类型")
    private Integer cancelCooperationType;


    /**
     * 取消合作细分类型
     */
    @ApiModelProperty(value = "取消合作细分类型")
    private String cancelCooperationSubType;

    /**
     * 状态说明
     */
    @ApiModelProperty(value = "状态说明")
    @Size(max = 1000, message = "[状态说明]长度不能超过30个字符")
    private String statusExplain;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "是否同步")
    private Boolean isSync = false;

    /**
     * 是否置底
     */
    @Null(message = "请勿传递[bottom]参数")
    private Boolean bottom;

    @AssertTrue(message = "当模特状态为[行程中]时，[开始时间]和[结束时间]不能为空且[结束时间]必须大于等于[开始时间]")
    private boolean isEndTimeValid() {
        if (ModelStatusEnum.JOURNEY.getCode().equals(status)) {
            if (startTime == null && endTime == null) {
                return false;
            }
            return DateUtil.compare(startTime, endTime) <= 0;
        } else {
            return true;
        }
    }

    @AssertTrue(message = "非正常状态，[状态说明]不能为空")
    private boolean isStatusExplainValid() {
        if (!ModelStatusEnum.NORMAL.getCode().equals(status)) {
            return statusExplain != null;
        }
        return true;
    }

    @AssertTrue(message = "暂停合作和取消合作不能同步")
    private boolean isCheckSync() {
        if (ObjectUtil.isNotNull(isSync) && isSync) {
            return !ModelStatusEnum.PAUSE.getCode().equals(status) && !ModelStatusEnum.CANCEL.getCode().equals(status);
        }
        return true;
    }
}
