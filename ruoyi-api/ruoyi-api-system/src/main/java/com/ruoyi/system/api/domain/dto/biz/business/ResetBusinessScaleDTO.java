package com.ruoyi.system.api.domain.dto.biz.business;

import com.ruoyi.common.core.enums.BusinessScaleTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 10:14
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResetBusinessScaleDTO implements Serializable {
    private static final long serialVersionUID = 478344144066175543L;

    @ApiModelProperty("商家规模")
    @EnumValid(enumClass = BusinessScaleTypeEnum.class, message = "[商家规模]输入错误")
    private Integer scale;

}
