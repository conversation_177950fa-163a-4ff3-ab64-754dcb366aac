package com.ruoyi.system.api.domain.entity.biz.channel.member;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 会员种草记录
 *
 * <AUTHOR>
 * @TableName member_seed_record
 */
@Data
public class MemberSeedRecord implements Serializable {


    private static final long serialVersionUID = -4422945275750683034L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[渠道id（distribution_channel.id）]不能为空")
    @ApiModelProperty("渠道id（distribution_channel.id）")
    private Long channelId;

    @NotNull(message = "[渠道类型：2-分销渠道 7-裂变]不能为空")
    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @ApiModelProperty("登录用户ID FK distribution_channel.biz_user_id")
    private Long channelBizUserId;

    @NotBlank(message = "[渠道名称（distribution_channel.channel_name）]不能为空")
    @Size(max = 27, message = "编码长度不能超过27")
    @ApiModelProperty("渠道名称（distribution_channel.channel_name）")
    @Length(max = 27, message = "编码长度不能超过27")
    private String channelName;

    @NotBlank(message = "[渠道手机号（biz_user.phone）]不能为空")
    @Size(max = 15, message = "编码长度不能超过15")
    @ApiModelProperty("渠道手机号（biz_user.phone）")
    @Length(max = 15, message = "编码长度不能超过15")
    private String channelPhone;

    @NotBlank(message = "[种草官ID（distribution_channel.seed_id）]不能为空")
    @Size(max = 16, message = "编码长度不能超过16")
    @ApiModelProperty("种草官ID（distribution_channel.seed_id）")
    @Length(max = 16, message = "编码长度不能超过16")
    private String channelSeedId;

    @NotBlank(message = "[种草码]不能为空")
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("种草码")
    @Length(max = 10, message = "编码长度不能超过10")
    private String seedCode;

    @NotNull(message = "[商家id（business.id）]不能为空")
    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    @NotBlank(message = "[商家名称（business.name）]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("商家名称（business.name）")
    @Length(max = 50, message = "编码长度不能超过50")
    private String businessName;

    @NotNull(message = "[商家端登录用户id FK:biz_user.id]不能为空")
    @ApiModelProperty("商家端登录用户id FK:biz_user.id")
    private Long bizUserId;

    @NotBlank(message = "[商家端登录用户微信昵称]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("商家端登录用户微信昵称")
    @Length(max = 32, message = "编码长度不能超过32")
    private String bizUserNickName;

    @NotBlank(message = "[商家端登录用户手机号]不能为空")
    @Size(max = 15, message = "编码长度不能超过15")
    @ApiModelProperty("商家端登录用户手机号")
    @Length(max = 15, message = "编码长度不能超过15")
    private String bizUserPhone;

    @NotBlank(message = "[会员编码（business.member_code）]不能为空")
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("会员编码（business.member_code）")
    @Length(max = 10, message = "编码长度不能超过10")
    private String memberCode;

    @NotNull(message = "[套餐类型：0=季度会员,1=年度会员,2=三年会员]不能为空")
    @ApiModelProperty("套餐类型：0=季度会员,1=年度会员,2=三年会员")
    private Integer memberPackageType;

    @NotBlank(message = "[订单号]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("订单号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String orderNum;

    @NotNull(message = "[订单实付金额（单位：￥）]不能为空")
    @ApiModelProperty("订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @ApiModelProperty("币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    @ApiModelProperty("实际支付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @NotNull(message = "[支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）]不能为空")
    @ApiModelProperty("支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）")
    private Integer payType;

    @NotNull(message = "[结算类型（1-固定金额，2-固定比例）]不能为空")
    @ApiModelProperty("结算类型（1-固定金额，2-固定比例）")
    private Integer settleType;

    @NotNull(message = "[结算比例]不能为空")
    @ApiModelProperty("结算比例")
    private BigDecimal settleRage;

    /**
     * todo 错误数据 暂时没用 不做处理
     */
    @NotNull(message = "[种草码优惠金额(固定金额)]不能为空")
    @ApiModelProperty("种草码优惠金额(固定金额)")
    private BigDecimal seedCodeDiscount;

    @NotNull(message = "[最终结算金额]不能为空")
    @ApiModelProperty("最终结算金额（结算比例 * 会员价格/固定金额）")
    private BigDecimal settleAmount;

    @NotNull(message = "[状态（0-待入账，1-待提现，2-待审核，3-待打款，4-已打款，5-审核不通过，6-打款异常，99-入账失败）]不能为空")
    @ApiModelProperty("状态（0-待入账，1-待提现，2-待审核，3-待打款，4-已打款，5-审核不通过，6-打款异常，99-入账失败，999-暂不可提现）")
    private Integer status;

    @ApiModelProperty("购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date buyTime;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
