package com.ruoyi.system.api.domain.dto.order.casus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :查询案例分组视频列表
 * @create :2024-07-10 15:31
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UpdateGroupSortDTO implements Serializable {
    private static final long serialVersionUID = -8423966946644452223L;

    @NotNull(message="[分组ID]不能为空")
    @ApiModelProperty("分组ID")
    private Long groupId;

    @NotNull(message="[视频Id]不能为空")
    @ApiModelProperty("视频Id")
    private Long videoId;

    @NotNull(message = "[分组视频排序]不能为空")
    @ApiModelProperty("分组视频排序")
    @PositiveOrZero(message = "[分组视频排序]不能小于0")
    @Max(value = 99999, message = "[分组视频排序]不能大于99999")
    private Integer sort;
}
