package com.ruoyi.system.api.domain.dto.biz.business.balance;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.PrepayPayTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商家预付表
 *
 * <AUTHOR>
 * @TableName business_balance_prepay
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OnlineBusinessBalancePrepayDTO implements Serializable {

    private static final long serialVersionUID = -5435965120366267204L;

    @NotNull(message = "[充值金额]不能为空")
    @ApiModelProperty("充值金额")
    @Max(value = 999999, message = "[充值金额]必须小于等于999999")
    @Min(value = 1, message = "[充值金额]至少为1")
    private Long amount;
}
