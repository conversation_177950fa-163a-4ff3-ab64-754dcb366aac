package com.ruoyi.system.api.domain.entity.biz.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 企业微信标签信息表
 *
 * <AUTHOR>
 * @TableName we_chat_tag
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WeChatTag implements Serializable {

    private static final long serialVersionUID = -5139820631131076804L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotBlank(message = "[标签组ID]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("标签组ID")
    @Length(max = 32, message = "编码长度不能超过32")
    private String groupId;

    @NotBlank(message = "[标签组名称]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("标签组名称")
    @Length(max = 30, message = "编码长度不能超过30")
    private String groupName;

    @NotBlank(message = "[标签id]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("标签id")
    @Length(max = 32, message = "编码长度不能超过32")
    private String tagId;

    @NotBlank(message = "[标签名称]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("标签名称")
    @Length(max = 30, message = "编码长度不能超过30")
    private String tagName;

    @ApiModelProperty("创建时间")
    private Date createTime;
}
