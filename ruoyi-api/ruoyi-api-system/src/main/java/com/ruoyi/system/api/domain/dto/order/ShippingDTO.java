package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 发货入参
 *
 * <AUTHOR>
 * @date 2024/6/3 15:46
 */
@Data
public class ShippingDTO implements Serializable {
    private static final long serialVersionUID = 8555899163149109637L;

    /**
     * 视频订单主键id
     */
    @ApiModelProperty(value = "视频订单主键id", required = true)
    @NotNull(message = "[视频订单主键id]不能为空")
    private Long videoId;

    /**
     * 收件地址ID (FK:order_video_model_shipping_address.id)
     */
    @ApiModelProperty(value = "收件地址ID")
    @Null(message = "请勿传递[shippingAddressId]")
    private Long shippingAddressId;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号", required = true)
    @NotBlank(message = "[物流单号]不能为空")
    @Size(max = 100, message = "[物流单号]长度不能超过100个字符")
    private String number;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 150, message = "[备注]长度不能超过150个字符")
    private String remark;

    /**
     * 补发原因
     */
    @ApiModelProperty(value = "补发原因")
    @Size(max = 300, message = "[补发原因]长度不能超过300个字符")
    private String reissueCause;

    /**
     * 是否补发（0:补发,1:不是补发）
     */
    private Integer reissue;


    /**
     * 国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty("国家")
    private Integer nation;

    /**
     * 收件人
     */
    @ApiModelProperty("收件人")
    private String recipient;

    /**
     * 城市
     */
    @ApiModelProperty("城市")
    private String city;

    /**
     * 州
     */
    @ApiModelProperty("州")
    private String state;

    /**
     * 邮编
     */
    @ApiModelProperty("邮编")
    private String zipcode;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String detailAddress;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 关联任务ID
     */
    @ApiModelProperty(value = "关联任务ID")
    private List<Long> taskDetailIds;

    /**
     * 标记物流状态（1:标记发货）
     */
    @Null(message = "请勿传递[logisticFlag]")
    private Integer logisticFlag;

    /**
     * 标记发货原因
     */
    @Null(message = "请勿传递[logisticFlagRemark]")
    private String logisticFlagRemark;

    /**
     * 标记发货原因
     */
    @Null(message = "请勿传递[logisticFlagTime]")
    private Date logisticFlagTime;
}
