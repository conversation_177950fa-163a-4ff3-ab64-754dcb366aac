package com.ruoyi.system.api.domain.dto.biz.business.balance;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 余额提现审核表
 *
 * <AUTHOR>
 * @TableName business_balance_audit_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceAuditFlowSaveDTO implements Serializable {

    private static final long serialVersionUID = -2421547779501655219L;
    @NotNull(message = "[商家ID]不能为空")
    @ApiModelProperty("商家ID")
    private Long businessId;

    @NotNull(message = "[提现金额]不能为空")
    @ApiModelProperty("提现金额")
    private BigDecimal amount;

    @ApiModelProperty("提现申请备注")
    private String applyRemark;

    @ApiModelProperty("提现类型（1-微信，2-支付宝，3-银行卡，4-境外汇款，5-其他）")
    private Integer withdrawWay;

    @ApiModelProperty("提现申请图片")
    private String payoutResourceUrl;
}
