package com.ruoyi.system.api.domain.entity.biz.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
* 商家信息操作日志
* <AUTHOR>
 * @TableName business_oper_log
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessOperLog implements Serializable {

    private static final long serialVersionUID = -1421914588005365629L;
    /**
    * 主键
    */
    @NotNull(message="[主键]不能为空")
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
    * 订单id (FK:sys_user.user_id)
    */
    @NotNull(message="[订单id (FK:sys_user.user_id)]不能为空")
    @ApiModelProperty("订单id (FK:sys_user.user_id)")
    private Long userId;
    /**
    * 用户账号
    */
    @Size(max= 30,message="编码长度不能超过30")
    @ApiModelProperty("用户账号")
    @Length(max= 30,message="编码长度不能超过30")
    private String userName;
    /**
    * 用户昵称
    */
    @Size(max= 30,message="编码长度不能超过30")
    @ApiModelProperty("用户昵称")
    @Length(max= 30,message="编码长度不能超过30")
    private String nickName;
    /**
    * 原视频json
    */
    @NotNull(message="[原视频json]不能为空")
    @ApiModelProperty("原视频json")
    private String originalBusiness;
    /**
    * 修改后视频json
    */
    @NotNull(message="[修改后视频json]不能为空")
    @ApiModelProperty("修改后视频json")
    private String resultBusiness;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Date createTime;

}
