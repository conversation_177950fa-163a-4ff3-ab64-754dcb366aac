package com.ruoyi.system.api.domain.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 订单收支明细
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@ApiModel(value = "订单明细dto")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderPayDetailDTO implements Serializable {
    private static final long serialVersionUID = 734687070967988311L;

    @ApiModelProperty(value = "order_id")
    private Long orderId;

    @ApiModelProperty(value = "商家名称")
    private String businessName;

    @ApiModelProperty("商家账号")
    private String account;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "订单类型（0-视频订单，1-会员订单，3-线下钱包充值订单，5-线上钱包充值）")
    private Integer orderType;

    @ApiModelProperty(value = "订单类型（0-视频订单，1-会员订单，3-线下钱包充值订单，5-线上钱包充值）")
    private List<Integer> orderTypeList;

    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "列表")
    private List<String> orderNums;

    @ApiModelProperty(value = "商家ID列表")
    private Set<Long> businessIds;

    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）")
    @Excel(name = "支付方式")
    private Set<Integer> payTypes;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private List<Integer> payTypeDetails;

    @ApiModelProperty(value = "是否入账")
    private Integer isRecord;

    @ApiModelProperty(value = "入账开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recordTimeBegin;

    @ApiModelProperty(value = "入账结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recordTimeEnd;


    @ApiModelProperty(value = "支付时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTimeBegin;

    @ApiModelProperty(value = "支付时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTimeEnd;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timeBegin;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timeEnd;

    @ApiModelProperty(value = "关键词")
    private String searchName;

    @ApiModelProperty(value = "关键词商家列表")
    private Set<Long> searchBusinessIds;
}
