package com.ruoyi.system.api.domain.dto.biz.wechat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :标签
 * @create :2024-09-25 15:08
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WeCropTagDTO implements Serializable {
    private static final long serialVersionUID = -4982840820768818507L;

    private String id;
    private String name;
    private String order;
    private Boolean deleted;
    private Long create_time;
}
