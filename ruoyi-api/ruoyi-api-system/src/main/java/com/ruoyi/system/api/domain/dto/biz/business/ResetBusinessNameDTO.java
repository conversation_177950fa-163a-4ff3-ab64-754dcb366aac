package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 10:14
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResetBusinessNameDTO implements Serializable {
    private static final long serialVersionUID = 478344144066175543L;

    @NotBlank(message="[商家名称]不能为空")
    @Size(max= 50,message="[商家名称]长度不能超过50")
    @ApiModelProperty("商家名称")
    private String name;

}
