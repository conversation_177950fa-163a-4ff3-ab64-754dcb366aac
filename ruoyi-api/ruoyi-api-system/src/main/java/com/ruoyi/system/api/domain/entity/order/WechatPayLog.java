package com.ruoyi.system.api.domain.entity.order;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.utils.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 微信回调记录表
 *
 * <AUTHOR>
 * @TableName wechat_pay_log
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WechatPayLog implements Serializable {

    private static final long serialVersionUID = 3145252821195343386L;
    @NotNull(message = "[主键，自增ID]不能为空")
    @ApiModelProperty("主键，自增ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("内部订单号")
    private String orderNum;

    @NotBlank(message = "[公众账号ID]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("公众账号ID")
    @Length(max = 32, message = "编码长度不能超过32")
    private String appid;

    @NotBlank(message = "[商户号]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("商户号")
    @Length(max = 32, message = "编码长度不能超过32")
    private String mchid;

    @NotBlank(message = "[商户订单号]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("商户订单号")
    @Length(max = 32, message = "编码长度不能超过32")
    private String outTradeNo;

    @NotBlank(message = "[微信支付订单号]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("微信支付订单号")
    @Length(max = 32, message = "编码长度不能超过32")
    private String transactionId;

    @NotBlank(message = "[交易类型:JSAPI：公众号支付, NATIVE：扫码支付, APP：APP支付, MICROPAY：付款码支付, MWEB：H5支付, FACEPAY：刷脸支付]不能为空")
    @Size(max = 16, message = "编码长度不能超过16")
    @ApiModelProperty("交易类型:JSAPI：公众号支付, NATIVE：扫码支付, APP：APP支付, MICROPAY：付款码支付, MWEB：H5支付, FACEPAY：刷脸支付")
    @Length(max = 16, message = "编码长度不能超过16")
    private String tradeType;

    @NotBlank(message = "[交易状态:SUCCESS：支付成功, REFUND：转入退款, NOTPAY：未支付, CLOSED：已关闭, REVOKED：已撤销（仅付款码支付会返回）, USERPAYING：用户支付中（仅付款码支付会返回）, PAYERROR：支付失败（仅付款码支付会返回）]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("交易状态:SUCCESS：支付成功, REFUND：转入退款, NOTPAY：未支付, CLOSED：已关闭, REVOKED：已撤销（仅付款码支付会返回）, USERPAYING：用户支付中（仅付款码支付会返回）, PAYERROR：支付失败（仅付款码支付会返回）")
    @Length(max = 32, message = "编码长度不能超过32")
    private String tradeState;

    @NotBlank(message = "[交易状态描述]不能为空")
    @Size(max = 256, message = "编码长度不能超过256")
    @ApiModelProperty("交易状态描述")
    @Length(max = 256, message = "编码长度不能超过256")
    private String tradeStateDesc;

    @NotBlank(message = "[付款银行]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("付款银行")
    @Length(max = 32, message = "编码长度不能超过32")
    private String bankType;

    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("附加数据")
    @Length(max = 255, message = "编码长度不能超过255")
    private String attach;

    @ApiModelProperty("支付完成时间")
    private Date successTime;

    @NotBlank(message = "[用户标识]不能为空")
    @Size(max = 128, message = "编码长度不能超过128")
    @ApiModelProperty("用户标识")
    @Length(max = 128, message = "编码长度不能超过128")
    private String openid;

    @NotNull(message = "[总金额]不能为空")
    @ApiModelProperty("总金额")
    private Integer total;

    @NotNull(message = "[付款人总金额]不能为空")
    @ApiModelProperty("付款人总金额")
    private Integer payerTotal;

    @NotBlank(message = "[货币种类]不能为空")
    @Size(max = 16, message = "编码长度不能超过16")
    @ApiModelProperty("货币种类")
    @Length(max = 16, message = "编码长度不能超过16")
    private String currency;

    @NotBlank(message = "[付款人货币种类]不能为空")
    @Size(max = 16, message = "编码长度不能超过16")
    @ApiModelProperty("付款人货币种类")
    @Length(max = 16, message = "编码长度不能超过16")
    private String payerCurrency;

    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("设备ID")
    @Length(max = 32, message = "编码长度不能超过32")
    private String deviceId;

    @ApiModelProperty("优惠券ID")
    private String remark;
}
