package com.ruoyi.system.api.domain.dto.order.casus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
* 案例分组表
* <AUTHOR>
 * @TableName case_group
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CasusGroupDTO implements Serializable {

    private static final long serialVersionUID = 5833978024312941321L;

    @ApiModelProperty("分组ID")
    private Long id;

    @ApiModelProperty("分组名称")
    private String name;

    @ApiModelProperty("平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    @ApiModelProperty("分组ID列表")
    private List<Long> ids;


}
