package com.ruoyi.system.api.domain.dto.biz.wechat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-25 15:07
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WeChatGroupTagDTO implements Serializable {
    private static final long serialVersionUID = -2510500178108263036L;

    private String group_id;
    private String group_name;
    private Integer order;
    private List<WeCropTagDTO> tag;
}
