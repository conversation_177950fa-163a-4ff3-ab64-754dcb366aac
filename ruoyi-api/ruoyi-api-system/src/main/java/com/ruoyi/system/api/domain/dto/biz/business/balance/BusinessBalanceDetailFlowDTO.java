package com.ruoyi.system.api.domain.dto.biz.business.balance;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商家余额详情流水表
 *
 * <AUTHOR>
 * @TableName business_balance_detail_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceDetailFlowDTO implements Serializable {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("商家余额流水ID（business_balance_flow.id）")
    private Long balanceFlowId;

    @ApiModelProperty("余额单号（business_balance_detail.number）")
    private String balanceNumber;

    @ApiModelProperty("单号（订单号、提现单号、预付单号）")
    @NotNull(message = "[单号]不能为空")
    private String number;

    @ApiModelProperty("视频订单ID")
    private Long videoId;

    @ApiModelProperty("视频订单ID")
    private String videoCode;

    @ApiModelProperty("视频订单金额")
    private BigDecimal amount;

    @ApiModelProperty("视频订单金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "差额")
    private BigDecimal differenceAmount;

    @NotNull(message = "[使用余额]不能为空")
    @ApiModelProperty("使用余额")
    private BigDecimal useBalance;

    @NotNull(message = "[订单类型(0-收入、1-支出)]不能为空")
    @ApiModelProperty("订单类型(0-收入、1-支出)")
    private Integer type;
}
