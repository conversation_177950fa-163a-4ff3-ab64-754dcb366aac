package com.ruoyi.system.api.domain.dto.biz.business.account.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
* 登录用户表
* <AUTHOR>
 * @TableName biz_user
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BizUserEditStatusDTO implements Serializable {

    private static final long serialVersionUID = 5040642647752241015L;

    @NotNull(message = "[id]不能为空")
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("状态")
    private Integer status;
}
