package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.validated.CommonValidatedGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/26 18:12
 */
@Data
public class OrderVideoUploadLinkDTO implements Serializable {
    private static final long serialVersionUID = -3795511228134817755L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "[主键]不能为空", groups = CommonValidatedGroup.EditValidatedGroup.class)
    private Long id;

    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty(value = "视频id")
    @NotNull(message = "[视频id]不能为空")
    private Long videoId;
    /**
     * 提交信息对象（1:商家,2:运营）
     */
    private Integer object;
    /**
     * 提交信息用户id
     */
    private Long userId;
    /**
     * 需要上传的链接
     */
    @ApiModelProperty(value = "需要上传的链接")
    @Pattern(regexp = "^https://www\\.amazon\\.com.*$", message = "[上传的链接]非Amazon链接")
    @NotBlank(message = "[上传的链接]不能为空")
    @Size(max = 1000, message = "[上传的链接]长度不能超过1000个字符")
    private String needUploadLink;
    /**
     * 视频标题
     */
    @ApiModelProperty(value = "视频标题")
    @Size(min = 10, max = 60, message = "[视频标题]长度应在10~60个字符之间")
    @Pattern.List({
            // 1. 至少需要包含3个单词（根据中间空格来识别）
            @Pattern(regexp = "^(?=(.*\\b[A-Za-z]+\\b.*){3,}).*$", message = "[视频标题]至少需要包含3个单词"),

            // 2. 不允许包含输入*表情符号（匹配所有可能的 emoji）
            @Pattern(regexp = "^(?:(?![\\p{So}\\p{Cn}\\p{Cs}]).)*$", message = "[视频标题]不允许包含表情符号"),

            // 3. 不允许包含“$”
            @Pattern(regexp = "^[^$]*$", message = "[视频标题]不允许包含'$'"),

            // 4. 不允许包含外语字符（非ASCII字符）
            @Pattern(regexp = "^[\\x00-\\x7F]*$", message = "[视频标题]不允许包含外语字符（非ASCII字符）"),

            // 5. 不允许包含产品ASIN（连续全大写+数字混合10位）
            @Pattern(regexp = "^(?!.*\\b(?=[A-Z0-9]{10}\\b)(?!(?:[A-Z]{10}|\\d{10})\\b)[A-Z0-9]{10}\\b).*$", message = "[视频标题]不允许包含产品ASIN"),

            // 6. 不允许包含全大写或全数字
            @Pattern(regexp = "^(?!^[A-Z\\d\\s]+$)(?!^\\d+$).*$", message = "[视频标题]不允许包含全大写或全数字")
    })
    @NotBlank(message = "[视频标题]不能为空")
    private String videoTitle;
    /**
     * 视频封面图URI
     */
    @ApiModelProperty(value = "视频封面图URI")
    private String videoCover;
    /**
     * 客服备注
     */
    @ApiModelProperty(value = "客服备注")
    @Size(max = 800, message = "[客服备注]长度不能超过800个字符")
    private String remark;
}
