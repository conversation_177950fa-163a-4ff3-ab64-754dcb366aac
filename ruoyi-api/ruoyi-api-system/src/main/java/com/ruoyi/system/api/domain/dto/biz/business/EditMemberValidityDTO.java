package com.ruoyi.system.api.domain.dto.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.BusinessValidityChangeReasonTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-10-14 10:45
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EditMemberValidityDTO implements Serializable {
    private static final long serialVersionUID = 5585482902346129275L;

    @ApiModelProperty("商家Id")
    @NotNull(message = "[商家Id]不能为空")
    private Long id;

    @ApiModelProperty("会员有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "[会员有效期]不能为空")
    private Date memberValidity;

    @ApiModelProperty("修改原因类型（1:老会员入驻,2:七天无理由,3:退会,4:其他）")
    @EnumValid(enumClass = BusinessValidityChangeReasonTypeEnum.class, message = "[修改原因类型]不合法")
    @NotNull(message = "[修改原因类型]不能为空")
    private Integer changeReasonType;

    @ApiModelProperty("修改原因")
    @NotBlank(message = "[修改原因]不能为空")
    @Size(max = 64, message = "[修改原因]不能超过64")
    @Length(max = 64, message = "[修改原因]不能超过64")
    private String remark;
}
