package com.ruoyi.system.api.domain.entity.biz.statistics;

import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 商家排单数统计
 *
 * <AUTHOR>
 * @TableName business_order_data_statistics
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class BusinessOrderDataStatistics implements Serializable {

    private static final long serialVersionUID = -5877741296197819291L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[记录时间]不能为空")
    @ApiModelProperty("记录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recordTime;

    @NotNull(message = "[商家ID]不能为空")
    @ApiModelProperty("商家ID")
    private Long businessId;

    @NotNull(message = "[会员类型(1-新会员,2-老会员)]不能为空")
    @ApiModelProperty("会员类型(1-新会员,2-老会员)")
    private Integer businessMemberType;

    @NotNull(message = "[订单数量]不能为空")
    @ApiModelProperty("订单数量")
    private Integer videoCount;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
