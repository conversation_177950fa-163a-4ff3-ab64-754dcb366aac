package com.ruoyi.system.api.domain.dto.biz.channel;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ChannelActivityEditDTO {
    private Long id;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 折扣
     */
    @ApiModelProperty(value = "折扣")
    private BigDecimal discount;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态 0-已结束 1-活动暂停 2-未开始 3-进行中")
    private Integer status;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date endTime;

    /**
     * 类型(0-全部生效,1-部分生效,2-部分不生效)
     */
    @ApiModelProperty(value = "类型(0-全部生效,1-部分生效,2-部分不生效)")
    private Integer type;

    /**
     * 渠道id
     */
    @ApiModelProperty(value = "渠道id")
    private List<Long> channelIds;

    /**
     * 渠道信息
     */
    @ApiModelProperty(value = "渠道信息")
    private List<ChannelActivityInfoDTO> channelActivityInfoList;
}
