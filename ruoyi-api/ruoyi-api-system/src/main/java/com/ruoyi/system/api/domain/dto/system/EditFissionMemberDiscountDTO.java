package com.ruoyi.system.api.domain.dto.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-02 17:11
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EditFissionMemberDiscountDTO implements Serializable {

    private static final long serialVersionUID = 2897836668727339009L;
    @ApiModelProperty("折扣")
    @Max(value = 99, message = "[折扣]必须小于100")
    @Min(value = 1, message = "[折扣]必须大于0")
    @NotNull(message = "折扣不能为空")
    private Integer discount;

    @ApiModelProperty("结算比例")
    @Max(value = 99, message = "[结算比例]必须小于100")
    @Min(value = 1, message = "[结算比例]必须大于0")
    @NotNull(message = "结算比例")
    private Integer brokeRage;
}
