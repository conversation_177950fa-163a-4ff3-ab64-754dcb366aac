package com.ruoyi.system.api.domain.entity.biz.business.balance;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家余额详情锁定表
 *
 * <AUTHOR>
 * @TableName business_balance_detail_lock
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceDetailLock implements Serializable {


    private static final long serialVersionUID = 1655517471805880106L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotBlank(message = "[锁定单号（提现单号、视频订单、会员订单）]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("锁定单号（提现单号、视频订单、会员订单）")
    @Length(max = 32, message = "编码长度不能超过32")
    private String number;

    @ApiModelProperty("视频编码")
    private String videoCode;

    @ApiModelProperty("视频编码")
    private String prepayNum;

    @ApiModelProperty("来源单号")
    private String balanceNumber;

    @NotNull(message = "[商家余额详情ID（business_balance_detail.id）]不能为空")
    @ApiModelProperty("商家余额详情ID（business_balance_detail.id）")
    private Long balanceDetailId;

    @NotNull(message = "[已用金额（business_balance_detail.use_balance + lock_balance 快照）]不能为空")
    @ApiModelProperty("已用金额（business_balance_detail.use_balance + lock_balance 快照）")
    private BigDecimal useBalance;

    @NotNull(message = "[提现金额]不能为空")
    @ApiModelProperty("提现金额")
    private BigDecimal payOutAmount;

    @NotNull(message = "[状态（0:待处理,1:已提现,2.已取消）]不能为空")
    @ApiModelProperty("状态（0:待处理,1:已提现,2.已取消）")
    private Integer status;

    @ApiModelProperty("订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、7.线下钱包充值收入、8.线上钱包充值收入)")
    private Integer origin;

    @ApiModelProperty("下单运营")
    private String createOrderUserName;

    @ApiModelProperty("下单运营微信名")
    private String createOrderUserNickName;

    @ApiModelProperty("余额增加时间")
    private Date balanceCreateTime;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @NotNull(message = "[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    private Date updateTime;
}
