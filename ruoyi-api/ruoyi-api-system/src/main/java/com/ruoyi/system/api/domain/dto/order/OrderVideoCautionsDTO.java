package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/2 14:28
 */
@Data
public class OrderVideoCautionsDTO {

    /**
     * 注意事项 图片
     */
    @ApiModelProperty("注意事项 图片")
    private List<String> cautionsPics = new ArrayList<>();

    /**
     * 模特要求（原匹配模特注意事项）
     */
    @ApiModelProperty("模特要求（原匹配模特注意事项）")
    @Valid
    @Size(max = 1, message = "[模特要求（原匹配模特注意事项）]只能有一条")
    private List<VideoContentDTO> cautions;
}
