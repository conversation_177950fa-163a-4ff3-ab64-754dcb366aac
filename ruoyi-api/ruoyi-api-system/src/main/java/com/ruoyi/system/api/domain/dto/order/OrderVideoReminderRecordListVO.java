package com.ruoyi.system.api.domain.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/14 17:40
 */
@Data
public class OrderVideoReminderRecordListVO implements Serializable {

    private static final long serialVersionUID = 8909102169992146917L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    private Long videoId;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 创建订单主账号id
     */
    @ApiModelProperty(value = "创建订单主账号id")
    @JsonIgnore
    private Long createOrderBusinessId;

    /**
     * 创建订单主账号
     */
    @ApiModelProperty(value = "创建订单主账号")
    private BusinessAccountDetailVO createOrderBusiness;

    /**
     * 出单人id
     */
    @ApiModelProperty(value = "出单人id")
    @JsonIgnore
    private Long issueId;

    /**
     * 出单人
     */
    @ApiModelProperty(value = "出单人")
    private UserVO issue;

    /**
     * 催单次数（累计）
     */
    @ApiModelProperty(value = "催单次数（累计）")
    private Integer reminder;

    /**
     * 催单时间
     */
    @ApiModelProperty(value = "催单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reminderTime;

    /**
     * 催单状态（1:未处理,2:已确认,3:已完成）
     */
    @ApiModelProperty(value = "催单状态（1:未处理,2:已确认,3:已完成）")
    private Integer status;
}
