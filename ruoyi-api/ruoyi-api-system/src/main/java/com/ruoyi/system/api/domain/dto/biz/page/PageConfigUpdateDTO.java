package com.ruoyi.system.api.domain.dto.biz.page;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
* 页面配置表
* <AUTHOR>
 * @TableName page_config
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageConfigUpdateDTO implements Serializable {

    private static final long serialVersionUID = -7053268823004100831L;

    @NotNull(message="[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotBlank(message="[页面名称]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("页面名称")
    @Length(max= 20,message="编码长度不能超过20")
    private String name;

    @ApiModelProperty("链接地址")
    @Size(max = 100, message="编码长度不能超过100")
    private String link;
}
