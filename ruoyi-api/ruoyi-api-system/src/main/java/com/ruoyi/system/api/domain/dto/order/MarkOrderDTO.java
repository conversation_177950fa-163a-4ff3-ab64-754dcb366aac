package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.CarryTypeEnum;
import com.ruoyi.common.core.enums.CommissionUnitEnum;
import com.ruoyi.common.core.enums.ScheduleTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/15 14:14
 */
@Data
public class MarkOrderDTO implements Serializable {
    private static final long serialVersionUID = -6038393726467897418L;

    /**
     * 匹配单ID
     */
    @ApiModelProperty(value = "匹配单ID", required = true)
    @NotNull(message = "[匹配单ID]不能为空")
    private Long id;

    /**
     * 预选模特ID
     */
    @ApiModelProperty(value = "预选模特ID", required = true)
    @NotNull(message = "[预选模特ID]不能为空")
    private Long matchPreselectModelId;

    /**
     * 排单类型（1:排单,2:携带排单）
     */
    @ApiModelProperty(value = "排单类型（1:排单,2:携带排单）", required = true)
    @NotNull(message = "[排单类型]不能为空")
    @EnumValid(enumClass = ScheduleTypeEnum.class, message = "[排单类型]输入错误")
    private Integer scheduleType;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    @EnumValid(enumClass = CommissionUnitEnum.class, message = "[模特佣金单位]输入错误", enumField = "unit")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    @DecimalMin(value = "0.00", message = "[模特佣金]不能小于0")
    @DecimalMax(value = "99999.00", message = "[模特佣金]不能大于99999.00")
    private BigDecimal commission;

    /**
     * 超额说明
     */
    @ApiModelProperty(value = "超额说明")
    @Size(max = 800, message = "[超额说明]长度不能超过800个字符")
    private String overstatement;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    @EnumValid(enumClass = CarryTypeEnum.class, message = "[携带类型]输入错误")
    private Integer carryType;

    /**
     * 主携带数量
     */
    @ApiModelProperty(value = "主携带数量")
    @PositiveOrZero(message = "[主携带数量]必须为正整数")
    @Max(value = 9999, message = "[主携带数量]不能大于9999")
    private Integer mainCarryCount;

    /**
     * 主携带订单id
     */
    @ApiModelProperty(value = "主携带订单id")
    private Long mainCarryVideoId;

    /**
     * 发货备注
     */
    @ApiModelProperty(value = "发货备注")
    @Size(max = 800, message = "[发货备注]长度不能超过800个字符")
    @NotBlank(message = "[发货备注]不能为空")
    private String shippingRemark;

    /**
     * 发货图片
     */
    @ApiModelProperty(value = "发货图片")
    @Size(max = 3, message = "[发货图片]最多上传3张")
    private List<String> shippingPic;

    @ApiModelProperty("是否清空之前的模特：1-淘汰")
    private Integer isClear;

    /**
     * 拍摄模特注意事项
     */
    @ApiModelProperty("拍摄模特注意事项")
    @Size(max = 3500, message = "[拍摄模特注意事项]长度不能超过3500个字符")
    @Pattern(regexp = "^[^\\p{IsHan}]*$", message = "[拍摄模特注意事项]不能包含中文字符")
    private String shootAttention;

    /**
     * 拍摄注意事项对象存储键值
     */
    @ApiModelProperty("拍摄注意事项对象存储键值")
    private List<String> shootAttentionObjectKey;

    @AssertTrue(message = "[排单类型]对应的必填项不能为空")
    private boolean isScheduleType() {
        if (ScheduleTypeEnum.ORDER.getCode().equals(scheduleType)) {
            return CharSequenceUtil.isNotBlank(commissionUnit) && ObjectUtil.isNotNull(commission) && ObjectUtil.isNull(carryType);
        } else {
            return ObjectUtil.isNotNull(carryType) && CharSequenceUtil.isBlank(commissionUnit) && ObjectUtil.isNull(commission);
        }
    }

    @AssertTrue(message = "[携带类型]对应的必填项不能为空")
    private boolean isCarryType() {
        if (ObjectUtil.isNotNull(carryType)) {
            if (CarryTypeEnum.MAIN_CARRY.getCode().equals(carryType)) {
                return ObjectUtil.isNotNull(mainCarryCount) && ObjectUtil.isNull(mainCarryVideoId);
            } else {
                return ObjectUtil.isNotNull(mainCarryVideoId) && ObjectUtil.isNull(mainCarryCount);
            }
        } else {
            return true;
        }
    }
}
