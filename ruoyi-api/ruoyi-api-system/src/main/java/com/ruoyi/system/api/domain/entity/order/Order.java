package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单对象 order_table
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@ApiModel(value = "订单对象 order_table")
@TableName("order_table")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Order implements Serializable {

    private static final long serialVersionUID = 7122814627827212829L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 订单号
     */
    @NotNull(message = "[订单号]不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    @Excel(name = "订单号")
    private String orderNum;

    /**
     * 支付单号
     */
    @ApiModelProperty(value = "支付单号")
    private String payNum;

    @ApiModelProperty(value = "登录账号id")
    private Long bizUserId;

    /**
     * 订单类型（0-视频订单，1-会员订单）
     */
    @NotNull(message = "[订单类型]不能为空")
    @ApiModelProperty(value = "订单类型（0-视频订单，1-会员订单）", required = true)
    @Excel(name = "订单类型")
    private Integer orderType;

    /**
     * 视频数量
     */
    @NotNull(message = "[视频数量]不能为空")
    @ApiModelProperty(value = "视频数量", required = true)
    @Excel(name = "视频数量")
    private Integer videoCount;

    /**
     * 订单金额
     */
    @NotNull(message = "[订单金额]不能为空")
    @ApiModelProperty(value = "订单金额", required = true)
    @Excel(name = "订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "订单金额（单位：$）")
    private BigDecimal orderAmountDollar;

    /**
     * 需支付金额（单位：￥）
     */
    @ApiModelProperty(value = "需支付金额（单位：￥）")
    @Excel(name = "需支付金额（单位：￥）")
    private BigDecimal payAmount;

    /**
     * 需支付金额（单位：$）
     */
    @ApiModelProperty(value = "需支付金额（单位：$）")
    @Excel(name = "需支付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @ApiModelProperty(value = "订单实付金额（单位：￥）")
    @Excel(name = "订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "订单实付金额（对应币种实付）")
    @Excel(name = "订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty(value = "订单活动优惠总额")
    private BigDecimal orderPromotionAmount;

    @ApiModelProperty(value = "差额")
    private BigDecimal differenceAmount;


    @ApiModelProperty(value = "币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    /**
     * 使用余额
     */
    @ApiModelProperty(value = "使用余额")
    @Excel(name = "使用余额")
    private BigDecimal useBalance;


    /**
     * 税点（单位：%）
     */
    @ApiModelProperty(value = "税点（单位：%）")
    private BigDecimal taxPoint;

    /**
     * 税点费用（单位：￥）
     */
    @ApiModelProperty(value = "税点费用（单位：￥）")
    private BigDecimal taxPointCost;


    /**
     * 种草码
     */
    @ApiModelProperty(value = "种草码")
    private String seedCode;

    @ApiModelProperty(value = "种草官ID")
    private String seedId;

    @ApiModelProperty(value = "种草官会员状态（0-非会员，1-会员）")
    private Integer seedMemberStatus;

    @ApiModelProperty(value = "会员结算类型（1-固定金额，2-固定比例）")
    private Integer memberDiscountType;

    @ApiModelProperty(value = "优惠折扣")
    private BigDecimal settleRage;

    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /**
     * 种草码优惠金额（单位：￥）
     */
    @ApiModelProperty(value = "种草码优惠金额（单位：￥）")
    private BigDecimal seedCodeDiscount;

    /**
     * 运营修改的订单金额（单位：￥） 正数则为增加金额，负数则为减少金额
     */
    @ApiModelProperty(value = "运营修改的订单金额（单位：￥） 正数则为增加金额，负数则为减少金额")
    private BigDecimal backModifyAmount;

    /**
     * 当前汇率
     */
    @ApiModelProperty(value = "当前汇率")
    @Excel(name = "当前汇率")
    private BigDecimal currentExchangeRate;

    /**
     * 是否使用的默认汇率（0:不是,1:默认）
     */
    @ApiModelProperty(value = "是否使用的默认汇率（0:不是,1:默认）")
    private Boolean isDefaultExchangeRate;

    /**
     * 下单用户id
     */
    @NotNull(message = "[下单用户id]不能为空")
    @ApiModelProperty(value = "下单用户id", required = true)
    @Excel(name = "下单用户id")
    private Long orderUserId;

    /**
     * 下单用户账号
     */
    @ApiModelProperty(value = "下单用户账号")
    private String orderUserAccount;

    /**
     * 下单用户名称
     */
    @ApiModelProperty(value = "下单用户名称")
    private String orderUserName;

    /**
     * 下单用户微信名称
     */
    @ApiModelProperty(value = "下单用户微信名称")
    private String orderUserNickName;

    /**
     * 下单时间
     */
    @NotNull(message = "[下单时间]不能为空")
    @ApiModelProperty(value = "下单时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    @ApiModelProperty(value = "订单时间标记（每次开启订单、创建订单时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeSign;

    @ApiModelProperty(value = "开启订单次数")
    private Integer reopenCount;

    /**
     * 支付用户id
     */
    @ApiModelProperty(value = "支付用户id")
    @Excel(name = "支付用户id")
    private Long payUserId;

    /**
     * 支付用户账号
     */
    @ApiModelProperty(value = "支付用户账号")
    private String payUserAccount;

    /**
     * 支付用户名称
     */
    @ApiModelProperty(value = "支付用户名称")
    private String payUserName;

    /**
     * 支付用户微信名称
     */
    @ApiModelProperty(value = "支付用户微信名称")
    private String payUserNickName;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）
     */
    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）")
    @Excel(name = "支付方式")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    /**
     * 收款信息id
     */
    @ApiModelProperty(value = "收款信息id")
    private Long payeeId;

    @ApiModelProperty(value = "提交凭证时间")
    private Date submitCredentialTime;

    /**
     * 支付账户（对公）
     */
    @ApiModelProperty(value = "支付账户（对公）")
    @Excel(name = "支付账户（对公）")
    private String payAccount;

    /**
     * 财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭
     */
    @ApiModelProperty(value = "财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭")
    @Excel(name = "财务审核状态")
    private Integer auditStatus;

    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("审核人员名称")
    @Length(max = 30, message = "编码长度不能超过30")
    private String auditUserName;

    @ApiModelProperty("关闭订单时间")
    private Date closeOrderTime;

    @ApiModelProperty("是否自动取消(0-否，1-是)")
    private Integer isAutoCancel;

    @ApiModelProperty("是否禁止发票(0-否，1-是)")
    private Integer banInvoice;

    @ApiModelProperty(value = "是否入账")
    private Integer isRecord;

    @ApiModelProperty(value = "是否合并订单")
    private Integer isMergeOrder;

    @ApiModelProperty(value = "是否入账")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recordTime;

    @ApiModelProperty(value = "订单备注")
    private String orderRemark;

    /**
     * 商家id
     */
    @NotNull(message = "[商家id]不能为空")
    @ApiModelProperty(value = "商家id", required = true)
    @Excel(name = "商家id")
    private Long merchantId;

    /**
     * 商家编码
     */
    @NotNull(message = "[商家编码]不能为空")
    @ApiModelProperty(value = "商家编码", required = true)
    @Excel(name = "商家编码")
    private String merchantCode;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    @ApiModelProperty(value = "渠道类型")
    private Integer channelType;

    @ApiModelProperty(value = "支付宝支付商户号")
    private String alipayPayAppId;

    @ApiModelProperty(value = "微信支付商户号")
    private String wechatPayAppId;
}
