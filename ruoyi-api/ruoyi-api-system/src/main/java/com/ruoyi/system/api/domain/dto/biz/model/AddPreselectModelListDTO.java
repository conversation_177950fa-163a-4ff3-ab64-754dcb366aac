package com.ruoyi.system.api.domain.dto.biz.model;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/5/21 17:41
 */
@Data
@ApiModel("添加预选模特列表入参")
public class AddPreselectModelListDTO implements Serializable {

    private static final long serialVersionUID = 5166027667384398059L;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 匹配单ID
     */
    @ApiModelProperty(value = "匹配单ID", required = true)
    @NotNull(message = "[匹配单ID]不能为空")
    private Long matchId;

    /**
     * 视频订单ID
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家", required = true)
    @NotNull(message = "[国家]不能为空")
    private Integer nation;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型", required = true)
    @NotNull(message = "[模特类型]不能为空")
    private List<Integer> type;

    /**
     * 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "平台", required = true)
    @NotNull(message = "[平台]不能为空")
    private Integer platform;

    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String account;
    /**
     * 模特名称
     */
    @ApiModelProperty(value = "模特名称")
    private String name;

    /**
     * 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
     */
    @ApiModelProperty(value = "年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）", notes = "1:婴幼儿,2:儿童,3:成年人,4:老年人")
    private List<Integer> ageGroup;
    /**
     * 擅长品类
     */
    @ApiModelProperty(value = "擅长品类")
    private List<String> specialtyCategory;
    /**
     * 模特标签
     */
    @ApiModelProperty(value = "模特标签")
    private List<String> modelTag;
    /**
     * 待拍数排序 ASC=升序 DESC=降序
     */
    @ApiModelProperty(value = "待拍数排序 ASC=升序 DESC=降序")
    @EnumValid(enumClass = OrderByDto.DIRECTION.class, message = "请传递正确的[waitsSort]参数", enumField = "value")
    private String waitsSort;

    /**
     * 性别(1:男,0:女)
     */
    @ApiModelProperty(value = "性别")
    private List<Integer> sex;

    /**
     * 是否携带单 true:是 false:否
     */
    @ApiModelProperty(value = "是否携带单 true:是 false:否")
    private Boolean carry;

    /**
     * 运营关联的模特
     */
    @Null(message = "请勿传递[modelIds]")
    private List<Long> modelIds;

    /**
     * 含有逾期未反馈素材和无法接单的模特
     */
    @Null(message = "请勿传递[cannotModel]")
    private List<Long> cannotModel;

    /**
     * 模特id ： 模特待拍数
     */
    @Null(message = "请勿传递[waitsMap]")
    private Map<Long, Long> waitsMap;

    /**
     * 还需携带订单的模特
     */
    @Null(message = "请勿传递[mainCarryModel]")
    private List<Long> mainCarryModel;

    /**
     * 还需携带订单的模特
     */
    @Null(message = "请勿传递[normalPreselectModelIds]")
    private Set<Long> normalPreselectModelIds;

    @ApiModelProperty(value = "需要过滤黑名单的登录账号ID")
    private Long filterBlackListBizUserId;

    /**
     * 是否是添加分发页面
     */
    @ApiModelProperty(value = "是否是添加分发页面")
    private Boolean isDistribution;

    /**
     * 是通品（1：是，0：不是）
     */
    @ApiModelProperty(value = "是通品（1：是，0：不是）")
    private Integer isGund;

    /**
     * 匹配开始时间
     */
    @ApiModelProperty(value = "匹配开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date matchStartTime;

    @ApiModelProperty(value = "请勿传递cooperation")
    private Integer cooperation;

}
