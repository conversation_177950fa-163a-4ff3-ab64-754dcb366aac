package com.ruoyi.system.api.domain.dto.biz.wechat;

import com.ruoyi.common.core.enums.WxChatLoginStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户登录信息
 *
 * <AUTHOR>
 * @date 2024/6/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatLoginCheckDTO implements Serializable {

    private static final long serialVersionUID = -3177682365666346932L;
    @ApiModelProperty("登录状态")
    WxChatLoginStatusEnum loginStatus;

    @ApiModelProperty("token")
    String token;
}
