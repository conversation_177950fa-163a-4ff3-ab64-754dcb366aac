package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/12/11 16:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AssignHandlerDTO extends TaskDetailOperateDTO {

    /**
     * 处理人ID
     */
    @ApiModelProperty(value = "处理人ID", required = true)
    @NotNull(message = "[处理人ID]不能为空")
    private Long assigneeId;
}
