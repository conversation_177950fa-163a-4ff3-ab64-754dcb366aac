package com.ruoyi.system.api.domain.dto.biz.model;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import com.ruoyi.system.api.domain.entity.biz.model.ModelVideoResource;
import com.ruoyi.system.api.domain.vo.biz.model.ModelTagVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/20 18:39
 */
@Data
@ApiModel("新增修改模特入参")
public class ModelDTO implements Serializable {
    private static final long serialVersionUID = 8983785328008857043L;
    /**
     * 模特id
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "[模特id]不能为空", groups = {CommonValidatedGroup.EditValidatedGroup.class})
    private Long id;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人", required = true)
    @NotNull(message = "[模特类型]不能为空")
    @EnumValid(enumClass = ModelTypeEnum.class, message = "[模特类型]输入错误")
    private Integer type;

    /**
     * 姓名
     */
    @NotBlank(message = "[姓名]不能为空")
    @Size(max = 16, message = "[姓名]长度不能超过16位字符")
    @ApiModelProperty(value = "模特姓名", required = true)
    private String name;

    /**
     * 性别(0:男,1:女)
     */
    @ApiModelProperty(value = "性别（1:男,0:女）", notes = "0:男,1:女", required = true)
    @NotNull(message = "[性别]不能为空")
    @EnumValid(enumClass = SexEnum.class, message = "[性别]输入错误")
    private Integer sex;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Past(message = "[出生日期]必须小于当前日期")
    private Date birthday;

    /**
     * 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
     */
    @ApiModelProperty(value = "模特年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）", notes = "1:婴幼儿,2:儿童,3:成年人,4:老年人", required = true)
    @NotNull(message = "[年龄层]不能为空")
    @EnumValid(enumClass = ModelAgeGroupEnum.class, message = "[年龄层]输入错误")
    private Integer ageGroup;

    /**
     * 国家 "（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @NotNull(message = "[国家]不能为空")
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", required = true, notes = "（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    @EnumValid(enumClass = NationEnum.class, message = "[国家]输入错误")
    private Integer nation;

    /**
     * 收件人
     */
    @ApiModelProperty(value = "收件人")
    @Size(max = 32, message = "[收件人]长度不能超过32位字符")
    @NotBlank(message = "[收件人]不能为空")
    private String recipient;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    @Size(max = 100, message = "[城市]长度不能超过100位字符")
    @NotBlank(message = "[城市]不能为空")
    private String city;

    /** 州 */
    @ApiModelProperty(value = "州 （加拿大/法国（省）、英国（局部区域））")
    @Size(max = 100, message = "[州]长度不能超过100位字符")
    private String state;

    /** 邮编 */
    @ApiModelProperty(value = "邮编")
    @Size(max = 16, message = "[邮编]长度不能超过16位字符")
    @NotBlank(message = "[邮编]不能为空")
    private String zipcode;

    /** 详细地址 */
    @ApiModelProperty(value = "门牌号和街道名称")
    @Size(max = 100, message = "[详细地址]长度不能超过100位字符")
    @NotBlank(message = "[详细地址]不能为空")
    private String detailAddress;

    /** 手机号 */
    @ApiModelProperty(value = "电话")
    @Size(max = 30, message = "[手机号]长度不能超过30位字符")
    private String phone;

    /**
     * 模特头图
     */
    @ApiModelProperty(value = "头像", required = true)
    @NotBlank(message = "[模特头图]不能为空")
    @Size(max = 64, message = "[模特头图]长度不能超过64个字符")
    private String modelPic;

    /**
     * 生活场景照
     */
    @ApiModelProperty(value = "生活照")
    @Size(max = 30, message = "[生活场景照]最多可以传30张")
    private List<String> lifePhoto;

    /** 擅长品类 */
    @ApiModelProperty(value = "擅长品类")
    private List<Long> specialtyCategory = new ArrayList<>();

    /** 修改前擅长品类 */
    @ApiModelProperty(value = "修改前擅长品类")
    private List<ModelTagVO> oldSpecialtyCategory = new ArrayList<>();

    /** 模特标签 */
    @ApiModelProperty(value = "模特标签")
    @Valid
    private List<ModelTagDTO> tags = new ArrayList<>();

    /** 修改前模特标签 */
    @ApiModelProperty(value = "修改前模特标签")
    private List<ModelTagVO> oldTags = new ArrayList<>();

    /**
     * 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "适用平台（0:Amazon,1:tiktok,2:其他,3:APP/解说类）", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类", required = true)
    @NotBlank(message = "[平台]不能为空")
    @EnumValid(enumClass = PlatformEnum.class, message = "[平台]输入错误")
    private String platform;

    /** 合作深度(0:一般模特,1:优质模特,2:中度模特) */
    @ApiModelProperty(value = "合作深度(0:一般模特,1:优质模特,2:中度模特)",notes = "0:一般模特,1:优质模特,2:中度模特")
//    @NotNull(message = "[合作深度]不能为空")
//    @EnumValid(enumClass = ModelCooperationEnum.class, message = "[合作深度]输入错误")
    private Integer cooperation;


    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)", required = true)
    @NotNull(message = "模特评分不能为空")
    private BigDecimal cooperationScore;


    /**
     * 关联对接人员
     * @ApiModelProperty的value不要修改 否则会影响 模特变更对接客服更新视频订单的出单人ID
     */
    @ApiModelProperty(value = "英文客服", required = true)
    @NotEmpty(message = "[关联对接人员]不能为空")
    @Size(max = 1, message = "[关联对接人员]最多可以关联1个")
    private List<Long> persons;

    /** 待完成最高接受量 */
    @ApiModelProperty(value = "待完成最高接受量", required = true)
    @NotNull(message = "[待完成最高接受量]不能为空")
    @PositiveOrZero(message = "[待完成最高接受量]必须大于等于0")
    @Max(value = 99999, message = "待完成最高接受量不可高于99999")
    private Integer acceptability;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）", required = true)
    @NotBlank(message = "[模特佣金单位]不能为空")
    @EnumValid(enumClass = CommissionUnitEnum.class, message = "[模特佣金单位]输入错误", enumField = "unit")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金", required = true)
    @NotNull(message = "[模特佣金]不能为空")
    @DecimalMin(value = "0.00", message = "[模特佣金]必须大于0.00")
    @DecimalMax(value = "9999.99", message = "[模特佣金]必须小于9999.99")
    @Digits(integer = 4, fraction = 2, message = "[模特佣金]应是四位整数及最多两位小数")
    private BigDecimal commission;

    /**
     * 亚马逊平台案例视频
     */
    @ApiModelProperty(value = "亚马逊平台案例视频")
    @Valid
    @Size(max = 50, message = "[亚马逊案例视频]最多可以上传50个")
    private List<ModelResourceDTO> amazonVideo = new ArrayList<>();

    /**
     * 修改前亚马逊平台案例视频
     */
    @ApiModelProperty(value = "修改前亚马逊平台案例视频")
    @Valid
    @Size(max = 50, message = "[修改前亚马逊平台案例视频]最多可以上传50个")
    private List<ModelVideoResource> oldAmazonVideo = new ArrayList<>();

    /**
     * tiktok平台案例视频
     */
    @ApiModelProperty(value = "tiktok平台案例视频")
    @Valid
    @Size(max = 50, message = "[TikTok案例视频]最多可以上传50个")
    private List<ModelResourceDTO> tiktokVideo = new ArrayList<>();

    /**
     * 修改前tiktok平台案例视频
     */
    @ApiModelProperty(value = "修改前tiktok平台案例视频")
    @Valid
    @Size(max = 50, message = "[修改前tiktok平台案例视频]最多可以上传50个")
    private List<ModelVideoResource> oldTiktokVideo = new ArrayList<>();

    /**
     * 排序值
     */
    @ApiModelProperty(value = "列表排序")
    @Max(value = 99999, message = "[排序值]必须小于等于99999")
    @PositiveOrZero(message = "[排序值]必须大于等于0")
    private Integer sort;

    /**
     * 是否展示（1:展示,0:不展示）
     */
    @ApiModelProperty(value = "展示开关（1:展示,0:不展示）")
    @EnumValid(enumClass = StatusTypeEnum.class, message = "[是否展示]输入错误")
    private Integer isShow;

    /**
     * 模特简介
     */
    @ApiModelProperty(value = "模特简介")
    @Size(max = 150, message = "[模特简介]长度不能超过150位字符")
    private String about;

    /**
     * 有蜗牛照（1：有，0：没有）
     */
    @ApiModelProperty(value = "有蜗牛照（1：有，0：没有）")
    @NotNull(message = "[蜗牛照]不能为空")
    @EnumValid(enumClass = StatusTypeEnum.class, message = "[蜗牛照]输入错误")
    private Integer haveSnailPic;

    /**
     * 开发人ID
     */
    @ApiModelProperty(value = "开发人ID")
    @NotNull(message = "[开发人ID]不能为空")
    private Long developerId;

    @AssertTrue(message = "[州]不能为空")
    private boolean isStateValid() {
        if (NationEnum.USA.getCode().equals(nation) || NationEnum.CANADA.getCode().equals(nation) || NationEnum.FRANCE.getCode().equals(nation)) {
            return StrUtil.isNotBlank(state);
        }
        return true;
    }

    @AssertTrue(message = "只有美国才有影响者")
    private boolean isAmericanInfluence() {
        if (ModelTypeEnum.INFLUENT.getCode().equals(type)) {
            return NationEnum.USA.getCode().equals(nation);
        }
        return true;
    }

    public void echo(List<ModelTagVO> modelTagList) {
        if (CollUtil.isNotEmpty(modelTagList)) {
            setSpecialtyCategory(modelTagList.stream().filter(item -> ModelTagEnum.CATEGORY.getCode().equals(item.getCategoryId())).map(ModelTagVO::getId).collect(Collectors.toList()));
            setTags(BeanUtil.copyToList(modelTagList.stream().filter(item -> ModelTagEnum.TAG.getCode().equals(item.getCategoryId())).collect(Collectors.toList()), ModelTagDTO.class));
        }
    }

    public void init(Long modelId) {
        for (ModelResourceDTO modelResourceDTO : amazonVideo) {
            modelResourceDTO.setModelId(modelId);
            modelResourceDTO.setType(ModelVideoResourceTypeEnum.AMAZON_VIDEO.getCode());
        }
        for (ModelResourceDTO modelResourceDTO : tiktokVideo) {
            modelResourceDTO.setModelId(modelId);
            modelResourceDTO.setType(ModelVideoResourceTypeEnum.TIKTOK_VIDEO.getCode());
        }
        amazonVideo.addAll(tiktokVideo);
    }
}

