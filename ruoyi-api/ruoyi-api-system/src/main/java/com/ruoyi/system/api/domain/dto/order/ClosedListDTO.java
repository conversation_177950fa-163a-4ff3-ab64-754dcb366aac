package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24 16:25
 */
@Data
public class ClosedListDTO implements Serializable {
    private static final long serialVersionUID = 4300551789915544266L;


    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 关闭原因（1：取消上传，2：不反馈给商家，3：订单回退，4：联动关闭）
     */
    @ApiModelProperty(value = "关闭原因（1：取消上传，2：不反馈给商家，3：订单回退，4：联动关闭）")
    private List<Integer> closeReason;

    /**
     * 关闭时间开始
     */
    @ApiModelProperty(value = "关闭时间开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date closeTimeBegin;

    /**
     * 关闭时间结束
     */
    @ApiModelProperty(value = "关闭时间结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date closeTimeEnd;

    /**
     * 根据关键字获取到的中文部/英文部客服ID
     */
    private Collection<Long> backUserIds;

    /**
     * 根据关键字获取到模特ID
     */
    private Collection<Long> modelIds;
}
