package com.ruoyi.system.api.domain.entity.biz.common;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/04
 */
@Data
@TableName("biz_resource")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BizResource implements Serializable {
    private static final long serialVersionUID = -5161851754448878840L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 资源URI
     */
    private String objectKey;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
