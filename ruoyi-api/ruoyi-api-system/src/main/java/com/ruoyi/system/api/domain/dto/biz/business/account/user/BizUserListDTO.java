package com.ruoyi.system.api.domain.dto.biz.business.account.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-30 17:18
 **/
@Data
public class BizUserListDTO implements Serializable {
    private static final long serialVersionUID = -2491411174027474838L;

    @ApiModelProperty("查询内容")
    private String searchName;

    @ApiModelProperty("账号状态：0-普通账号，1-主账号，2-子账号")
    private Integer accountType;

    @ApiModelProperty("客服id列表")
    private List<Long> waiterIds;

    @ApiModelProperty("渠道类型: 0-自然,1-市场渠道, 2-分销渠道, 4-官网")
    private Integer channelType;

    @ApiModelProperty("登录账号ID")
    private Long bizUserId;

    @ApiModelProperty("状态")
    private Integer status;
}
