package com.ruoyi.system.api.domain.dto.biz.channel;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.ChannelDiscountTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
* 渠道信息表
* <AUTHOR>
 * @TableName distribution_channel
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DistributionChancelEditDTO implements Serializable {

    private static final long serialVersionUID = 4583766812119615332L;
    @ApiModelProperty("分销渠道Id")
    @NotNull(message="[分销渠道Id]不能为空")
    private Long id;

    @Size(max= 12,message="[渠道名称]不能超过12位")
    @ApiModelProperty("渠道名称")
    @NotBlank(message="[渠道名称]不能为空")
    private String channelName;

    /**
     * 海报名称
     */
    @ApiModelProperty("海报名称")
    @Size(max= 12,message="[海报名称]不能超过12位")
    private String posterName;

    @Size(max= 4,message="[密码]不能超过4")
    @ApiModelProperty("密码")
    private String password;

    @Size(max= 11,message="[手机号]长度不能超过11")
    @ApiModelProperty("手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式有误")
    private String phone;

    @Size(max= 60,message="[备注]不能超过60")
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("结算佣金类型（1-固定金额，2-固定比例）")
    @EnumValid(enumClass = ChannelDiscountTypeEnum.class, message = "结算佣金类型不合法")
    @NotNull(message = "[结算佣金类型]不能为空")
    private Integer settleDiscountType;

    @ApiModelProperty("渠道佣金")
    @NotNull(message="[渠道佣金]不能为空")
    private BigDecimal brokeRage;

    @AssertTrue(message = "[结算佣金]有误")
    private boolean isSettleDiscountParam() {
        if (ObjectUtil.isNull(brokeRage)) {
            return Boolean.TRUE;
        }
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(settleDiscountType)) {
            //固定金额
            return brokeRage.compareTo(BigDecimal.ZERO) >= 0 && brokeRage.compareTo(BigDecimal.valueOf(9999)) <= 0;
        } else {
            //固定比例
            return brokeRage.compareTo(BigDecimal.ZERO) >= 0 && brokeRage.compareTo(BigDecimal.valueOf(100)) <= 0;
        }
    }

}
