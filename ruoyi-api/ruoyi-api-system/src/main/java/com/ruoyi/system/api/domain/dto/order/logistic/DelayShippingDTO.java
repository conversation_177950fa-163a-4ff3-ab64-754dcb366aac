package com.ruoyi.system.api.domain.dto.order.logistic;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 延迟发货
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DelayShippingDTO implements Serializable {

    private static final long serialVersionUID = 3720039063582560481L;
    @ApiModelProperty("物流跟进Id")
    @NotNull(message = "物流跟进Id不能为空")
    @Size(min = 1, message = "[物流跟进Id]至少需要一条数据")
    private List<Long> ids;

    @ApiModelProperty("是否默认发货时间：0-否，1-是")
    @NotNull(message = "是否默认发货时间不能为空")
    private Integer isDefaultLogisticStartTime;

    @ApiModelProperty("发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date logisticStartTime;

    @ApiModelProperty("说明")
    private String remark;

    @ApiModelProperty("图片地址")
    private List<String> resourceIds;

    @AssertTrue(message = "发货时间不能为空")
    private boolean isLogisticStartTime() {
        if (StatusTypeEnum.YES.getCode().equals(isDefaultLogisticStartTime)) {
            return true;
        }else {
            return ObjectUtil.isNotNull(logisticStartTime);
        }
    }
}
