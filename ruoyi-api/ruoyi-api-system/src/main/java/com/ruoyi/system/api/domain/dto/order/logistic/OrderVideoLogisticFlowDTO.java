package com.ruoyi.system.api.domain.dto.order.logistic;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.HandleStatusEnum;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :物流流转参数
 * @create :2025-04-23 09:31
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoLogisticFlowDTO implements Serializable {
    private static final long serialVersionUID = -1197457655964313619L;
    @ApiModelProperty("物流跟进id")
    private Long orderVideoLogisticFollowId;

    @ApiModelProperty("物流关联表id order_video_logistic.id")
    private Long orderVideoLogisticId;

    @ApiModelProperty("是否补发")
    private Integer reissue;

    @ApiModelProperty("物流单号")
    private String logisticNum;

    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    @ApiModelProperty("会员编码（business.member_code）")
    private String memberCode;

    @ApiModelProperty("视频编码")
    private String videoCode;

    @ApiModelProperty("视频id FK:order_video.id")
    private Long videoId;

    @ApiModelProperty("处理状态枚举(0-未通知、1-已通知、2-延迟发货、3-延迟发货提醒、4-延迟发货已提醒、5-催发货提醒、6-催发货已提醒、7-地址变更通知、8-变更已通知、9-补充说明、10-标记发货、11-标记发货提醒、12-标记发货已提醒、13-通知确认模特、14-已通知确认模特、15-催确认模特提醒、16-已通知催确认模特)")
    private HandleStatusEnum handleStatus;

    @ApiModelProperty("备注/说明")
    private String remark;

    @ApiModelProperty("预计发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date logisticStartTime;

    @ApiModelProperty("是否默认发货时间：0-否，1-是")
    private Integer isDefaultLogisticStartTime;

    @ApiModelProperty("图片地址")
    private List<String> resourceIds;

    @ApiModelProperty("最新物流主状态")
    private String latestMainStatus;

    @ApiModelProperty("物流系统同步时间、设置时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date logisticUpdateTime;

    @ApiModelProperty("模特名称")
    private String modelName;

    @ApiModelProperty("是否回调")
    private Integer isCallback;

    @ApiModelProperty("模特结果枚举(0-待处理、1-已询问、2-已收货、3-丢件、4-订单回退、5-待通知拍摄、6-已通知拍摄)")
    private Integer modelResult;

    @ApiModelProperty("实际签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    @ApiModelProperty(value = "是否作废（0-否 1-是）")
    private Integer isCancel;

    @ApiModelProperty("物流跟进数据")
    private OrderVideoLogisticFollow orderVideoLogisticFollow;

    @ApiModelProperty("批量更新数据-物流跟进数据")
    private List<OrderVideoLogisticFollow> orderVideoLogisticFollowList;

    @ApiModelProperty("账号信息")
    private LoginUserInfoVO loginUserInfoVO;
}
