package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.RefundStatusEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRefund;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/18 18:40
 */
@Data
public class RefundFlowDTO implements Serializable {
    private static final long serialVersionUID = 3663100248480476969L;
    private  List<OrderVideoRefund> refunds;
    private RefundStatusEnum refundStatus;
}
