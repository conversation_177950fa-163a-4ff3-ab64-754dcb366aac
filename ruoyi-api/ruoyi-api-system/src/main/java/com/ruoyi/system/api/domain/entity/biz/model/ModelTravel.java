package com.ruoyi.system.api.domain.entity.biz.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 模特行程对象 model_travel
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@ApiModel(value = "模特行程对象 model_travel")
@TableName("model_travel")
@Data
public class ModelTravel implements Serializable {

    private static final long serialVersionUID = -116253728646381736L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 模特id
     */
    @NotNull(message = "[模特id]不能为空")
    @ApiModelProperty(value = "模特id", required = true)
    @Excel(name = "模特id")
    private Long modelId;

    /**
     * 开始时间
     */
    @NotNull(message = "[开始时间]不能为空")
    @ApiModelProperty(value = "开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "[结束时间]不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 行程原因
     */
    @ApiModelProperty(value = "行程原因")
    @Excel(name = "行程原因")
    private String remark;
}
