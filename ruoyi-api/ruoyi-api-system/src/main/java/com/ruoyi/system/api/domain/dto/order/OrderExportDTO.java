package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单导出对应字段
 *
 * <AUTHOR>
 * @date 2024/6/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderExportDTO implements Serializable {

    private static final long serialVersionUID = 3016453173914499334L;
    /**
     * 支付时间
     */
    @Excel(name = "付款日期", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19, height = 144, defaultValue = StrPool.DASHED)
    private Date payTime;

    /**
     * 视频编码
     */
    @Excel(name = "视频编码", width = 10, defaultValue = StrPool.DASHED)
    private String videoCode;

    /**
     * 产品中文名
     */
    @Excel(name = "产品中文名", width = 20, defaultValue = StrPool.DASHED)
    private String productChinese;

    /**
     * 产品英文名
     */
    @Excel(name = "产品英文名", width = 20, defaultValue = StrPool.DASHED)
    private String productEnglish;

    /**
     * 产品链接
     */
    @Excel(name = "产品链接", width = 20, defaultValue = StrPool.DASHED)
    private String productLink;

    @Excel(name ="拍摄国家", readConverterExp = "1=英国,2=加拿大,3=德国,4=法国,5=意大利,6=西班牙,7=美国", defaultValue = StrPool.DASHED)
    private Integer shootingCountry;

    @Excel(name = "模特类型", readConverterExp = "0=亚马逊影响者,1=素人创作者,3=亚马逊影响者/素人创作者", defaultValue = StrPool.DASHED)
    private Integer modelType;

    @Excel(name = "使用平台", readConverterExp = "0=Amazon,1=tiktok,2=其他,3=APP/解说类", defaultValue = StrPool.DASHED)
    private Integer platform;

    /**
     * 拍摄建议（原拍摄要求）
     */
    @Excel(name = "拍摄建议", width = 18, defaultValue = StrPool.DASHED)
    private String shootingRequirement;

    /**
     * 模特要求（原匹配模特注意事项）
     */
    @Excel(name = "模特要求", width = 22, defaultValue = StrPool.DASHED)
    private String cautions;

    @Excel(name = "商品规格要求", width = 22, defaultValue = StrPool.DASHED)
    private String orderSpecificationRequire;

    @Excel(name = "特别强调", width = 22, defaultValue = StrPool.DASHED)
    private String particularEmphasis;


    @Excel(name = "参考视频", width = 22, defaultValue = StrPool.DASHED)
    private String referenceVideoLink;

    /**
     * 视频格式/风格
     */
    @Excel(name = "视频格式/风格", width = 33, defaultValue = StrPool.DASHED)
    private String videoFormatStyle;

    /**
     * 选配（1:2张/$10,2:5张/$20）
     */
    @Excel(name = "照片数量", readConverterExp = "1=2 photos,2=5 photos", width = 11, defaultValue = StrPool.DASHED)
    private Integer picCount;

    @Excel(name = "取消选配", width = 8, defaultValue = StrPool.DASHED)
    private String opentionAmount;

    /**
     * 下单运营
     */
    @Excel(name = "订单运营", width = 13, defaultValue = StrPool.DASHED)
    private String orderOperation;

    /**
     * 中文部/英文部
     */
    @Excel(name = "中文部/英文部", width = 14, defaultValue = StrPool.DASHED)
    private String chineseDepartmentEnglishDepartment;

    @Excel(name = "订单补偿金额", width = 8, defaultValue = StrPool.DASHED)
    private String reparationAmount;

    /**
     * 订单状态
     */
    @Excel(name = "订单状态", readConverterExp = "1=待支付,2=待审核,3=待确认,4=待匹配,5=需发货,6=待完成,7=需确认,8=已完成,9=交易关闭", width = 10, defaultValue = StrPool.DASHED)
    private Integer status;

    /**
     * 订单备注
     */
    @Excel(name = "订单备注", width = 24, defaultValue = StrPool.DASHED)
    private String orderNote;
    /**
     * 拍摄模特/国家
     */
    @Excel(name = "拍摄模特/国家", width = 15, defaultValue = StrPool.DASHED)
    private String shootModelCountry;

    @Excel(name = "发货备注", defaultValue = StrPool.DASHED)
    private String shippingRemark;


    /**
     * 是否携带
     */
    @Excel(name = "是否携带", readConverterExp = "1=主携带,2=被携带", width = 13, defaultValue = StrPool.DASHED)
    private Integer carryType;

    /**
     * 币种
     */
    @Excel(name = "币种", readConverterExp = "USD=美金,CAD=加币,GBP=英镑,EUR=欧元", width = 8, defaultValue = StrPool.DASHED)
    private String modelCommissionUnit;

    /**
     * 模特佣金
     */
    @Excel(name = "模特佣金", width = 8, defaultValue = StrPool.DASHED)
    private BigDecimal modelCommission;

    @Excel(name = "模特质量", readConverterExp = "0=一般模特,1=优质模特,2=中度模特", width = 13, defaultValue = StrPool.DASHED)
    private Integer cooperation;

    @Excel(name = "确认提交时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19, height = 144, defaultValue = StrPool.DASHED)
    private Date submitPreselectModelDate;

    @Excel(name = "发货时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19, height = 144, defaultValue = StrPool.DASHED)
    private Date shippingTime;
    /**
     * 物流信息
     */
    @Excel(name = "物流信息（最新）", width = 17, defaultValue = StrPool.DASHED)
    private String logisticsInfo;

    @Excel(name = "签收时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19, height = 144, defaultValue = StrPool.DASHED)
    private Date receiptTime;

    /**
     * 视频时长/剪辑要求
     */
    @Excel(name = "视频时长/剪辑要求", width = 19, defaultValue = StrPool.DASHED)
    private String videoDurationClipRequirements;

    /**
     * 素材链接
     */
    @Excel(name = "素材链接", width = 40, defaultValue = StrPool.DASHED)
    private String materialLink;

    @Excel(name = "上传需求提交日期", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 15, defaultValue = StrPool.DASHED)
    private Date submitDate;

    @Excel(name = "反馈视频素材给商家时间（第一次)", dateFormat = DatePattern.NORM_DATETIME_PATTERN, defaultValue = StrPool.DASHED)
    private Date firstSendFeedVideoDate;

    @Excel(name = "反馈照片素材给商家时间（第一次）", dateFormat = DatePattern.NORM_DATETIME_PATTERN, defaultValue = StrPool.DASHED)
    private Date firstSendPhotoFeedDate;

    /**
     * 反馈视频素材给商家时间（最新）
     */
    @Excel(name = "反馈视频素材给商家时间（最新）", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 15, defaultValue = StrPool.DASHED)
    private Date feedbackDate;

    /**
     * 反馈素材给商家时间（最新）
     */
    @Excel(name = "反馈照片素材给商家时间（最新）", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 15, defaultValue = StrPool.DASHED)
    private Date feedbackImageDate;

    /**
     * 售后单状态
     */
    @Excel(name = "售后单状态", readConverterExp = "0=无售后,1=售后中,4=售后完结", width = 10, defaultValue = StrPool.DASHED)
    private Integer afterSaleTaskStatus;

    @Excel(name = "售后类型", defaultValue = StrPool.DASHED, readConverterExp = "1=重拍视频,2=补拍视频,3=重拍视频\n补拍视频")
    private Integer afterSaleType;

    /**
     * 提交上传需求人
     */
    @Excel(name = "提交上传需求人", width = 14, defaultValue = StrPool.DASHED)
    private String submitter;

    /**
     * 上传链接
     */
    @Excel(name = "上传链接", width = 10, defaultValue = StrPool.DASHED)
    private String uploadLink;

    /**
     * 视频标题
     */
    @Excel(name = "视频标题", width = 20, defaultValue = StrPool.DASHED)
    private String videoTitle;

    @Excel(name = "上传备注", defaultValue = StrPool.DASHED)
    private String uploadLinkRemark;

    @Excel(name = "确认收货", dateFormat = DatePattern.NORM_DATETIME_PATTERN, defaultValue = StrPool.DASHED)
    private Date finalReceiptTime;

    @Excel(name = "确认模特", dateFormat = DatePattern.NORM_DATETIME_PATTERN, defaultValue = StrPool.DASHED)
    private Date finalReconDate;

    @Excel(name = "是否回退", readConverterExp = "1=回退订单",width = 13, defaultValue = StrPool.DASHED)
    private Integer isRollback;

    @Excel(name = "是否照顾单", readConverterExp = "0=否,1=是")
    private Integer isCare;

}
