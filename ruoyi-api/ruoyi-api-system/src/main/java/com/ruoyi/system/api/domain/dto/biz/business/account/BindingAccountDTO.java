package com.ruoyi.system.api.domain.dto.biz.business.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-28 11:38
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BindingAccountDTO implements Serializable {
    private static final long serialVersionUID = 2272055337674420300L;

    @ApiModelProperty("账号ID")
    @NotNull(message = "[账号ID]不能为空")
    private Long businessAccountId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式有误")
    @NotBlank(message = "手机号不为空")
    private String phone;

    @ApiModelProperty("ticket: 'VER'开头")
    @NotNull(message = "[ticket]不能为空")
    private String ticket;
}
