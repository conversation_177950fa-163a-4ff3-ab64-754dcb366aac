package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.enums.VideoContentTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("运营订单编辑入参")
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderOperationVideoDTO extends OrderVideoDTO  {
    private static final long serialVersionUID = 3703853790071912212L;
    /**
     * 模特要求（原匹配模特注意事项）
     */
    @ApiModelProperty("模特要求（原匹配模特注意事项）")
    @Valid
    private OrderVideoCautionsDTO orderVideoCautionsDTO;

    /**
     * 剪辑要求
     */
    @ApiModelProperty(value = "剪辑要求")
    @Valid
    @Size(max = 1, message = "[剪辑要求]只能有一条")
    private List<VideoContentDTO> clipsRequired;

    @ApiModelProperty(value = "商品规格要求")
    @Size(max = 300, message = "[商品规格要求]长度不能超过300")
    private String orderSpecificationRequire;

    @ApiModelProperty(value = "特别强调")
    @Size(max = 500, message = "[特别强调]长度不能超过500")
    private String particularEmphasis;

    @ApiModelProperty(value = "特别强调图片")
    @Size(max = 10, message = "[特别强调图片]最多只能有10条")
    private List<String> particularEmphasisPic;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "视频时长")
    @NotNull(message = "[视频时长]不能为空")
    private Integer videoDuration;

    /**
     * 产品品类
     */
    @ApiModelProperty(value = "产品品类")
    @NotEmpty(message = "[产品品类]不能为空")
    @Size(min = 1, message = "[产品品类]至少选择1个")
    @Size(max = 100, message = "[产品品类]最多选择100个")
    private List<Long> productCategory;

    /**
     * @see com.ruoyi.common.core.enums.VideoContentTypeEnum
     */
    @ApiModelProperty(value = "视频订单-审核需要传入类型固定9")
    private Integer auditType;

    /**
     * 是通品（1：是，0：不是）
     */
    @ApiModelProperty(value = "是通品（1：是，0：不是）")
    @NotNull(message = "[通品]不能为空")
    @EnumValid(enumClass = StatusTypeEnum.class, message = "通品不可为空")
    private Integer isGund;

    @Override
    public void initOrderVideoContents() {
        super.initOrderVideoContents();
        super.formatVideoLink();
        if (ObjectUtil.isNotNull(orderVideoCautionsDTO) && CollUtil.isNotEmpty(orderVideoCautionsDTO.getCautions())) {
            for (VideoContentDTO caution : orderVideoCautionsDTO.getCautions()) {
                caution.setVideoId(id);
                caution.setType(VideoContentTypeEnum.CAUTIONS.getCode());
                caution.setFirstContent(StrUtil.isNotBlank(caution.getContent()) ? caution.getContent() : null);
            }
            orderVideoCautionsDTO.getCautions().removeIf(item -> CharSequenceUtil.isBlank(item.getContent()));
            orderVideoContents.addAll(BeanUtil.copyToList(orderVideoCautionsDTO.getCautions(), OrderVideoContent.class));
        }
        if (StrUtil.isNotBlank(orderSpecificationRequire)) {
            OrderVideoContent orderVideoContent = new OrderVideoContent();
            orderVideoContent.setVideoId(id);
            orderVideoContent.setType(VideoContentTypeEnum.ORDER_SPECIFICATION_REQUIRE.getCode());
            orderVideoContent.setContent(orderSpecificationRequire);
            orderVideoContent.setSort(0);
            orderVideoContents.add(orderVideoContent);
        }

        if (CollUtil.isNotEmpty(particularEmphasisPic)) {
            OrderVideoContent orderVideoContent = new OrderVideoContent();
            orderVideoContent.setVideoId(id);
            orderVideoContent.setType(VideoContentTypeEnum.PARTICULAR_EMPHASIS_PIC.getCode());
            orderVideoContent.setContent(StrUtil.join(StrUtil.COMMA, particularEmphasisPic));
            orderVideoContent.setSort(0);
            orderVideoContents.add(orderVideoContent);
        }

        if (StrUtil.isNotBlank(particularEmphasis)) {
            OrderVideoContent orderVideoContent = new OrderVideoContent();
            orderVideoContent.setVideoId(id);
            orderVideoContent.setType(VideoContentTypeEnum.PARTICULAR_EMPHASIS.getCode());
            orderVideoContent.setContent(particularEmphasis);
            orderVideoContent.setSort(0);
            orderVideoContents.add(orderVideoContent);
        }
        if (CollUtil.isNotEmpty(clipsRequired)) {
            for (VideoContentDTO caution : clipsRequired) {
                caution.setVideoId(id);
                caution.setType(VideoContentTypeEnum.CLIPS_REQUIRED.getCode());
            }
            clipsRequired.removeIf(item -> CharSequenceUtil.isBlank(item.getContent()));
            orderVideoContents.addAll(BeanUtil.copyToList(clipsRequired, OrderVideoContent.class));
        }
    }
}
