package com.ruoyi.system.api.domain.dto.biz.business;

import cn.hutool.core.text.CharSequenceUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21 17:41
 */
@Data
@ApiModel("模特商家端列表入参")
public class BusinessAccountCollectModelDTO implements Serializable {
    private static final long serialVersionUID = 6567248449599768557L;
    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id")
    private List<Long> id;
    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String modelAccount;
    /**
     * 模特名称
     */
    @ApiModelProperty(value = "模特名称")
    private String name;

    /**
     * 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "平台（0:Amazon,1:tiktok,2:其他,3:APP/解说类）", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private List<String> platform;
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;
    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private List<Long> nation;
    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    private List<String> type;
    /**
     * 性别(0:男,1:女)
     */
    @ApiModelProperty(value = "性别（1:男,0:女）", notes = "1:男,0:女")
    private List<Integer> sex;
    /**
     * 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
     */
    @ApiModelProperty(value = "年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）", notes = "1:婴幼儿,2:儿童,3:成年人,4:老年人")
    private List<Integer> ageGroup;
    /**
     * 擅长品类
     */
    @ApiModelProperty(value = "擅长品类")
    private List<String> specialtyCategory;
    /**
     * 模特标签
     */
    @ApiModelProperty(value = "模特标签")
    private List<String> modelTag;
    /**
     * 无法接单的模特
     */
    @ApiModelProperty(value = "无法接单的模特")
    private List<Long> cannotModel;

    @ApiModelProperty(value = "是否需要所有状态模特：1-需要")
    private Integer needAllModel;

    @ApiModelProperty(value = "用于判断是否首页模特列表1=是，2=创建订单-选择模特（否）")
    private Integer isIndex = 1;

    public void init() {
        if (CharSequenceUtil.isNotBlank(keyword)) {
            keyword = keyword.trim();
        }
    }
    public String getStoreString(){
        return "BusinessAccountCollectModelDTO{" +
                "id=" + id +
                ", modelAccount='" + modelAccount + '\'' +
                ", name='" + name + '\'' +
                ", platform=" + platform +
                ", keyword='" + keyword + '\'' +
                ", nation=" + nation +
                ", type=" + type +
                ", sex=" + sex +
                ", ageGroup=" + ageGroup +
                ", specialtyCategory=" + specialtyCategory +
                ", modelTag=" + modelTag +
                ", needAllModel=" + needAllModel +
                '}';
    }
}
