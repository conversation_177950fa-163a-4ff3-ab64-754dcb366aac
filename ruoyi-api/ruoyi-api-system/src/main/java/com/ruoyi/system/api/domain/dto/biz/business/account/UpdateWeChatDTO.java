package com.ruoyi.system.api.domain.dto.biz.business.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-31 15:58
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpdateWeChatDTO implements Serializable {
    private static final long serialVersionUID = -258984425630979663L;

    @ApiModelProperty("ticket: 'REB'开头")
    @NotBlank(message = "[ticket]不能为空")
    private String ticket;

    @ApiModelProperty("账号ticket")
    @NotBlank(message = "[账号ticket]不能为空")
    private String accountTicket;
}
