package com.ruoyi.system.api.domain.dto.order.logistic;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.LogisticOperationTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 提醒发货
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OperationVideoLogisticDTO implements Serializable {

    private static final long serialVersionUID = -7228672116695046317L;
    @ApiModelProperty("物流跟进Id")
    @NotNull(message = "物流跟进Id不能为空")
    private Long id;

    @ApiModelProperty("操作类型:1-修改单号，2-作废")
    @NotNull(message = "操作类型不能为空")
    private Integer type;

    @ApiModelProperty("物流单号")
    private String number;

    @ApiModelProperty("原因")
    @NotBlank(message = "原因不能为空")
    private String remark;

    @AssertTrue(message = "物流单号不能为空")
    private boolean isLogisticStartTime() {
        if (LogisticOperationTypeEnum.UPDATE.getCode().equals(type)) {
            return StrUtil.isNotBlank(number);
        }
        return true;
    }

}
