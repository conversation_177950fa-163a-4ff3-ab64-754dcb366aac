package com.ruoyi.system.api.domain.dto.biz.business.balance;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 余额提现审核表
 *
 * <AUTHOR>
 * @TableName business_balance_audit_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceAuditFlowListDTO implements Serializable {

    private static final long serialVersionUID = 2294834581858276229L;

    @ApiModelProperty("关键字")
    @Size(max = 100, message = "[关键字]长度不能超过100字符")
    private String keyword;

    @ApiModelProperty("审核状态（0:待处理,1:已提现,2.已取消）")
    private List<Integer> auditStatus;

    @ApiModelProperty("模糊搜索：账号微信名、企业名称、会员账号、账号")
    private String searchNameMemberCodeAccount;

    @ApiModelProperty("商家id列表")
    private List<Long> businessIds;

    @ApiModelProperty("商家id列表")
    private List<Long> createUserIds;

    @ApiModelProperty("是否过滤通知 0：否 1：是")
    private Integer filterNotice = 0;

    @ApiModelProperty("提现通知状态 0：未通知 1：已通知")
    private Integer noticeStatus;

    @ApiModelProperty(value = "发起时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTimeBegin;

    @ApiModelProperty(value = "发起时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTimeEnd;

    @ApiModelProperty(value = "审批时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTimeBegin;

    @ApiModelProperty(value = "审批时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTimeEnd;

}
