package com.ruoyi.system.api.domain.dto.biz.business.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 账号申请表
 *
 * <AUTHOR>
 * @TableName business_account_apply
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuditAccountApplyDTO implements Serializable {

    private static final long serialVersionUID = 7595819103098953049L;

    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[审核状态（0:待审核,1:审核通过,2.拒绝）]不能为空")
    @ApiModelProperty("审核状态（0:待审核,1:审核通过,2.拒绝）")
    private Integer auditStatus;

}
