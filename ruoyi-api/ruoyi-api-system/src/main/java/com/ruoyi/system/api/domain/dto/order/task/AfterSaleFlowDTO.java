package com.ruoyi.system.api.domain.dto.order.task;

import com.ruoyi.system.api.domain.dto.order.TaskDetailOperateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-11 18:09
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AfterSaleFlowDTO extends TaskDetailOperateDTO implements Serializable {
    private static final long serialVersionUID = 7325627215134608278L;

    @ApiModelProperty(value = "操作类型：101-创建售后 102-拒绝售后 103-确认售后 104-反馈素材给商家 105-申请取消 106-撤销申请 107-取消售后 108-拒绝取消 109-重新打开 110-取消工单")
    private Integer operateType;
}
