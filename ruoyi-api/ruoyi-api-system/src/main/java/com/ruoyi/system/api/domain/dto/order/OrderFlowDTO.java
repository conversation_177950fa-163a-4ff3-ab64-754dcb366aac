package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/18 18:40
 */
@Data
public class OrderFlowDTO implements Serializable {
    private static final long serialVersionUID = 8332215413575352338L;
    private List<OrderVideo> orderVideos;
    private OrderStatusEnum orderStatus;
    private String eventName;
}
