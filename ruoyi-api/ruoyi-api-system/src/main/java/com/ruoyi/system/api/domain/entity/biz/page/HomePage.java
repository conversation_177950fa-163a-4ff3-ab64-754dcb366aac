package com.ruoyi.system.api.domain.entity.biz.page;/**
 * @Title: HomePage
 * <AUTHOR>
 * @Package com.ruoyi.system.api.domain.entity
 * @Date 2024/8/20 18:26
 * @description: 首页配置
 */

import com.ruoyi.system.api.domain.vo.biz.page.HomePageTagVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * @program :woniu-world
 * @description :首页配置
 * <AUTHOR>
 * @create :2024-08-20 18:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HomePage implements Serializable {
    private static final long serialVersionUID = -8683471537993646L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("是否开启搜索：0-否，1-是")
    @NotNull(message = "[是否开启搜索]不能为空")
    private Integer showSearch;

    @ApiModelProperty("搜索提示")
    private String searchHint;

    @ApiModelProperty("分类列表")
    @Valid
    @Size(min = 1, message = "至少需要有一条分类信息")
    private List<HomePageTagVO> homePageTagList;

}
