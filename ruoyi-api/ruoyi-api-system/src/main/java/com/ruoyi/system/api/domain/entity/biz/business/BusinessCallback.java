package com.ruoyi.system.api.domain.entity.biz.business;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:17
 */
@Data
@TableName("business_callback")
public class BusinessCallback implements Serializable {
    private static final long serialVersionUID = 6485562248157649368L;


    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID (FK:business.id)
     */
    @ApiModelProperty("商家ID")
    private Long businessId;

    /**
     * 回访状态（1:待回访,2:回访中,3:已回访）
     */
    @ApiModelProperty("回访状态（1:待回访,2:回访中,3:已回访）")
    private Integer status;

    /**
     * 标记时间
     */
    @ApiModelProperty("标记时间")
    private Date markTime;

    /**
     * 记录时间
     */
    @ApiModelProperty("记录时间")
    private Date writeTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
