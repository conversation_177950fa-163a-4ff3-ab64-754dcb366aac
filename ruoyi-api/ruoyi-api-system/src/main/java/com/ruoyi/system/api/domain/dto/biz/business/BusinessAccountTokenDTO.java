package com.ruoyi.system.api.domain.dto.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-23 16:03
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessAccountTokenDTO implements Serializable {
    private static final long serialVersionUID = 6483669202163380509L;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("微信昵称")
    private String nickName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("头像")
    private String pic;

    @ApiModelProperty("unionId")
    private String unionid;

    @ApiModelProperty("ExternalUserID企业微信外部联系人id")
    private String externalUserId;


    @ApiModelProperty("商家名称")
    private String businessName;

    @ApiModelProperty("商家规模")
    private Integer businessScale;

    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;

    @ApiModelProperty("手机号可显示(0:否,1:是)")
    private Integer phoneVisible;

    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;

    @ApiModelProperty("帐号余额")
    private BigDecimal balance;

    @NotNull(message = "[使用余额]不能为空")
    private BigDecimal useBalance;

    @ApiModelProperty("余额是否锁定（0-不锁定，1-锁定）")
    private Integer isBalanceLock;

    @ApiModelProperty("对接客服  FK：sys_user.user_id")
    private Long waiterId;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty("会员类型: 0-非会员，1-会员")
    private Integer memberType;

    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;

    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberPackageType;

    @ApiModelProperty("会员首次购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberFirstTime;

    @ApiModelProperty("首次购买套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberFirstType;

    @ApiModelProperty("会员最近购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberLastTime;

    @ApiModelProperty("会员有效期")
    private Date memberValidity;
}
