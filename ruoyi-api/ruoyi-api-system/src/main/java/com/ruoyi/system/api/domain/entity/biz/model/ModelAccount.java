package com.ruoyi.system.api.domain.entity.biz.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
* 模特账号表
* <AUTHOR>
 * @TableName model_account
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModelAccount implements Serializable {

    private static final long serialVersionUID = -4628810582472754273L;

    @NotNull(message="[主键Id]不能为空")
    @ApiModelProperty("主键Id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotNull(message="[模特Id]不能为空")
    @ApiModelProperty("模特Id")
    private Long modelId;

    @NotBlank(message="[模特账号]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("模特账号")
    @Length(max= 20,message="编码长度不能超过20")
    private String account;

    @NotBlank(message="[模特登录账户]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("模特登录账号")
    @Length(max= 20,message="模特登录账号长度不能超过20")
    private String loginAccount;

    @ApiModelProperty("最后登录时间")
    private Date lastLoginTime;

    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @NotNull(message="[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
