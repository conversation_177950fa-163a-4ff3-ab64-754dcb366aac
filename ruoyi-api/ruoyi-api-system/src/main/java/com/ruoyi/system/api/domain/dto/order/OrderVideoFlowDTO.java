
package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/2 16:51
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoFlowDTO implements Serializable {

    private static final long serialVersionUID = 8098527317561655468L;
    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty("视频id (FK:order_video.id)")
    private Long videoId;

    /**
     * 原先视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）
     */
    @ApiModelProperty("原先视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer originStatus;


    /**
     * 目标视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）
     */
    @ApiModelProperty("目标视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer targetStatus;
}
