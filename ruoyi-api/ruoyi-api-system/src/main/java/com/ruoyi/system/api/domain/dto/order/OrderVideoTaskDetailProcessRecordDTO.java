package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11 17:58
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoTaskDetailProcessRecordDTO {

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    @NotBlank(message = "[工单编号]不能为空")
    private String taskNum;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    @NotBlank(message = "[内容]不能为空")
    @Size(max = 1000, message = "[内容]长度不能超过1000")
    private String content;

    /**
     * 处理记录图片列表
     */
    @ApiModelProperty(value = "处理记录图片列表")
    private List<String> objectKeys;

    /**
     * 完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）
     */
    private Integer completionMode;

    /**
     * 操作类型 详见OrderTaskDetailFlowOperateTypeEnum
     * @see com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum
     */
    private Integer operateType;

    /**
     * 记录时间
     */
    @ApiModelProperty(value = "记录时间")
    private Date time;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operateBy;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operateById;

    /**
     * 操作人类型（1：处理人，2：剪辑人，3：系统）
     */
    @ApiModelProperty(value = "操作人类型（1：处理人，2：剪辑人，3：系统）")
    private Integer operateByType;
}
