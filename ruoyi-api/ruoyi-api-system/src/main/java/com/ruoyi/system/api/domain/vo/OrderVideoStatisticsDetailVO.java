package com.ruoyi.system.api.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :视频订单统计
 * @create :2024-06-25 09:57
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "视频订单统计详情vo")
@Builder
public class OrderVideoStatisticsDetailVO implements Serializable {
    private static final long serialVersionUID = 3832096627784909821L;

    @ApiModelProperty(value = "商家id")
    private Long merchantId;

    @ApiModelProperty(value = "视频总数量")
    private Integer orderVideoTotal;

    @ApiModelProperty(value = "进30天排单量")
    private Integer recentOrderTotal;

    @ApiModelProperty(value = "待完成订单")
    private Integer preFinishOrderTotal;

    /**
     * 售后率
     */
    @ApiModelProperty(value = "售后率")
    private BigDecimal afterSaleRate;
}
