package com.ruoyi.system.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :修改商家余额
 * @create :2024-06-27 11:08
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceDTO implements Serializable {
    private static final long serialVersionUID = 5932727932672000571L;

    @ApiModelProperty("商家id")
    @NotNull(message = "[商家id]不能为空")
    private Long businessId;

    /**
     * 添加余额 则使用负数
     */
    @ApiModelProperty("使用余额order_table.use_balance")
    @NotNull(message = "[使用余额]不能为空")
    private BigDecimal useBalance;

    @ApiModelProperty("锁定状态")
    @NotNull(message = "[锁定状态]不能为空")
    private Integer isBalanceLock;

    @NotNull(message = "[订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6提现支出)]不能为空")
    @ApiModelProperty("订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6提现支出)")
    private Integer origin;

    @ApiModelProperty(value = "财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭")
    private Integer auditStatus;

    @ApiModelProperty(value = "余额提现审核状态（0:待处理,1:已提现,2.已取消")
    private Integer balanceAuditStatus;

    @ApiModelProperty(value = "是否余额支付")
    private Integer isBalancePay;

    @Valid
    @NotEmpty(message = "余额流水数据不能为空")
    private List<BusinessBalanceFlowDTO> businessBalanceFlowDTOS;

    /**
     * 是否是视频订单操作
     */
    private boolean isVideoOperate;

}
