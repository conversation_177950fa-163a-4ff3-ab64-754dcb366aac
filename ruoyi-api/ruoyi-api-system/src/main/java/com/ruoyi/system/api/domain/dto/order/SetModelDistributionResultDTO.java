package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.DistributionResultEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/17 17:03
 */
@Data
public class SetModelDistributionResultDTO implements Serializable {
    private static final long serialVersionUID = -302423656875525253L;

    /**
     * 预选模特ID
     */
    @ApiModelProperty("预选模特ID")
    @NotNull(message = "[预选模特ID]不能为空")
    private Long preselectModelId;

    /**
     * 分发结果（1：待处理，2：MT想要，3：MT不想要，4：取消分发）
     */
    @ApiModelProperty(value = "分发结果（1：待处理，2：MT想要，3：MT不想要，4：取消分发）")
    @EnumValid(enumClass = DistributionResultEnum.class, message = "[分发结果]输入错误")
    private Integer distributionResult;
}
