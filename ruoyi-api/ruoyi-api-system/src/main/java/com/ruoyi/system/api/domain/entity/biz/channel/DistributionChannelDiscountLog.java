package com.ruoyi.system.api.domain.entity.biz.channel;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 渠道折扣日志
 *
 * <AUTHOR>
 * @TableName distribution_channel_discount_log
 */
@Data
public class DistributionChannelDiscountLog implements Serializable {

    private static final long serialVersionUID = -5644982030659589172L;

    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("渠道ID")
    private Long channelId;

    @NotNull(message = "[渠道类型：2-分销渠道 7-裂变]不能为空")
    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @NotNull(message = "[会员折扣类型（1-固定金额，2-固定比例）]不能为空")
    @ApiModelProperty("会员折扣类型（1-固定金额，2-固定比例）")
    private Integer memberDiscountType;

    @NotNull(message = "[会员折扣]不能为空")
    @ApiModelProperty("会员折扣")
    private BigDecimal memberDiscount;

    @NotNull(message = "[结算佣金类型（1-固定金额，2-固定比例）]不能为空")
    @ApiModelProperty("结算佣金类型（1-固定金额，2-固定比例）")
    private Integer settleDiscountType;

    @NotNull(message = "[结算佣金]不能为空")
    @ApiModelProperty("结算佣金")
    private BigDecimal settleDiscount;

    @NotNull(message = "[活动开始时间]不能为空")
    @ApiModelProperty("活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @NotNull(message = "[活动结束时间]不能为空")
    @ApiModelProperty("活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @NotNull(message = "[操作人（sys_user.user_id）]不能为空")
    @ApiModelProperty("操作人（sys_user.user_id）")
    private Long createById;

    @NotBlank(message = "[操作人名称（sys_user.user_name）]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("操作人名称（sys_user.user_name）")
    @Length(max = 30, message = "编码长度不能超过30")
    private String createBy;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
