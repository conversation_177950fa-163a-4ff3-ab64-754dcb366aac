package com.ruoyi.system.api.domain.entity.biz.channel;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 市场渠道信息表
 *
 * <AUTHOR>
 * @TableName marketing_channel
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MarketingChannel implements Serializable {

    private static final long serialVersionUID = -426514988197043567L;

    @TableId(type = IdType.AUTO)
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)]不能为空")
    @ApiModelProperty("市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)")
    private Integer marketingPlatform;

    @NotBlank(message = "[市场渠道名称]不能为空")
    @Size(max = 27, message = "编码长度不能超过27")
    @ApiModelProperty("市场渠道名称")
    @Length(max = 27, message = "编码长度不能超过27")
    private String marketingChannelName;

    @NotNull(message = "[落地形式]不能为空")
    @ApiModelProperty("落地形式（1:官网首页，2:添加企微客服）")
    private Integer landingForm;

    @NotBlank(message = "[专属链接code（SC + 8位随机字符）]不能为空")
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("专属链接code（SC + 8位随机字符）")
    @Length(max = 10, message = "编码长度不能超过10")
    private String dedicatedLinkCode;

    @NotBlank(message = "[专属企微二维码地址]不能为空")
    @Size(max = 150, message = "编码长度不能超过150")
    @ApiModelProperty("专属企微二维码地址")
    @Length(max = 150, message = "编码长度不能超过150")
    private String weChatUrl;

    @NotBlank(message = "[标签id]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("标签id")
    @Length(max = 32, message = "编码长度不能超过32")
    private String tagId;

    @NotNull(message = "[独立访客]不能为空")
    @ApiModelProperty("独立访客")
    private Integer uniqueVisitor;

    @NotNull(message = "[访问量]不能为空")
    @ApiModelProperty("访问量")
    private Integer pageView;

    @NotNull(message = "[跳出率]不能为空")
    @ApiModelProperty("跳出率")
    private BigDecimal bounceRate;

    @NotNull(message = "[状态（0=正常,1=禁用）]不能为空")
    @ApiModelProperty("状态（0=正常,1=禁用）")
    private Integer status;

    @NotBlank(message = "[备注]不能为空")
    @Size(max = 60, message = "编码长度不能超过60")
    @ApiModelProperty("备注")
    @Length(max = 60, message = "编码长度不能超过60")
    private String remark;

    @ApiModelProperty("创建人id")
    private Long createId;

    @NotBlank(message = "[创建人]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("创建人")
    @Length(max = 32, message = "编码长度不能超过32")
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新人id")
    private Long updateId;

    @NotBlank(message = "[更新人]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("更新人")
    @Length(max = 32, message = "编码长度不能超过32")
    private String updateBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
