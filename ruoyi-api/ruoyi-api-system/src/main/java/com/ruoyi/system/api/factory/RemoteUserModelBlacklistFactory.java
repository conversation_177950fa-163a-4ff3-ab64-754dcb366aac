package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemoteUserModelBlacklistService;
import com.ruoyi.system.api.domain.entity.biz.model.UserModelBlacklist;
import com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 模特拉黑服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteUserModelBlacklistFactory implements FallbackFactory<RemoteUserModelBlacklistService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteUserModelBlacklistFactory.class);

    @Override
    public RemoteUserModelBlacklistService create(Throwable throwable) {
        log.error("模特拉黑服务调用失败");
        return new RemoteUserModelBlacklistService() {
            @Override
            public List<UserModelBlacklist> selectBlackModelListByBizUserId(String source) {
                return Collections.emptyList();
            }

            @Override
            public List<UserBlackModelVO> userBlackModelListByBizUserId(Long bizUserId, String source) {
                return Collections.emptyList();
            }

            @Override
            public List<UserModelBlacklist> userBlackModelListByModelId(Long modelId, String source) {
                return null;
            }

            @Override
            public List<Long> getBlackModelBusinessIdsByModelId(Long modelId, String source) {
                return null;
            }

            @Override
            public List<UserBlackModelVO> batchUserBlackModelListByBizUserIds(List<Long> bizUserIds, String source) {
                return Collections.emptyList();
            }
        };
    }
}
