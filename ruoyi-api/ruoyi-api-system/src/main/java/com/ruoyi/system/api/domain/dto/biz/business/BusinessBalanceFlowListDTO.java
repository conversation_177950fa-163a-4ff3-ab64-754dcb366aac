package com.ruoyi.system.api.domain.dto.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 余额流水表
 *
 * <AUTHOR>
 * @TableName business_balance_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceFlowListDTO implements Serializable {
    private static final long serialVersionUID = -3206162264681086313L;

    @ApiModelProperty("关键字")
    @Size(max = 100, message = "[关键字]长度不能超过100字符")
    private String keyword;

    @ApiModelProperty("商家id")
    private Long businessId;

    @ApiModelProperty("商家主账号")
    private String ownerAccount;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("订单号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String orderNum;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("退款审批号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String refundNum;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("视频编码")
    @Length(max = 30, message = "编码长度不能超过30")
    private String videoCode;

    @NotNull(message = "[订单类型(0-收入、1-支出)]不能为空")
    @ApiModelProperty("订单类型(0-收入、1-支出)")
    private Integer type;

    @NotNull(message = "[订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6.余额提现、7.线下钱包充值收入、8.线上钱包充值)]不能为空")
    @ApiModelProperty("订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6.余额提现、7.线下钱包充值收入、8.线上钱包充值)")
    private Integer origin;

    @ApiModelProperty("订单来源列表(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6.余额提现、7.线下钱包充值收入、8.线上钱包充值)")
    private Set<Integer> origins;

    @ApiModelProperty("订单交易开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeBegin;

    @ApiModelProperty("订单交易结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeEnd;

}
