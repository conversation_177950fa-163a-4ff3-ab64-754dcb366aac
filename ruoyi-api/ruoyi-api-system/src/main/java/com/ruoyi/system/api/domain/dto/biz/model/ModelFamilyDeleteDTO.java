package com.ruoyi.system.api.domain.dto.biz.model;

import com.ruoyi.common.core.enums.ModelFamilyRelationshipTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :添加家庭模特入参
 * @create :2024-11-27 15:14
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ModelFamilyDeleteDTO implements Serializable {
    private static final long serialVersionUID = -1388939136524595783L;

    @ApiModelProperty(value = "家庭id")
    @NotNull(message = "[家庭id]不能为空")
    private Long familyId;

    @ApiModelProperty(value = "模特ID")
    @NotNull(message = "[模特ID]不能为空")
    private Long modelId;

}
