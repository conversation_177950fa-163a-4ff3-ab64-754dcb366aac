package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/7 10:51
 */
@Data
public class CompanyNotInvoicedListDTO implements Serializable {
    private static final long serialVersionUID = -703841694495997993L;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 订单类型（0-视频订单，1-会员订单）
     */
    @ApiModelProperty(value = "订单类型（0-视频订单，1-会员订单）")
    private Integer orderType;

    /**
     * 支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）
     */
    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）")
    private List<Integer> payType;

    /**
     * 支付时间-开始
     */
    @ApiModelProperty(value = "支付时间-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date payTimeBegin;

    /**
     * 支付时间-结束
     */
    @ApiModelProperty(value = "支付时间-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date payTimeEnd;

    /**
     * 大订单提现了多少钱
     */
    @Null(message = "请勿传递[orderPayoutAmountMap]")
    private Map<String, BigDecimal> orderPayoutAmountMap;

    /**
     * 当前登录商家
     */
    @Null(message = "请勿传递[businessId]")
    private Long businessId;

    /**
     * 大订单ID
     */
    @Null(message = "请勿传递[orderId]")
    private List<Long> orderId;

    @Null(message = "请勿传递[orderNums]")
    private List<String> orderNums;
}
