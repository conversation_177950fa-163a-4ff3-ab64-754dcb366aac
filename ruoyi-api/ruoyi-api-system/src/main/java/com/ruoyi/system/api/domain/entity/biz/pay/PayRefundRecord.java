package com.ruoyi.system.api.domain.entity.biz.pay;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付退款记录对象 pay_refund_record
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@ApiModel(value = "支付退款记录对象 pay_refund_record")
@TableName("pay_refund_record")
@Data
public class PayRefundRecord extends BaseEntity
{

    private static final long serialVersionUID = -7409436830320521752L;
    /** 主键 */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /** 订单id */
    @ApiModelProperty(value = "订单id")
    @Excel(name = "订单id")
    private Long orderId;

    /** 申请人id */
    @ApiModelProperty(value = "申请人id")
    @Excel(name = "申请人id")
    private Long applyId;

    /** 申请人名称 */
    @ApiModelProperty(value = "申请人名称")
    @Excel(name = "申请人名称")
    private String applyName;

    /** 申请时间 */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /** 退款类型（1:补偿、2:取消订单、3:取消选配） */
    @ApiModelProperty(value = "退款类型",notes = "1:补偿、2:取消订单、3:取消选配")
    @Excel(name = "退款类型", readConverterExp = "1:补偿、2:取消订单、3:取消选配")
    private String refundType;

    /** 申请金额 */
    @ApiModelProperty(value = "申请金额")
    @Excel(name = "申请金额")
    private BigDecimal applyAmount;

    /** 申请原因 */
    @ApiModelProperty(value = "申请原因")
    @Excel(name = "申请原因")
    private String applyCause;

    /** 退款状态（1:已退款、2:待审核、3:已取消） */
    @ApiModelProperty(value = "退款状态",notes = "1:已退款、2:待审核、3:已取消")
    @Excel(name = "退款状态", readConverterExp = "1:已退款、2:待审核、3:已取消")
    private String refundStatus;

}
