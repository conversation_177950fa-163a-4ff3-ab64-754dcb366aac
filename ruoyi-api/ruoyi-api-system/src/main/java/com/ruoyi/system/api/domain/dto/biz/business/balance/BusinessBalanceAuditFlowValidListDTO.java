package com.ruoyi.system.api.domain.dto.biz.business.balance;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 余额提现审核表
 *
 * <AUTHOR>
 * @TableName business_balance_audit_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceAuditFlowValidListDTO implements Serializable {

    private static final long serialVersionUID = 2294834581858276229L;

    @ApiModelProperty("审核状态（0:待处理,1:已提现,2.已取消）")
    private Integer auditStatus;

    @ApiModelProperty("商家id列表")
    private List<Long> businessIds;

}
