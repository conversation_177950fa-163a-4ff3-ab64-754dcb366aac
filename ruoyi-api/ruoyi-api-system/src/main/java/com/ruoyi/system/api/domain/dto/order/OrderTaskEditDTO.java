package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderTaskEditDTO implements Serializable {

    private static final long serialVersionUID = -8500831659292288478L;
    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号", required = true)
    @NotBlank(message = "[工单编号]不能为空")
    private String taskNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 32, message = "[备注]长度不能超过32个字符")
    private String remark;
}
