package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/16 17:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MyPreselectEndMatchListDTO extends OrderPoolListDTO {
    private static final long serialVersionUID = -3849936952553608535L;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    private Integer carryType;

    /**
     * 是否是我的订单（true:是,false:不是）
     */
    @ApiModelProperty(value = "是否是我的订单（true:是,false:不是）")
    private Boolean isMyOrder;

    /**
     * 订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）
     */
    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer orderStatus;

    /**
     * 拍摄模特ID
     */
    @ApiModelProperty(value = "拍摄模特ID")
    private Collection<Long> shootModelIds;

    /**
     * 添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发,5:排单推荐,6:凑单推荐）
     */
    @ApiModelProperty(value = "来源（1:意向模特,2:模特自选,3:运营添加,4:客服分发,5:排单推荐,6:凑单推荐）")
    private List<Integer> addTypes;

    /**
     * 匹配结果（1:匹配成功,2:匹配失败）
     */
    @ApiModelProperty(value = "匹配结果（1:匹配成功,2:匹配失败）")
    private Integer matchingResults;
}
