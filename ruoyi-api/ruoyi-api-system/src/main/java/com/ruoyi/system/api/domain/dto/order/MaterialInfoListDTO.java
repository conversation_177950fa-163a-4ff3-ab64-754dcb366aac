package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/20 15:38
 */
@Data
public class MaterialInfoListDTO implements Serializable {
    private static final long serialVersionUID = 3400607068131636218L;

    /**
     * 状态（1：待下载，2：待剪辑，3：待反馈，4：需确认，5：已关闭）
     */
    @ApiModelProperty(value = "状态（1：待下载，2：待剪辑，3：待反馈，4：需确认，5：已关闭）")
    private Integer status;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 拍摄模特ID
     */
    @ApiModelProperty(value = "拍摄模特ID")
    private List<Long> shootModelIds = new ArrayList<>();

    /**
     * 素材领取编码
     */
    @ApiModelProperty(value = "素材领取编码")
    private String getCode;

    /**
     * 领取人ID
     */
    @ApiModelProperty(value = "领取人ID")
    private List<Long> getByIds;

    /**
     * 领取状态
     */
    @ApiModelProperty(value = "领取状态")
    private Integer getStatus;

    /**
     * 领取时间开始
     */
    @ApiModelProperty(value = "领取时间开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date getTimeBegin;

    /**
     * 领取时间结束
     */
    @ApiModelProperty(value = "领取时间结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date getTimeEnd;

    /**
     * 反馈时间开始
     */
    @ApiModelProperty(value = "反馈时间开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date feedbackTimeBegin;

    /**
     * 反馈时间结束
     */
    @ApiModelProperty(value = "反馈时间结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date feedbackTimeEnd;

    /**
     * 剪辑人ID
     */
    @ApiModelProperty(value = "剪辑人ID")
    private List<Long> editByIds;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）")
    private Integer modelType;

    /**
     * 根据关键字获取到的中文部/英文部客服ID
     */
    private Collection<Long> backUserIds;

    /**
     * 内部查询字段-模特反馈素材详情ID
     */
    private Collection<Long> ids;

    @ApiModelProperty(value = "创建时间排序")
    private String createTimeSort;
}
