package com.ruoyi.system.api.domain.dto.biz.business.balance;

import com.ruoyi.common.core.enums.PrepayPayTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :修改订单支付状态dto
 * @create :2025-04-11 16:41
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PrepayUpdatePayStatusDTO implements Serializable {
    private static final long serialVersionUID = 4161099765252299663L;

    @ApiModelProperty(value = "预付单号")
    @NotBlank(message = "[预付单号]不能为空")
    private String prepayNum;

    @ApiModelProperty("支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账，7-全币种,99-其他)")
    @NotNull(message = "[支付方式]不能为空")
    @EnumValid(enumClass = PrepayPayTypeEnum.class, message = "[支付方式]输入错误")
    private Integer payType;

    @ApiModelProperty(value = "支付金额")
    @NotNull(message = "[支付金额]不能为空")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "是否开启真实支付")
    @NotBlank(message = "[是否开启真实支付]不能为空")
    private String debuggerEnable;

    @ApiModelProperty("支付时间")
    @NotNull(message="[支付时间]不能为空")
    private Date payTime;

}
