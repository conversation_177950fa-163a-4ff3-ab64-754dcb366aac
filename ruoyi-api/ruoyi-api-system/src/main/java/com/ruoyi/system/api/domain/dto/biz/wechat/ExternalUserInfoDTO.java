package com.ruoyi.system.api.domain.dto.biz.wechat;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 外部联系人信息dto
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExternalUserInfoDTO implements Serializable {
    private static final long serialVersionUID = 7488021588713807655L;
    private Integer errcode;
    private String errmsg;
    private ExternalContactInfoDTO externalContact;
    private List<FollowUser> follow_user;

    @Data
    public class FollowUser {
        /**
         * 添加了此外部联系人的企业成员userid
         */
        private String userid;
        /**
         * 该成员对此外部联系人的备注
         */
        private String remark;
        /**
         * 该成员对此外部联系人的描述
         */
        private String description;
        /**
         * 该成员添加此外部联系人的时间
         */
        private long createtime;
        /**
         * 该成员对此客户备注的企业名称
         */
        private String remarkCorpName;
        /**
         * 该成员对此客户备注的手机号码
         */
        private String[] remarkMobiles;
        /**
         * 该成员添加此客户的来源
         */
        private String addWay;
        /**
         * 发起添加的userid，如果成员主动添加，为成员的userid；如果是客户主动添加，则为客户的外部联系人userid；如果是内部成员共享/管理员分配，则为对应的成员/管理员userid
         */
        private String operUserid;
        /**
         * 企业自定义的state参数，用于区分客户具体是通过哪个「联系我」添加，由企业通过创建「联系我」方式指定
         */
        private String state;
    }
}

