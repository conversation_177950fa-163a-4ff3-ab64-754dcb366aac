package com.ruoyi.system.api.domain.dto.biz.page;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
* 页面配置表
* <AUTHOR>
 * @TableName page_config
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageConfigQueryListDTO implements Serializable {

    private static final long serialVersionUID = -7053268823004100831L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("页面名称")
    private String name;

    @ApiModelProperty("1-首页，2-精选案例，3-其他")
    private Integer type;
}
