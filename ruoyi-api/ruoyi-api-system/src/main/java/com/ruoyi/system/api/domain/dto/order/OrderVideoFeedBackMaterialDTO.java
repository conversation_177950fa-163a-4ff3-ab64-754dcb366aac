package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;

/**
 * 订单反馈表(模特)
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoFeedBackMaterialDTO implements Serializable {
    private static final long serialVersionUID = 666103695554697617L;

    @ApiModelProperty("视频订单id列表")
    private Collection<Long> videoIds;

    @ApiModelProperty("文件下载状态 (0-否， 1-是)")
    private Integer downloadStatus;
}
