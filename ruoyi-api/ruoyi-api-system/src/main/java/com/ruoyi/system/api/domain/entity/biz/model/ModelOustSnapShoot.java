package com.ruoyi.system.api.domain.entity.biz.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2025-05-13 17:34:48
 */
@Data
@TableName("model_oust_snap_shoot")
public class ModelOustSnapShoot implements Serializable {
    private static final long serialVersionUID = 6607362489005317204L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模特ID（FK：model.id）
     */
    @ApiModelProperty(value = "模特ID（FK：model.id）")
    private Long modelId;

    /**
     * 合作深度(0:一般模特,1:优质模特,2:中等模特)
     */
    @ApiModelProperty(value = "合作深度(0:一般模特,1:优质模特,2:中等模特)")
    private Integer cooperation;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 模特对象JSON
     */
    @ApiModelProperty(value = "模特对象JSON")
    private String extraData;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
