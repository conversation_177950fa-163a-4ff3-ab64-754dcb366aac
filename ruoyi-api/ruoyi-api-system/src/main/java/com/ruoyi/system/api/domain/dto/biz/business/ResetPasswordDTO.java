package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 09:12
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResetPasswordDTO implements Serializable {
    private static final long serialVersionUID = -2887906943823309540L;

    @ApiModelProperty("账号")
    @NotNull(message="[账号]不能为空")
    private String account;

    @ApiModelProperty("密码")
    @NotBlank(message="[密码]不能为空")
    @Size(max= 20, message="[密码]编码长度不能超过20")
    @Length(max= 20, message="[密码]编码长度不能超过20")
    private String password;


    @ApiModelProperty("ticket")
    private String ticket;

}
