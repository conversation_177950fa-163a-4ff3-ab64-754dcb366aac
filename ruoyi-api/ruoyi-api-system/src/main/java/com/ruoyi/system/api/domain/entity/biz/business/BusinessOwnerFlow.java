package com.ruoyi.system.api.domain.entity.biz.business;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 商家主账号换绑记录
 *
 * <AUTHOR>
 * @TableName business_owner_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessOwnerFlow implements Serializable {

    private static final long serialVersionUID = 7531029173588562477L;

    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[商家ID(business.id)]不能为空")
    @ApiModelProperty("商家ID(business.id)")
    private Long businessId;

    @NotNull(message = "[原账号ID(business_account.account)]不能为空")
    @ApiModelProperty("原账号ID(business_account.account)")
    private Long originAccountId;

    @NotBlank(message = "[原账号微信名(biz_user.nick_name)]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("原账号微信名(biz_user.nick_name)")
    @Length(max = 32, message = "编码长度不能超过32")
    private String originAccountNickName;

    @NotBlank(message = "[原账号名称(biz_user.name)]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("原账号名称(biz_user.name)")
    @Length(max = 32, message = "编码长度不能超过32")
    private String originAccountName;

    @NotNull(message = "[更换账号ID(business_account.account)]不能为空")
    @ApiModelProperty("更换账号ID(business_account.account)")
    private Long accountId;

    @NotBlank(message = "[更换账号微信名(biz_user.nick_name)]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("更换账号微信名(biz_user.nick_name)")
    @Length(max = 32, message = "编码长度不能超过32")
    private String accountNickName;

    @NotBlank(message = "[更换账号名称(biz_user.name)]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("更换账号名称(biz_user.name)")
    @Length(max = 32, message = "编码长度不能超过32")
    private String accountName;

    @NotNull(message = "[操作人（sys_user.user_id）]不能为空")
    @ApiModelProperty("操作人（sys_user.user_id）")
    private Long createById;

    @NotBlank(message = "[操作人名称（sys_user.user_name）]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("操作人名称（sys_user.user_name）")
    @Length(max = 30, message = "编码长度不能超过30")
    private String createBy;

    @NotNull(message = "[操作人时间]不能为空")
    @ApiModelProperty("操作人时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @NotNull(message = "[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
