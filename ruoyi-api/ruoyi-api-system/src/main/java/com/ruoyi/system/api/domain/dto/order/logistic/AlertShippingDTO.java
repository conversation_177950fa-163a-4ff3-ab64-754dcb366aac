package com.ruoyi.system.api.domain.dto.order.logistic;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 提醒发货
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AlertShippingDTO implements Serializable {

    private static final long serialVersionUID = -7228672116695046317L;
    @ApiModelProperty("物流跟进Id")
    @NotNull(message = "物流跟进Id不能为空")
    @Size(min = 1, message = "[物流跟进Id]至少需要一条数据")
    private List<Long> ids;

    @ApiModelProperty("说明")
    private String remark;

    @ApiModelProperty("图片地址")
    private List<String> resourceIds;
}
