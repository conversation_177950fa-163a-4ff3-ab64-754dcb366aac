package com.ruoyi.system.api.domain.dto.biz.business.balance;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.PayTypeEnum;
import com.ruoyi.common.core.enums.PrepayPayTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 提交凭证DTO
 *
 * <AUTHOR>
 * @date 2024/6/15 10:12
 */
@Data
public class OnlineRechargeSubmitCredentialDTO implements Serializable {

    private static final long serialVersionUID = -7507567367555456221L;

    @ApiModelProperty(value = "订单号")
    @NotEmpty(message = "[订单号]不能为空")
    private String orderNum;

    @Size(max = 5, message = "[支付凭证]最多上传5张")
    @Size(min = 1, message = "至少需要有一条支付凭证数据")
    @NotNull(message = "[支付凭证]不能为空")
    private List<String> resourceIds;

    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额）")
    @NotNull(message = "[支付方式]不能为空")
    @EnumValid(enumClass = PrepayPayTypeEnum.class, message = "[支付方式]输入错误")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    @EnumValid(enumClass = PayTypeEnum.PayTypeDetailEnum.class, message = "[支付方式明细]输入错误")
    private Integer payTypeDetail;

    @ApiModelProperty(value = "付款账号名称", required = true)
    @Size(max = 100, message = "[付款账号名称]不能超过100个字符")
    private String payAccount;

    @ApiModelProperty("是否是代付")
    private Integer isAnother;

    @AssertTrue(message = "全币种支付，[付款账号名称]不能为空~")
    private boolean isCheckPayAccount() {
        if (PrepayPayTypeEnum.FULL_CURRENCY.getCode().equals(payType)) {
            return StrUtil.isNotBlank(payAccount);
        }
        return true;
    }


    @AssertTrue(message = "选择全币种支付时，[支付方式明细]不能为空")
    private boolean isPayTypeDetail() {
        if (PayTypeEnum.FULL_CURRENCY.getCode().equals(payType) || PayTypeEnum.FULL_CURRENCY_BALANCE.getCode().equals(payType)) {
            return ObjectUtil.isNotNull(payTypeDetail);
        } else {
            return ObjectUtil.isNull(payTypeDetail);
        }
    }

}
