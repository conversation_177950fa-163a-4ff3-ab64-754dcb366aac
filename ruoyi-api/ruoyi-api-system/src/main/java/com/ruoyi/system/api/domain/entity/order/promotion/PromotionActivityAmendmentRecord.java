package com.ruoyi.system.api.domain.entity.order.promotion;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2025-05-21 14:33:45
 */
@Data
@TableName("promotion_activity_amendment_record")
public class PromotionActivityAmendmentRecord implements Serializable {
    private static final long serialVersionUID = 2251721447053423930L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 优惠扣减类型（1：直减，2：折扣）
     */
    @ApiModelProperty(value = "优惠扣减类型（1：直减，2：折扣）")
    private Integer type;

    /**
     * 优惠数值
     */
    @ApiModelProperty(value = "优惠数值")
    private BigDecimal amount;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty("结算佣金类型（1-固定金额，2-固定比例）")
    private Integer settleDiscountType;

    @ApiModelProperty("佣金比例、固定金额")
    private BigDecimal settleDiscount;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createBy;


    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createById;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人姓名
     */
    @ApiModelProperty(value = "更新人姓名")
    private String updateBy;


    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long updateById;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
