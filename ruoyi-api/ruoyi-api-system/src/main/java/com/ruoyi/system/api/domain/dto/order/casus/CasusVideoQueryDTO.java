package com.ruoyi.system.api.domain.dto.order.casus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
* 案例视频表
* <AUTHOR>
 * @TableName case_video
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CasusVideoQueryDTO implements Serializable {

    @ApiModelProperty("Id")
    private Long id;

    private static final long serialVersionUID = -6674922225769613902L;
    @ApiModelProperty("视频名称")
    private String name;

    @ApiModelProperty("平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    @ApiModelProperty("ID列表")
    private List<Long> ids;

}
