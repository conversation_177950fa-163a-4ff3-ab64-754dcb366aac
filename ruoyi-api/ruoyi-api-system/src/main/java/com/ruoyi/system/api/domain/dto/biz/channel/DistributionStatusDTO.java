package com.ruoyi.system.api.domain.dto.biz.channel;

import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-27 17:46
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DistributionStatusDTO implements Serializable {
    private static final long serialVersionUID = 5895052630126047284L;

    @NotNull(message="[渠道id]不能为空")
    @ApiModelProperty("渠道id")
    private Long id;

    @NotNull(message="[渠道状态]不能为空")
    @ApiModelProperty("渠道状态")
    @EnumValid(enumClass = StatusEnum.class, message = "[分销渠道状态]输入错误")
    private Integer status;
}
