package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-04-01 09:51
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LogisticCountDTO implements Serializable {
    private static final long serialVersionUID = -6381572738547407084L;

    @ApiModelProperty(value = "视频ID列表")
    private Set<Long> videoIds;

    @ApiModelProperty(value = "回滚ID列表")
    private Set<Long> rollbackIds;

    @ApiModelProperty(value = "视频状态列表")
    private List<Integer> videoStatus;

    @ApiModelProperty(value = "用户ID")
    private Long userId;
}
