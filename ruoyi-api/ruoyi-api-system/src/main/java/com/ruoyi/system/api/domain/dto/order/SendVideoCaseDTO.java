package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 发起反馈DTO入参
 *
 * <AUTHOR>
 * @date 2024/6/12 18:17
 */
@Data
public class SendVideoCaseDTO implements Serializable {

    private static final long serialVersionUID = 6047029362765962680L;
    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id", required = true)
    @NotNull(message = "[视频id]不能为空")
    private Long videoId;

    /**
     * 发送内容
     */
    @ApiModelProperty(value = "发送内容", required = true)
    @NotBlank(message = "[发送内容]不能为空")
    @Size(max = 300, message = "[发送内容]不能超过300个字符")
    private String sendContent;
}
