package com.ruoyi.system.api.domain.dto.order.pay;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/21 19:46
 */
@Data
public class PayInfoVideoExportDTO implements Serializable {
    private static final long serialVersionUID = -3297615492724673150L;

    /**
     * 产品名称
     */
    private String productChinese;

    /**
     * 下单用户
     */
    private String orderUserName;

    /**
     * 视频佣金
     */
    private String videoPrice;

    /**
     * 照片佣金
     */
    private String picPrice;

    /**
     * 佣金代缴税费
     */
    private String commissionPaysTaxes;

    /**
     * paypal代付手续费
     */
    private String exchangePrice;

    /**
     * 蜗牛服务费
     */
    private String servicePrice;

    /**
     * 实时百度汇率
     */
    private String currentExchangeRate;

    /**
     * 小计USD
     */
    private String subtotalUSD;

    /**
     * 小计CNY
     */
    private String subtotalCNY;
}
