package com.ruoyi.system.api.domain.dto.order.casus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :查询案例分组视频列表
 * @create :2024-07-10 15:31
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class GroupsVideoListDTO implements Serializable {
    private static final long serialVersionUID = -8423966946644452223L;

    @NotNull(message="[分组ID]不能为空")
    @ApiModelProperty("分组ID")
    private Long groupId;

    @ApiModelProperty("视频名称")
    private String videoName;

    @ApiModelProperty("视频id")
    private Long videoId;
}
