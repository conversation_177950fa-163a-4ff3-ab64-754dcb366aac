package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 13:48
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessRemarkDTO implements Serializable {

    private static final long serialVersionUID = 3146933270606334354L;
    @NotNull(message="[账号]不能为空")
    @ApiModelProperty("账号")
    private String ownerAccount;

    @NotNull(message="[备注]不能为空")
    @Size(max= 1000,message="备注长度不能超过1000")
    @ApiModelProperty("备注")
    private String remark;
}
