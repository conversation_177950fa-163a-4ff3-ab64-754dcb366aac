package com.ruoyi.system.api.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collection;

/**
 * 商家信息对象DTO
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@ApiModel(value = "商家信息对象DTO")
@Data
public class MerchantListDTO implements Serializable {
    private static final long serialVersionUID = 3340039384118561396L;
    /**
     * 商家ID
     */
    @ApiModelProperty(value = "商家ID")
    private Collection<Long> merchantId;

    /**
     * 商家账号
     */
    @ApiModelProperty(value = "商家账号")
    private String merchantName;
}
