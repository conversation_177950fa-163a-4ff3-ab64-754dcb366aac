package com.ruoyi.system.api.domain.dto.order.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-10-24 16:43
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WeChatPayDTO implements Serializable {
    private static final long serialVersionUID = 3537128270653739520L;

    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "商户订单号")
    private String outTradeNo;

    @ApiModelProperty(value = "appId")
    private String appId;

}
