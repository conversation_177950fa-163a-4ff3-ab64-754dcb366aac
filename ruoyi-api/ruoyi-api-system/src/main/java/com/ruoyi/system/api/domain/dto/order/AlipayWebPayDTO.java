package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 支付宝电脑网站支付
 *
 * <AUTHOR>
 * @date 2024/10/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlipayWebPayDTO implements Serializable {
    private static final long serialVersionUID = -2975065751428676321L;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", required = true)
    @NotBlank(message = "[订单号]不能为空")
    private String orderNum;

    /**
     * 种草码
     */
    @ApiModelProperty("种草码")
    @Size(min = 1, max = 10, message = "[种草码]长度应在1~10个字符之间")
    private String seedCode;
}
