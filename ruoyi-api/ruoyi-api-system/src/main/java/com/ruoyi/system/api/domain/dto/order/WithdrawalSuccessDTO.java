package com.ruoyi.system.api.domain.dto.order;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/8 19:13
 */
@Data
public class WithdrawalSuccessDTO implements Serializable {
    private static final long serialVersionUID = -131161040722857986L;

    /**
     * 视频编码
     */
    private String videoCode;

    /**
     * 钱包充值单号
     */
    private String prepayNum;

    /**
     * 退款类型
     */
    private Integer refundType;

    /**
     * 提现时间
     */
    private Date withdrawDepositTime;

    /**
     * 提现金额
     */
    private BigDecimal withdrawDepositAmount;





    /**
     * 订单号
     */
    private String orderNum;

    /**
     * 视频订单ID
     */
    private Long videoId;
}
