package com.ruoyi.system.api.domain.dto.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/8 20:36
 */
@Data
public class ModelDataTableAddRemarkDTO implements Serializable {
    private static final long serialVersionUID = 2707985320422924824L;

    /**
     * 模特ID
     */
    @ApiModelProperty(value = "模特ID", required = true)
    @NotNull(message = "[模特ID]不能为空")
    private Long modelId;

    /**
     * 备注内容
     */
    @ApiModelProperty(value = "备注内容", required = true)
    @NotBlank(message = "[备注内容]不能为空")
    private String remark;
}
