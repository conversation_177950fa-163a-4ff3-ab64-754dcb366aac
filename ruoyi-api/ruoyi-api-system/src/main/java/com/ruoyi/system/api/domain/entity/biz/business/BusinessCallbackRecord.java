package com.ruoyi.system.api.domain.entity.biz.business;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:17
 */
@Data
@TableName("business_callback_record")
public class BusinessCallbackRecord implements Serializable {
    private static final long serialVersionUID = 9074877766256757714L;


    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID (FK:business.id)
     */
    @ApiModelProperty("商家ID")
    private Long businessId;

    /**
     * 回访ID（FK:business_callback.id）
     */
    @ApiModelProperty("回访ID")
    private Long callbackId;

    /**
     * 回访账号BizUserID
     */
    @ApiModelProperty("回访账号BizUserID")
    private Long accountBizUserId;

    /**
     * 回访账号ID
     */
    @ApiModelProperty("回访账号ID")
    private Long accountId;

    /**
     * 回访账号是否是主账号
     */
    @ApiModelProperty("回访账号是否是主账号")
    private Integer accountIsOwnerAccount;

    /**
     * 回访账号员工名称
     */
    @ApiModelProperty("回访账号员工名称")
    private String accountName;

    /**
     * 回访账号员工微信名称
     */
    @ApiModelProperty("回访账号员工微信名称")
    private String accountNickName;

    /**
     * 反馈类型
     */
    @ApiModelProperty("反馈类型")
    private String feedbackType;

    /**
     * 回访内容
     */
    @ApiModelProperty("回访内容")
    private String callbackContent;

    /**
     * 回访图片（FK:biz.resource.id 多个,隔开）
     */
    @ApiModelProperty("回访图片")
    private String resourceId;

    /**
     * 记录时间
     */
    @ApiModelProperty("记录时间")
    private Date writeTime;

    /**
     * 记录人
     */
    @ApiModelProperty("记录人")
    private String writeBy;

    /**
     * 记录人ID
     */
    @ApiModelProperty("记录人ID")
    private Long writeById;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
