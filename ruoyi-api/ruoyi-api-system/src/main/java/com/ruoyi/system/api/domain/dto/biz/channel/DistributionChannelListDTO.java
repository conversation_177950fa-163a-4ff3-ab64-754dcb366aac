package com.ruoyi.system.api.domain.dto.biz.channel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.BizUserAccountTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-25 10:57
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DistributionChannelListDTO implements Serializable {
    private static final long serialVersionUID = -5195419728118375382L;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "是否结算：0-未结算，1-已结算")
    private Integer isSettle;

    @ApiModelProperty(value = "排序类型：1-邀请数，2-注册数，3-会员成交数，4-结算比例，5-订单成交额，6-待结算金额，7-已结算金额,8-实际结算金额,9-单个渠道的会员订单成交金额")
    private Integer orderByType;

    @ApiModelProperty(value = "渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @ApiModelProperty(value = "是否正序排序")
    private Integer isAsc;

    @ApiModelProperty(value = "创建人ID")
    private Long createId;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty("账号状态：0-普通账号，1-主账号，2-子账号")
    @EnumValid(enumClass = BizUserAccountTypeEnum.class, message = "账号状态错误")
    private Integer accountType;

    @ApiModelProperty("渠道ID列表")
    private List<Long> channelIds;

    @ApiModelProperty("用户ID列表")
    private List<Long> bizUserIds;

    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;

    @ApiModelProperty("分销状态（0=正常,1=禁用）")
    private Integer channelStatus;


}
