package com.ruoyi.system.api.domain.dto.order.promotion;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/21 16:11
 */
@Data
public class UpdatePromotionActivityDTO implements Serializable {
    private static final long serialVersionUID = 7879205571430029976L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "[主键]不能为空")
    private Long id;

    /**
     * 优惠数值
     */
    @ApiModelProperty(value = "优惠数值", required = true)
    @NotNull(message = "[优惠数值]不能为空")
    @Digits(integer = 2, fraction = 2)
    @DecimalMin(value = "0.01")
    private BigDecimal amount;

    /**
     * 开始时间
     */
    @NotNull(message = "[开始时间]不能为空")
    @ApiModelProperty(value = "开始时间", required = true)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "[结束时间]不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date endTime;

    @AssertTrue(message = "[开始时间]不能大于[结束时间]")
    private boolean isValidTime() {
        return DateUtil.compare(startTime, endTime) < 0;
    }
}
