package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :账号订单数据dto
 * @create :2025-03-24 14:18
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountOrderListDTO implements Serializable {
    private static final long serialVersionUID = -4456953377611034895L;

    @ApiModelProperty(value = "商家Id")
    private Long merchantId;

    @ApiModelProperty(value = "登录账号")
    private String createOrderUserAccount;
}
