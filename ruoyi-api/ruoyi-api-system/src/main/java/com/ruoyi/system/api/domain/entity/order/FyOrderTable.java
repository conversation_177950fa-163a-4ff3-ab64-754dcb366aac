package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 富友订单表
 *
 * <AUTHOR>
 * @TableName fy_order_table
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FyOrderTable implements Serializable {

    private static final long serialVersionUID = 3323759981196402382L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotBlank(message = "[商户订单号]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("商户订单号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String mchntOrderNo;

    @NotBlank(message = "[内部订单号]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("内部订单号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String orderNumber;

    @NotBlank(message = "[二维码]不能为空")
    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("二维码")
    @Length(max = 100, message = "编码长度不能超过100")
    private String qrcode;

    @NotNull(message = "[平台类型（1-微信，2-支付宝，3-云闪付/银联，4-数字人民币，5-未知支付方式）]不能为空")
    @ApiModelProperty("平台类型（1-微信，2-支付宝，3-云闪付/银联，4-数字人民币，5-未知支付方式）")
    private Integer platform;

    @NotNull(message = "[订单金额]不能为空")
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @NotNull(message = "[是否有效（1-有效， 0-无效）]不能为空")
    @ApiModelProperty("是否有效（1-有效， 0-无效）")
    private Integer status;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
