package com.ruoyi.system.api.domain.dto.biz.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 商家表
 * <AUTHOR>
 * @TableName business
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessDTO implements Serializable {

    private static final long serialVersionUID = 2793617183293391458L;
    @NotNull(message="[公司ID]不能为空")
    @ApiModelProperty("公司ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("商家账号或会员账号")
    private String searchName;
    /**
     * 商家主账号（FK：business_account.account）
     */
    @NotBlank(message="[商家主账号（FK：business_account.account）]不能为空")
    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;
    /**
     * 商家名称
     */
    @NotBlank(message="[商家名称]不能为空")
    @Size(max= 50,message="[商家名称]长度不能超过50")
    @ApiModelProperty("商家名称")
    private String name;

    @ApiModelProperty("规模")
    private Integer scale;

    /**
     * 是否为代理(0:否,1:是)
     */
    @NotNull(message="[是否为代理(0:否,1:是)]不能为空")
    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;
    /**
     * 是否可见手机号(0:否,1:是)
     */
    @NotNull(message="是否可见手机号不能为空")
    @ApiModelProperty("是否可见手机号(0:否,1:是)")
    private Integer phoneVisible;
    /**
     * 账号状态（0正常 1禁用）
     */
    @NotNull(message="[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;

    @ApiModelProperty("备注")
    private String remark;
    /**
     * 客户类型 （0-一般客户 1-重要客户）
     */
    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;

    @ApiModelProperty("商家标识 （0-新客 1-老客 2-'-'）")
    private Integer businessIdentifier;
    /**
     * 帐号余额
     */
    @ApiModelProperty("帐号余额")
    private BigDecimal balance;
    /**
     * 对接客服  FK：sys_user.user_id
     */
    @NotNull(message="[对接客服  FK：sys_user.user_id]不能为空")
    @ApiModelProperty("对接客服  FK：sys_user.user_id")
    private Long waiterId;
    /**
     * 抬头类型  0-企业
     */
    @ApiModelProperty("抬头类型  0-企业")
    private Integer invoiceTitleType;
    /**
     * 发票抬头
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("发票抬头")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceTitle;
    /**
     * 税号
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("税号")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceDutyParagraph;
    /**
     * 发票内容
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("发票内容")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceContent;
    /**
     * 会员编码
     */
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("会员编码")
    @Length(max= 10,message="编码长度不能超过10")
    private String memberCode;
    /**
     * 会员类型: 0-非会员，1-会员
     */
    @NotNull(message="[会员类型: 0-非会员，1-会员]不能为空")
    @ApiModelProperty("会员类型: 0-非会员，1-会员")
    private Integer memberType;
    /**
     * 会员状态：0-非会员1-正常，2-即将过期，3-已过期
     */
    @NotNull(message="[会员状态：0-非会员1-正常，2-即将过期，3-已过期]不能为空")
    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;
    /**
     * 会员套餐名称
     */
    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberPackageType;
    /**
     * 会员首次购买时间
     */
    @ApiModelProperty("会员首次购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberFirstTime;

    @ApiModelProperty("首次购买套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberFirstType;
    /**
     * 会员最近购买时间
     */
    @ApiModelProperty("会员最近购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberLastTime;
    /**
     * 会员有效期
     */
    @ApiModelProperty("会员有效期")
    private Date memberValidity;

    @ApiModelProperty("购买会员次数")
    private Long rechargeCount;

    /**
     * 创建人id FK: sys_user.user_id
     */
    @NotNull(message="[创建人id FK: sys_user.user_id]不能为空")
    @ApiModelProperty("创建人id FK: sys_user.user_id")
    private Long createUserId;
    /**
     * 创建人名称： FK: sys_user.user_name
     */
    @NotBlank(message="[创建人名称： FK: sys_user.user_name]不能为空")
    @Size(max= 30,message="编码长度不能超过30")
    @ApiModelProperty("创建人名称： FK: sys_user.user_name")
    @Length(max= 30,message="编码长度不能超过30")
    private String createUserName;
    /**
     * 创建时间
     */
    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 修改人id	FK: sys_user.user_id
     */
    @NotNull(message="[修改人id]不能为空")
    @ApiModelProperty("修改人id	FK: sys_user.user_id")
    private Long updateUserId;
    /**
     * 修改人名称  FK: sys_user.user_name
     */
    @NotBlank(message="[修改人名称  FK: sys_user.user_name]不能为空")
    @Size(max= 30,message="编码长度不能超过30")
    @ApiModelProperty("修改人名称  FK: sys_user.user_name")
    @Length(max= 30,message="编码长度不能超过30")
    private String updateUserName;

    @ApiModelProperty("近30天是否存在订单：1-是，0-否")
    private Integer isExistRecentOrder;

    @ApiModelProperty("是否分配客服：1-是，0-否")
    private Integer isAssignWaiter;
    /**
     * 修改时间
     */
    @NotNull(message="[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "注册时间-begin")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date businessCreateBegin;

    @ApiModelProperty(value = "注册时间-end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date businessCreateEnd;

    @ApiModelProperty(value = "会员有效期-begin")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberValidityBegin;

    @ApiModelProperty(value = "会员有效期-end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberValidityEnd;

    @ApiModelProperty(value = "商家编码列表")
    private List<Long> accounts;

    @ApiModelProperty(value = "商家id列表")
    private List<Long> businessIds;

    @ApiModelProperty(value = "会员状态列表")
    private List<Integer> memberStatusList;

    @ApiModelProperty(value = "排序类型：1-订单总数")
    private Integer orderByType;

    @ApiModelProperty(value = "是否正序排序")
    private Integer isAsc;

    @ApiModelProperty(value = "订单总数量map")
    private Map<Long, Integer> orderCountMap;

    @ApiModelProperty(value = "商务经理")
    private String connectUserName;
}