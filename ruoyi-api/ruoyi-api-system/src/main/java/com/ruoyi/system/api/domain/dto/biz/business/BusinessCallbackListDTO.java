package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessCallbackListDTO implements Serializable {
    private static final long serialVersionUID = -6590446201092942287L;

    /**
     * 回访ID
     */
    @ApiModelProperty("回访ID")
    private Long id;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keyword;

    /**
     * 回访事件
     */
    @ApiModelProperty("回访事件")
    private List<Integer> event;

    /**
     * 回访状态（1:待回访,2:回访中,3:已回访）
     */
    @ApiModelProperty("回访状态（1:待回访,2:回访中,3:已回访）")
    private Integer status;
}
