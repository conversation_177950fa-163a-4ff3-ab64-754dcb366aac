package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 拒绝模特素材DTO
 *
 * <AUTHOR>
 * @date 2024/6/15
 */
@Data
public class MaterialRejectDTO implements Serializable {

    private static final long serialVersionUID = 1104854985535099845L;
    @ApiModelProperty("素材id")
    @NotNull(message = "[素材id]不能为空")
    private Integer materialId;

    @ApiModelProperty("驳回理由")
    @NotBlank(message = "[驳回理由]不能为空")
    private String remark;

    @ApiModelProperty("驳回标题")
    @NotBlank(message = "[驳回标题]不能为空")
    @Size(max = 64, message = "[驳回标题]长度不能超过64个字符")
    private String title;
}
