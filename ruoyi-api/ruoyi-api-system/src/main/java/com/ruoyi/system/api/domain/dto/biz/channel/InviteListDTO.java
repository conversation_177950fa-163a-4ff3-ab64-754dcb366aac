package com.ruoyi.system.api.domain.dto.biz.channel;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-29 16:08
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InviteListDTO implements Serializable {
    private static final long serialVersionUID = -5417482523951574070L;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "渠道id")
    @NotNull(message = "渠道ID不能为空")
    private Long channelId;

    @ApiModelProperty(value = "渠道类型")
    private Integer channelType;

    @ApiModelProperty("会员类型：0=季度会员,1=年度会员,2=三年会员,3=非该渠道邀请")
    private Integer memberPackageType;

    @ApiModelProperty("购买会员开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN,timezone = "GMT+8")
    private Date memberStartTime;

    @ApiModelProperty("购买会员结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN,timezone = "GMT+8")
    private Date memberEndTime;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN,timezone = "GMT+8")
    private Date registerStartTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN,timezone = "GMT+8")
    private Date registerEndTime;
}
