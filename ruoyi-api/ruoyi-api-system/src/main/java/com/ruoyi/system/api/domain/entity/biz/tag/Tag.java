package com.ruoyi.system.api.domain.entity.biz.tag;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 标签对象 tag
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@ApiModel(value = "标签对象 tag")
@TableName("tag")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Tag implements Serializable {


    private static final long serialVersionUID = 4126649725614450165L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 父标签id
     */
    @ApiModelProperty(value = "父标签id", required = true)
    private Long parentId = 0L;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    @Excel(name = "标签名称")
    private String name;


    /**
     * 标签英文名称
     */
    @ApiModelProperty(value = "标签英文名称")
    @Excel(name = "标签英文名称")
    private String englishName;

    /**
     * 标签分类path
     */
    @ApiModelProperty(value = "标签分类path")
    @Excel(name = "标签分类path")
    private String path;

    /** 分类id */
    @ApiModelProperty(value = "分类id")
    @Excel(name = "分类id")
    private Long categoryId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @Excel(name = "排序")
    private Long sort;

    /**
     * 状态（0:禁用，1:启用）
     */
    @ApiModelProperty(value = "状态", notes = "0:禁用，1:启用")
    @Excel(name = "状态", readConverterExp = "0:禁用，1:启用")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private Long createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private Long updateBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
