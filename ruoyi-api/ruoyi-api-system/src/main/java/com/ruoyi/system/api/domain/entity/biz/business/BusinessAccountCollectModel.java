package com.ruoyi.system.api.domain.entity.biz.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 商家子账号收藏模特对象 business_account_collect_model
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@ApiModel(value = "商家子账号收藏模特对象 business_account_collect_model")
@TableName("business_account_collect_model")
@Data
public class BusinessAccountCollectModel implements Serializable
{

    private static final long serialVersionUID = -2126036395427982458L;
    /** 主键 */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /** 商家端用户id FK:biz_user.id */
    @ApiModelProperty(value = "商家端用户id")
    @Excel(name = "商家端用户id")
    private Long bizUserId;

    /** 模特id FK:model.id */
    @ApiModelProperty(value = "模特id")
    @Excel(name = "模特id")
    private Long modelId;

}
