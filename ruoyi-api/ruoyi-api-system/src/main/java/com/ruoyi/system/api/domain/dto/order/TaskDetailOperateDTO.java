package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11 16:31
 */
@Data
public class TaskDetailOperateDTO {

    /**
     * 主键 order_video_task_detail.id
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "[主键]不能为空")
    private Long id;

    /**
     * 备注
     */
    @ApiModelProperty(value = "理由", required = true)
    @Size(max = 1000, message = "[理由]长度不能超过1000字符")
    @NotBlank(message = "[理由]不能为空")
    private String remark;

    /**
     * 图片
     */
    @ApiModelProperty(value = "图片")
    @Size(max = 5, message = "[图片]最多上传5张")
    private List<String> issuePic;
}
