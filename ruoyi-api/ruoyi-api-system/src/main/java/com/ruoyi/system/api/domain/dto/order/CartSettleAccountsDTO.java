package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/12 10:04
 */
@Data
public class CartSettleAccountsDTO implements Serializable {
    private static final long serialVersionUID = 8210800648621234946L;
    @ApiModelProperty("购物车id")
    @NotNull(message = "[购物车id]不能为空")
    private Long id;

    @ApiModelProperty("是否重新下单(0:是,1:不是)")
    @NotNull(message = "[是否重新下单]不能为空")
    @EnumValid(enumClass = StatusEnum.class, message = "[是否重新下单]输入错误")
    private Integer repetition;
}
