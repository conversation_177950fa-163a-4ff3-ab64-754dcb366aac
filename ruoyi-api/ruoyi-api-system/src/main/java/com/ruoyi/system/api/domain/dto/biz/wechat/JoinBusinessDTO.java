package com.ruoyi.system.api.domain.dto.biz.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :加入商家
 * @create :2024-08-02 10:56
 **/
@Data
public class JoinBusinessDTO implements Serializable {
    private static final long serialVersionUID = 1078509318036610160L;

    @ApiModelProperty("员工名称")
    @NotBlank(message = "员工名称不能为空")
    @Size(max = 20, message = "[员工名称]长度不能超过20")
    @Size(min = 1, message = "[员工名称]至少需要一个字符")
    private String name;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("手机验证码")
    private String phoneCaptcha;

    @ApiModelProperty("微信授权code")
    @NotBlank(message = "微信授权code不能为空")
    String code;

    @ApiModelProperty("state参数")
    @NotBlank(message = "state参数不能为空")
    String state;
}
