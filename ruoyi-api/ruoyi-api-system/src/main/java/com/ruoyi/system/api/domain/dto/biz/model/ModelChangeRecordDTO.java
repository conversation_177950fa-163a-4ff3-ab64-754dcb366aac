package com.ruoyi.system.api.domain.dto.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/11 17:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModelChangeRecordDTO implements Serializable {
    private static final long serialVersionUID = 217653167922816499L;
    /**
     * 模特id
     */
    @ApiModelProperty("模特id")
    private Long modelId;

    /**
     * 操作类型（1:新增模特,2:修改模特信息,3:变更状态）
     */
    @ApiModelProperty("操作类型（1:新增模特,2:修改模特信息,3:变更状态）")
    private Integer operateType;

    /**
     * 操作详情
     */
    @ApiModelProperty("操作详情")
    private String operateDetail;

    /**
     * 操作说明
     */
    @ApiModelProperty("操作说明")
    private String operateExplain;
}
