package com.ruoyi.system.api.domain.dto.biz.model;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-9-5
 */
@Data
public class ModelTagDTO implements Serializable {
    private static final long serialVersionUID = 1253932592118626382L;

    /**
     * 标签id
     */
    @ApiModelProperty(value = "标签id")
    private Long id;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    private String name;

    @AssertTrue(message = "[标签id]和[标签名称]不能同时为空")
    private boolean isId() {
        if (ObjectUtil.isNull(id)) {
            return CharSequenceUtil.isNotBlank(name);
        } else {
            return ObjectUtil.isNull(name);
        }
    }
}
