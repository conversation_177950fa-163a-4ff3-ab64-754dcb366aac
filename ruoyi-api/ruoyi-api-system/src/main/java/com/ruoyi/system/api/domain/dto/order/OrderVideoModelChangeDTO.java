
package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/15 11:47
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoModelChangeDTO implements Serializable {
    private static final long serialVersionUID = -4994033202604490934L;

    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty(value = "视频id (FK:order_video.id)")
    private Long videoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id")
    private Long modelId;

    /**
     * 模特来源（1:意向模特,2:拍摄模特）
     */
    @ApiModelProperty(value = "模特来源")
    private Integer source;

    /**
     * 选定时间
     */
    @ApiModelProperty(value = "选定时间")
    private Date selectedTime;

    /**
     * 排单类型（1:排单,2:携带排单）
     */
    @ApiModelProperty(value = "排单类型（1:排单,2:携带排单）")
    private Integer scheduleType;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    private Integer carryType;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 超额说明
     */
    @ApiModelProperty(value = "超额说明")
    private String overstatement;
}
