package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.PositiveOrZero;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/23 17:24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayMemberInfoDTO implements Serializable {
    private static final long serialVersionUID = -6285956177841009343L;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号", required = true)
    @NotBlank(message = "[订单编号]不能为空")
    private String orderNum;

    /**
     * 是否对公
     */
    @ApiModelProperty(value = "是否对公（0:是,1:不是）", required = true)
    @NotBlank(message = "[是否是对公]不能为空")
    private Integer isPublic;

    /**
     * 使用的余额
     */
    @ApiModelProperty("使用的余额")
    @PositiveOrZero(message = "[使用的余额]必须大于等于0")
    private BigDecimal useBalance = BigDecimal.ZERO;

    /**
     * 种草码
     */
    @ApiModelProperty("种草码")
    @Size(min = 1, max = 10, message = "[种草码]长度应在1~10个字符之间")
    private String seedCode;
}
