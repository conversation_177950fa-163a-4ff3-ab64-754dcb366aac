package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 10:51
 */
@Data
public class ToBeInvoicedListDTO implements Serializable {
    private static final long serialVersionUID = -260756778309203681L;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废,5:待审核,6:已取消,7:已重开）
     */
    @ApiModelProperty(value = "开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废,5:待审核,6:已取消,7:已重开）")
    private List<Integer> status;

    /**
     * 订单类型（0-视频订单，1-会员订单）
     */
    @ApiModelProperty(value = "订单类型（0-视频订单，1-会员订单）")
    private Integer orderType;

    /**
     * 发票类型（1：增值税普通发票，2：形式发票）
     */
    @ApiModelProperty(value = "发票类型（1：增值税普通发票，2：形式发票）")
    private Integer invoiceType;

    /**
     * 来源（1：商家申请，2：红冲重开）
     */
    @ApiModelProperty(value = "来源（1：商家申请，2：红冲重开）")
    private Integer source;

    /**
     * 申请时间-开始
     */
    @ApiModelProperty(value = "申请时间-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTimeBegin;

    /**
     * 申请时间-结束
     */
    @ApiModelProperty(value = "申请时间-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTimeEnd;

    /**
     * 当前登录用户类型
     */
    private Integer currentUserType;

    /**
     * 当前登录商家ID
     */
    private Long currentBusinessId;

    /**
     * 通过关键字搜索的商家用户
     */
    private List<Long> keywordCompanyUserIds;
}
