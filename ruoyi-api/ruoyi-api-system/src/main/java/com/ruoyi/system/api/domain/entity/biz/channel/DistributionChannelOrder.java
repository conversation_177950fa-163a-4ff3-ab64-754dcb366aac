package com.ruoyi.system.api.domain.entity.biz.channel;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 分销渠道订单表
 *
 * <AUTHOR>
 * @TableName distribution_channel_order
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DistributionChannelOrder implements Serializable {

    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[种草码]不能为空")
    @ApiModelProperty("种草码")
    private String seedCode;

    @NotNull(message = "[商家ID]不能为空")
    @ApiModelProperty("商家ID")
    private Long businessId;

    @NotBlank(message = "[订单号]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("订单号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String orderNum;

    @NotNull(message = "[订单金额]不能为空")
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @NotNull(message = "[订单实付金额（单位：￥）]不能为空")
    @ApiModelProperty("订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @ApiModelProperty("支付时间")
    @NotNull(message = "[支付时间]不能为空")
    private Date payTime;

}
