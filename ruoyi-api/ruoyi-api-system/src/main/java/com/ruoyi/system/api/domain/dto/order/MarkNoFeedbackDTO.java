package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/21 16:31
 */
@Data
public class MarkNoFeedbackDTO implements Serializable {
    private static final long serialVersionUID = -7931867975047176321L;

    /**
     * 模特反馈素材详情ID
     */
    @ApiModelProperty(value = "模特反馈素材详情ID", required = true)
    @NotNull(message = "[模特反馈素材详情ID]不能为空")
    private Long id;

    /**
     * 反馈状态备注
     */
    @ApiModelProperty(value = "反馈状态备注", required = true)
    @NotBlank(message = "[反馈状态备注]不能为空")
    @Size(max = 500, message = "[反馈状态备注]长度不能超过500个字符")
    private String feedbackRemark;
}
