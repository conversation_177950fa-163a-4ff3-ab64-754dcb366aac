package com.ruoyi.system.api.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/26 15:17
 */
@Data
public class GeneratePdfDTO implements Serializable {
    private static final long serialVersionUID = -205901576793168667L;
    /**
     * 公司
     */
    private String company;
    /**
     * 交款单位
     */
    private String unit;
    /**
     * 账单日期
     */
    private String date;
    /**
     * 项目名称
     */
    private String name;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 收款公司名称
     */
    private String receiptCompany;
    /**
     * 开户行名称
     */
    private String openingBank;
    /**
     * 收款账号
     */
    private String proceedsAccount;
}
