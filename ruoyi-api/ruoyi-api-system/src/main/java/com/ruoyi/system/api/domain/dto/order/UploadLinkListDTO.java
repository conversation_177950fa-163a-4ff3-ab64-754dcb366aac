package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-10-16 14:56
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UploadLinkListDTO implements Serializable {
    private static final long serialVersionUID = -4451830584103798194L;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 拍摄模特ID
     */
    @ApiModelProperty(value = "拍摄模特ID")
    private List<Long> shootModelIds;

    /**
     * 上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传）
     */
    @ApiModelProperty(value = "上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传）")
    private Integer status;

    /**
     * 是否有封面（1：有，0：没有）
     */
    @ApiModelProperty(value = "是否有封面（1：有，0：没有）")
    private Integer existCover;

    /**
     * 上传账号
     */
    @ApiModelProperty(value = "上传账号")
    private List<String> uploadAccounts;

    /**
     * 根据关键字获取到的中文部/英文部客服ID
     */
    private Collection<Long> backUserIds;


    @ApiModelProperty(value = "视频id列表")
    private Collection<Long> videoIds;

    /**
     * 上传需求时间-开始
     */
    @ApiModelProperty(value = "上传需求时间-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date uploadLinkTimeBegin;

    /**
     * 上传需求时间-结束
     */
    @ApiModelProperty(value = "上传需求时间-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date uploadLinkTimeEnd;


    @ApiModelProperty(value = "1-首次,2-2次,3-3次,4-4次,5-4次以上")
    private Integer uploadLinkCount;
}
