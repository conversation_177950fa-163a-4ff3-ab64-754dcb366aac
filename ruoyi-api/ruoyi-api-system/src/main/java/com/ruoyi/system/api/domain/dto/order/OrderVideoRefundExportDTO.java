package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-06-21
 */
@Data
public class OrderVideoRefundExportDTO implements Serializable {
    private static final long serialVersionUID = -408797914035987798L;
    /**
     * 申请时间
     */
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;
    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderNum;

    /**
     * 退款审批号
     */
    @Excel(name = "退款审批号")
    private String refundNum;

    /**
     * 视频编码
     */
    @Excel(name = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @Excel(name = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @Excel(name = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @Excel(name = "产品链接")
    private String productLink;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他)
     */
    @Excel(name = "使用平台", readConverterExp = "0=Amazon,1=tiktok,2=其他,3=APP/解说类")
    private Integer platform;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @Excel(name = "拍摄国家", readConverterExp = "1=英国,2=加拿大,3=德国,4=法国,5=意大利,6=西班牙,7=美国")
    private Integer shootingCountry;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @Excel(name = "照片数量", readConverterExp = "1=2张/$10,2=5张/$20")
    private Integer picCount;

    /**
     * 申请时订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9=交易关闭）
     */
    @Excel(name = "订单状态", readConverterExp = "1=待支付,2=待审核,3=待确认,4=待匹配,5=需发货,6=待完成,7=需确认,8=已完成,9=交易关闭")
    private Integer status;

    /**
     * 拍摄模特
     */
    @Excel(name = "拍摄模特")
    private String shootModelName;

    /**
     * 对接人
     */
    @Excel(name = "对接人")
    private String contactUserName;

    /**
     * 出单人
     */
    @Excel(name = "出单人")
    private String issueUserName;

    /**
     * 视频金额（单位：￥）
     */

    private BigDecimal amount;

    @Excel(name = "订单金额")
    private BigDecimal realAmount;

    /**
     * 退款金额（单位：￥）
     */
    @Excel(name = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @Excel(name = "退款类型", readConverterExp = "1=补偿,2=取消订单,3=取消选配")
    private Integer refundType;

    /**
     * 发起方（1:商家,2:平台）
     */
    @Excel(name = "退款发起方", readConverterExp = "1=商家,2=平台")
    private Integer initiatorSource;

    /**
     * 发起人
     */
    @Excel(name = "退款发起人")
    private String initiatorName;

    /**
     * 退款原因
     */
    @Excel(name = "退款原因")
    private String refundCause;

    /**
     * 退款状态（0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）
     */
    @Excel(name = "退款状态", readConverterExp = "0=退款待审核,1=退款中,2=已拒绝,3=已取消,4=退款成功")
    private Integer refundStatus;

    /**
     * 操作时间
     */
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    /**
     * 拒绝理由
     */
    @Excel(name = "拒绝理由")
    private String rejectCause;

    @Excel(name = "备注", defaultValue = StrPool.DASHED)
    private String remark;
}
