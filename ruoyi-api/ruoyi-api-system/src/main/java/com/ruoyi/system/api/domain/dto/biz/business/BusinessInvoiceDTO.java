package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 10:19
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessInvoiceDTO implements Serializable {
    private static final long serialVersionUID = -3920762309739600680L;

    @NotNull(message="[商家主账号（FK：business_account.account）]不能为空")
    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;

    @ApiModelProperty("抬头类型  0-企业")
    private Integer invoiceTitleType;

    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("发票抬头")
    @Length(max= 32,message="编码长度不能超过32")
    @NotBlank(message = "[发票抬头]不能为空")
    private String invoiceTitle;

    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("税号")
    @Length(max= 32,message="编码长度不能超过32")
    @NotBlank(message = "[税号]不能为空")
    private String invoiceDutyParagraph;

    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("发票内容")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceContent;
}
