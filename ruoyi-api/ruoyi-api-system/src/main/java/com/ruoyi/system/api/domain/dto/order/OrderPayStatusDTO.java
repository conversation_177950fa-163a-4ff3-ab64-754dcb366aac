package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 订单支付状态
 *
 * <AUTHOR>
 * @date 2024/6/4
 */
@Data
@AllArgsConstructor
public class OrderPayStatusDTO implements Serializable {
    private static final long serialVersionUID = 669610496309844837L;

    /**
     * 合并单ID
     */
    @ApiModelProperty(value = "合并单ID")
    private Long mergeId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 支付交易状态（0:交易关闭,1:交易成功,2:退款中,3:未支付,4:已撤销,5:用户支付中,6:支付失败,7:未知状态,8:交易结束）
     */
    @ApiModelProperty(value = "支付交易状态（0:交易关闭,1:交易成功,2:退款中,3:未支付,4:已撤销,5:用户支付中,6:支付失败,7:未知状态,8:交易结束）")
    private Integer payStatus;

}
