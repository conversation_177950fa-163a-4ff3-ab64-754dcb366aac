package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Null;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/2 9:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderModelListDTO implements Serializable {
    private static final long serialVersionUID = 996138523838042029L;
    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    @Size(max = 100, message = "[关键字]长度不能超过100字符")
    private String keyword;

    /**
     * 擅长品类
     */
    @ApiModelProperty("擅长品类")
    private List<Long> specialtyCategory;

    /**
     * 当前登录模特id
     */
    @Null(message = "不接收此参数，请检查后重试")
    private Long curModelId;


    /**
     * 当前登录模特为一般模特时，拼接此段sql
     */
    @Null(message = "不接收此参数，请检查后重试")
    // private String sql;
    private Boolean isQuality;
    /**
     * 当前登录模特的国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @Null(message = "不接收此参数，请检查后重试")
    private Integer shootingCountry;

    /**
     * 当前登录模特的模特类型（0:影响者,1:素人）
     */
    @Null(message = "不接收此参数，请检查后重试")
    private Integer modelType;

    /**
     * 当前登录模特的平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @Null(message = "不接收此参数，请检查后重试")
    private List<Long> platform;

    /**
     *  预选模特坑位数
     */
    @Null(message = "不接收此参数，请检查后重试")
    private Integer preselectModelNumberOfPits;

    /**
     * 视频订单ID
     */
    @Null(message = "不接收此参数，请检查后重试")
    private Long videoId;

    /**
     * 视频订单释放到模特端订单列表 前几个小时优先推送给优质模特（单位：小时，24小时制）
     */
    @Null(message = "不接收此参数，请检查后重试")
    private Integer releaseOverTime;

    /**
     * 模特端新版48小时逻辑旧数据结束时间
     */
    @Null(message = "不接收此参数，请检查后重试")
    private String modelTerminalAllListOldDataEndTime;

    /**
     * 是否展示（1:展示,0:不展示）
     */
    @Null(message = "不接收此参数，请检查后重试")
    private Integer isShow;

    /**
     * 拉黑该模特的商家ID
     */
    @Null(message = "不接收此参数，请检查后重试")
    private List<Long> blackModelBusinessIds;
}
