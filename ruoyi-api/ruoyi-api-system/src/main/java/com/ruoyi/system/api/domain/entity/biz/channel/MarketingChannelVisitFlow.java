package com.ruoyi.system.api.domain.entity.biz.channel;

import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* 市场渠道访问流水数据
* @TableName marketing_channel_visit_flow
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MarketingChannelVisitFlow implements Serializable {

    @NotNull(message="[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message="[渠道id（marketing_channel.id）]不能为空")
    @ApiModelProperty("渠道id（marketing_channel.id）")
    private Long channelId;

    @NotNull(message="[独立访客]不能为空")
    @ApiModelProperty("独立访客")
    private Integer uniqueVisitor;

    @NotNull(message="[访问量]不能为空")
    @ApiModelProperty("访问量")
    private Integer pageView;

    @NotNull(message="[跳出率]不能为空")
    @ApiModelProperty("跳出率")
    private BigDecimal bounceRate;

    @ApiModelProperty("采集时间")
    private Date createTime;
}
