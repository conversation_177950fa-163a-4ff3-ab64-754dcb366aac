package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.RefundTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-20
 */
@Data
public class ApplyRefundDTO implements Serializable {
    private static final long serialVersionUID = -4949986776475134585L;
    /**
     * 视频订单id
     */
    @NotNull(message = "[视频订单id]不能为空")
    @ApiModelProperty(value = "视频订单id", required = true)
    private Long videoId;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @NotNull(message = "[退款类型]不能为空")
    @ApiModelProperty(value = "退款类型（1:补偿,2:取消订单,3:取消选配）", notes = "1:补偿,2:取消订单,3:取消选配", required = true)
    @EnumValid(enumClass = RefundTypeEnum.class, message = "[退款类型]输入错误")
    private Integer refundType;
    /**
     * 退款金额（单位：￥）
     */
    @NotNull(message = "[退款金额]不能为空")
    @ApiModelProperty(value = "退款金额（单位：￥）", notes = "单位：￥", required = true)
    @PositiveOrZero(message = "[退款金额]不能小于0")
    @DecimalMax(value = "999999.00", message = "[退款金额]不能超过999999")
    private BigDecimal refundAmount;


    @ApiModelProperty(value = "是否全额退款照片（0:是,1:不是）")
    @EnumValid(enumClass = StatusTypeEnum.class, message = "[是否全额退款照片]输入错误")
    private Integer isFullRefundPic;

    @ApiModelProperty(value = "退款照片数量")
    @Min(value = 1, message = "[退款照片数量]退款照片数量至少为1")
    private Integer refundPicCount;

    /**
     * 退款原因
     */
    @ApiModelProperty(value = "退款原因", required = true)
    @Size(max = 500, message = "[退款原因]长度不能超过500个字符")
    private String refundCause;

    /**
     * 关联任务ID
     */
    @ApiModelProperty(value = "关联任务ID")
    private List<Long> taskDetailIds;

    @ApiModelProperty(value = "是否关闭订单：0-否，1-是")
    private Integer isCancelOrder;

    @AssertTrue(message = "[退款类型]为[补偿订单]时，才可关联任务")
    private boolean isTaskDetail() {
        if (CollUtil.isEmpty(taskDetailIds)) {
            return true;
        }
        return RefundTypeEnum.REPARATION.getCode().equals(refundType);
    }
    @AssertTrue(message = "[退款类型]为[补偿订单]时，是否关闭订单不能为空")
    private boolean isCancelOrderType() {
        if (!RefundTypeEnum.REPARATION.getCode().equals(refundType)){
            return true;
        }
        return ObjectUtil.isNotNull(isCancelOrder);
    }
    @AssertTrue(message = "[退款类型]为[照片选配]时，[是否全额退款]、[退款照片数量]不能为空")
    private boolean isCheckPicType() {
        if (!RefundTypeEnum.CANCEL_OPENTION.getCode().equals(refundType)){
            return true;
        }
        return ObjectUtil.isNotNull(refundPicCount);
    }
}
