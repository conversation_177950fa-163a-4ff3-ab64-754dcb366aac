package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.PayTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-12 16:18
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderPayLockDTO implements Serializable {
    private static final long serialVersionUID = 2414869440815268691L;

    /**
     * 合并单ID
     */
    @ApiModelProperty(value = "合并单ID")
    private Long mergeId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 使用的余额
     */
    @ApiModelProperty("使用的余额")
    @NotNull(message = "[使用余额]不能为空")
    private BigDecimal useBalance;

    /**
     * 支付类型
     */
    @ApiModelProperty("支付类型")
    @NotNull(message = "[支付类型]不能为空")
    @EnumValid(enumClass = PayTypeEnum.class, message = "[支付类型]输入错误")
    private Integer payType;

    @AssertTrue(message = "[合并单ID]和[订单编号]不能同时为空")
    private boolean isMerge() {
        return CharSequenceUtil.isNotBlank(orderNum) || ObjectUtil.isNotNull(mergeId);
    }
    @AssertTrue(message = "[合并单ID]和[订单编号]两者二选一")
    private boolean isMergeParam() {
        if (ObjectUtil.isNotNull(mergeId)) {
            return CharSequenceUtil.isBlank(orderNum);
        }
        if (CharSequenceUtil.isNotBlank(orderNum)) {
            return ObjectUtil.isNull(mergeId);
        }
        return true;
    }
}
