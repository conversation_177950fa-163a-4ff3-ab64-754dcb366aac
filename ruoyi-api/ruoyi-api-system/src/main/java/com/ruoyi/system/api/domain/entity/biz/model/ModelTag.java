package com.ruoyi.system.api.domain.entity.biz.model;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 模特分类对象 model_tag
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@ApiModel(value = "模特分类对象 model_tag")
@TableName("model_tag")
@Data
public class ModelTag implements Serializable
{

    private static final long serialVersionUID = -1534374813309917869L;
    /** $column.columnComment */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /** 模特id */
    @ApiModelProperty(value = "模特id")
    @Excel(name = "模特id")
    private Long modelId;

    /** 标签id */
    @ApiModelProperty(value = "标签id")
    @Excel(name = "标签id")
    private Long dictId;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    private String dictName;

    /**
     * 分类id（1009:模特标签,1008:类目标签）
     */
    @ApiModelProperty(value = "分类id（1009:模特标签,1008:类目标签）")
    private Long dictCategoryId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
}
