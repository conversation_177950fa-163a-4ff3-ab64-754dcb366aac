package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@Data
public class OrderMemberChannelSettlementDTO implements Serializable {
    private static final long serialVersionUID = 293122723936840526L;

    /**
     * 主键
     */
    @NotEmpty(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private List<Long> ids;

    /**
     * 实际结算金额
     */
    @ApiModelProperty("实际结算金额")
    @DecimalMin(value = "0.0", message = "[实际结算金额不能小于0]")
    private BigDecimal realSettleAmount;

    /**
     * 结算时间
     */
    @ApiModelProperty("结算时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @PastOrPresent(message = "[结算时间]不能大于当前时间")
    private Date settleTime;

    /**
     * 结算凭证地址
     */
    @NotBlank(message = "[结算凭证地址]不能为空")
    @Size(max = 64, message = "编码长度不能超过64")
    @ApiModelProperty("结算凭证地址")
    private String settleResourceUrl;

    /**
     * 备注
     */
    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("备注")
    private String remark;
}
