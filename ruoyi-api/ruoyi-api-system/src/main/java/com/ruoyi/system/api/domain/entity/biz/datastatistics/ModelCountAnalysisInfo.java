package com.ruoyi.system.api.domain.entity.biz.datastatistics;

import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9 17:06
 */
@Data
public class ModelCountAnalysisInfo implements Serializable {
    private static final long serialVersionUID = 8784159868965504872L;

    /**
     * 该月新增/淘汰模特（人）
     */
    private Long modelNumber;

    /**
     * 新增/淘汰模特平均佣金（$）
     */
    private BigDecimal modelAverageCommission;

    /**
     * 模特合作深度分析饼图
     */
    private List<PieChartVO> pieChartVOS;
}
