package com.ruoyi.system.api.domain.dto.biz.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-09 17:16
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DistributionChannelOrderBatchDTO implements Serializable {
    private static final long serialVersionUID = 5705764808572863996L;

    @NotNull(message = "[渠道订单]不能为空")
    @Size(min = 1, message = "至少需要有一条渠道订单")
    @Valid
    private List<DistributionChannelOrderDTO> list;

    @ApiModelProperty(value = "是否全局更新：0-否,1-是")
    private Integer isAll;
}
