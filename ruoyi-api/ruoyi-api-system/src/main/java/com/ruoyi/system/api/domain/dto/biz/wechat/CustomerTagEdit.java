package com.ruoyi.system.api.domain.dto.biz.wechat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 客户标签编辑
 * @create :2024-09-25 15:55
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerTagEdit implements Serializable {
    private static final long serialVersionUID = -4740255972842187718L;

    /**
     * 添加外部联系人的userid
     */
    private String userid;
    /**
     * 外部联系人userid
     */
    private String external_userid;
    /**
     * 要标记的标签列表
     */
    private String[] add_tag;
    /**
     * 要移除的标签列表
     */
    private String[] remove_tag;
}
