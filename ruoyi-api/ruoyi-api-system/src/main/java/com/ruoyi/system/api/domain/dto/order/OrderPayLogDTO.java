package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 订单支付记录表
* <AUTHOR>
 * @TableName order_pay_log
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderPayLogDTO implements Serializable {
    private static final long serialVersionUID = -2744295321131593860L;

    @NotBlank(message="[订单号]不能为空")
    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("订单号")
    private String orderNum;

    @NotNull(message="[订单类型（0-视频订单，1-会员订单，3-线下钱包充值订单，5.线上钱包充值）]不能为空")
    @ApiModelProperty("订单类型（0-视频订单，1-会员订单，3-线下钱包充值订单，5.线上钱包充值）")
    private Integer orderType;

    @NotNull(message="[商家id（business.id）]不能为空")
    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    @Size(max= 30,message="编码长度不能超过30")
    @ApiModelProperty("商户订单号")
    private String mchntOrderNo;

    @ApiModelProperty("商户号")
    private String mchid;

    @NotNull(message="[支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账,7:全币种)]不能为空")
    @ApiModelProperty("支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账,7:全币种)")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    @ApiModelProperty("支付时间")
    @NotNull(message="[支付时间]不能为空")
    private Date payTime;

    @NotNull(message="[需支付金额]不能为空")
    @ApiModelProperty("需支付金额")
    private BigDecimal payAmount;

    @NotNull(message="[订单实付金额（单位：￥）]不能为空")
    @ApiModelProperty("订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @ApiModelProperty("使用余额（单位：￥）")
    private BigDecimal useBalance;


    @ApiModelProperty("收款人账号配置Id")
    private Long payeeAccountConfigInfoId;

}
