package com.ruoyi.system.api.domain.dto.biz.business.account.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-27 11:47
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BizUserChannelStatisticsDTO implements Serializable {
    private static final long serialVersionUID = -2161227852250556879L;

    @ApiModelProperty("注册渠道类型(0=普通,1=市场，2=分销)")
    private Integer channelType;

    @ApiModelProperty("注册时间-开始")
    private Date registerStartTime;

    @ApiModelProperty("注册时间-开始")
    private Date registerEndTime;

}
