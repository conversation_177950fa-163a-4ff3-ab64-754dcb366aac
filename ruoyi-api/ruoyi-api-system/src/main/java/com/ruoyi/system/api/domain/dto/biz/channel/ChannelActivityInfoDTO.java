package com.ruoyi.system.api.domain.dto.biz.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelActivityInfoDTO {
    @ApiModelProperty(value = "渠道id")
    private Long id;

    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "海报名称")
    private String posterName;

    @ApiModelProperty(value = "渠道折扣")
    private BigDecimal brokeRage;

    @ApiModelProperty("种草码")
    private String seedCode;

    @ApiModelProperty("渠道状态")
    private Integer status;

    @ApiModelProperty("专属企微二维码地址")
    private String weChatUrl;

    @ApiModelProperty(value = "渠道活动信息")
    private List<ChannelActivityDTO> activityList;
}
