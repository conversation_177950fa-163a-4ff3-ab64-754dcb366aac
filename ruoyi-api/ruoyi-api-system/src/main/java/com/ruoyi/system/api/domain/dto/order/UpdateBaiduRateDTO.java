package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/4 14:17
 */
@Data
public class UpdateBaiduRateDTO {

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    @NotBlank(message = "[订单号]不能为空")
    private String orderNum;

    /**
     * 百度汇率
     */
    @ApiModelProperty("百度汇率")
    @NotNull(message = "[百度汇率]不能为空")
    @Digits(integer = 1, fraction = 4, message = "[百度汇率]只支持1位整数以及最多4位小数")
    @DecimalMin(value = "6.5", message = "[百度汇率]最小值为6.5")
    @DecimalMax(value = "8", message = "[百度汇率]最大值为8")
    private BigDecimal baiduRate;
}
