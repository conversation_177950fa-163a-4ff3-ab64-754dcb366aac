package com.ruoyi.system.api.domain.entity.biz.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家预付表
 *
 * <AUTHOR>
 * @TableName business_balance_prepay
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalancePrepay implements Serializable {

    private static final long serialVersionUID = -981735608546630181L;
    @TableId(type = IdType.AUTO)
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[商家ID]不能为空")
    @ApiModelProperty("商家ID")
    private Long businessId;

    @ApiModelProperty(value = "支付单号")
    private String payNum;

    @ApiModelProperty(value = "订单类型（3-线下钱包充值，5-线上钱包充值）")
    private Integer orderType;

    @ApiModelProperty(value = "支付用户id")
    private Long payUserId;

    @ApiModelProperty(value = "支付宝支付商户号")
    private String alipayPayAppId;

    @ApiModelProperty(value = "微信支付商户号")
    private String wechatPayAppId;

    @ApiModelProperty("关闭订单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date closeOrderTime;

    @ApiModelProperty(value = "提交凭证时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitCredentialTime;

    @ApiModelProperty("订单状态（1:待支付,2:待审核,8:已完成,9:交易关闭）")
    private Integer status;

    @ApiModelProperty("预付单号")
    private String prepayNum;

    @NotNull(message = "[申请增加金额（单位：￥）]不能为空")
    @ApiModelProperty("申请增加金额（单位：￥）")
    private BigDecimal amount;

    @ApiModelProperty(value = "包含赠送金额")
    private BigDecimal containPresentedAmount;

    @ApiModelProperty("支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账)")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    @ApiModelProperty("税点（单位：%）")
    private BigDecimal taxPoint;

    @ApiModelProperty("应付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("应付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @ApiModelProperty("实际增加金额（单位：￥）")
    private BigDecimal realAmount;

    @ApiModelProperty("实际支付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @ApiModelProperty("订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty("币种（详见sys_dict_type.dict_type = sys_money_type）")
    private String currency;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty(value = "付款账号")
    private String payAccount;

    @Size(max = 300, message = "[图片资源地址]不能超过300")
    @ApiModelProperty("图片资源地址id（FK：resource.id）使用，隔开")
    @Length(max = 300, message = "[图片资源地址]不能超过300")
    private String resourceId;

    @NotNull(message = "[审核状态（0:待处理,1:审核通过,2.审核拒绝）]不能为空")
    @ApiModelProperty("审核状态（0:待处理,1:审核通过,2.审核拒绝）")
    private Integer auditStatus;

    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @Size(max = 30, message = "[审核人员名称]不能超过30")
    @ApiModelProperty("审核人员名称")
    @Length(max = 30, message = "[审核人员名称]不能超过30")
    private String auditUserName;

    @ApiModelProperty("收款账号ID")
    private Long accountId;

    @Size(max = 100, message = "[申请备注]不能超过100")
    @ApiModelProperty("申请备注")
    @Length(max = 100, message = "[申请备注]不能超过100")
    private String applyRemark;

    @Size(max = 100, message = "[拒绝原因]不能超过100")
    @ApiModelProperty("拒绝原因")
    @Length(max = 100, message = "[拒绝原因]不能超过100")
    private String rejectCause;

    @Size(max = 300, message = "[备注]不能超过300")
    @ApiModelProperty("备注")
    @Length(max = 300, message = "{备注}不能超过300")
    private String remark;

    @NotNull(message = "[创建人id（sys_user.user_id）]不能为空")
    @ApiModelProperty("创建人id（sys_user.user_id）")
    private Long createById;

    @NotBlank(message = "[创建人名称（sys_user.user_name）]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("创建人名称（sys_user.user_name）")
    @Length(max = 30, message = "编码长度不能超过30")
    private String createBy;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creatTime;
}
