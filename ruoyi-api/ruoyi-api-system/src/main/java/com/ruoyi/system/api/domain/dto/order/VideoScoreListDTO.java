package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/26 9:36
 */
@Data
public class VideoScoreListDTO implements Serializable {
    private static final long serialVersionUID = -5490492852676153620L;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 拍摄模特ID
     */
    @ApiModelProperty(value = "拍摄模特ID")
    private List<Long> shootModelIds;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）")
    private Integer modelType;

    /**
     * 评分人ID
     */
    @ApiModelProperty(value = "评分人ID")
    private List<Long> videoScoreByIds;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private List<Float> videoScores;
}
