package com.ruoyi.system.api.domain.entity.biz.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2025-07-08 20:35:17
 */
@Data
@TableName("model_data_table_remark")
public class ModelDataTableRemark implements Serializable {
    private static final long serialVersionUID = 6625988779995239740L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模特ID（FK：model.id）
     */
    @ApiModelProperty(value = "模特ID（FK：model.id）")
    private Long modelId;

    /**
     * 备注内容
     */
    @ApiModelProperty(value = "备注内容")
    private String remark;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createBy;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createById;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人姓名
     */
    @ApiModelProperty(value = "更新人姓名")
    private String updateBy;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long updateById;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
