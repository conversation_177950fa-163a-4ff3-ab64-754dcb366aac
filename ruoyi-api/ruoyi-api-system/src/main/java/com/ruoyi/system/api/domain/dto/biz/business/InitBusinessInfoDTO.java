package com.ruoyi.system.api.domain.dto.biz.business;

import com.ruoyi.common.core.enums.BusinessScaleTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 09:40
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InitBusinessInfoDTO implements Serializable {
    private static final long serialVersionUID = 8093391892514113380L;


    @NotBlank(message="[商家名称]不能为空")
    @Size(max= 50,message="[商家名称]长度不能超过50")
    @ApiModelProperty("商家名称")
    private String name;

    @ApiModelProperty("账号名称")
    @NotBlank(message="[账号名称]不能为空")
    @Size(max= 20,message="[账号名称]长度不能超过20")
    private String accountName;

    @ApiModelProperty("规模")
    @EnumValid(enumClass = BusinessScaleTypeEnum.class, message = "[商家规模]输入错误")
    private Integer scale;

    @ApiModelProperty("手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式有误")
    private String phone;

    @ApiModelProperty("手机验证码")
    private String phoneCaptcha;
}
