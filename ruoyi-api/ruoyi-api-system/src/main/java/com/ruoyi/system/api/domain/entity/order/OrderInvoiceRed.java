package com.ruoyi.system.api.domain.entity.order;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:07
 */
@Data
@TableName("order_invoice_red")
public class OrderInvoiceRed implements Serializable {
    private static final long serialVersionUID = 1934584124371607379L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 发票ID (FK:order_invoice.id)
     */
    @ApiModelProperty(value = "发票ID")
    private Long invoiceId;

    /**
     * 红冲状态（1：待红冲，2：不红冲，3：已红冲）
     */
    @ApiModelProperty(value = "红冲状态（1：待红冲，2：不红冲，3：已红冲）")
    private Integer invoiceRedStatus;

    /**
     * 红冲原因（1：重开发票，2：商家提现）
     */
    @ApiModelProperty(value = "红冲原因（1：重开发票，2：商家提现）")
    private Integer invoiceRedCause;

    /**
     * 红冲票号
     */
    @ApiModelProperty(value = "红冲票号")
    private String invoiceRedNumber;

    /**
     * 红冲金额
     */
    @ApiModelProperty(value = "红冲金额")
    private BigDecimal invoiceRedAmount;

    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    private Date invoiceRedInvoicingTime;

    /**
     * 发票URI
     */
    @ApiModelProperty(value = "发票URI")
    private String objectKey;

    /**
     * 是否重开（1：重开，0：不重开）
     */
    @ApiModelProperty(value = "是否重开（1：重开，0：不重开）")
    private Integer isReopen;

    /**
     * 重开金额
     */
    @ApiModelProperty(value = "重开金额")
    private BigDecimal reopenAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 申请人姓名
     */
    @ApiModelProperty(value = "申请人姓名")
    private String applyBy;

    /**
     * 申请人ID
     */
    @ApiModelProperty(value = "申请人ID")
    private Long applyById;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private Date applyTime;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 发票类型（1：增值税普通发票，2：形式发票）
     */
    @ApiModelProperty(value = "发票类型（1：增值税普通发票，2：形式发票）")
    private Integer invoiceType;

    /**
     * 抬头类型（1：企业单位）
     */
    @ApiModelProperty(value = "抬头类型（1：企业单位）")
    private Integer titleType;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    private String title;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    private String dutyParagraph;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    /**
     * 公司联系电话
     */
    @ApiModelProperty(value = "公司联系电话")
    private String companyPhone;

    /**
     * 公司联系人
     */
    @ApiModelProperty(value = "公司联系人")
    private String companyContact;

    /**
     * 附件URI
     */
    @ApiModelProperty(value = "附件URI")
    private String attachmentObjectKey;

    /**
     * 发票内容
     */
    @ApiModelProperty(value = "发票内容")
    private String content;

    /**
     * 发票备注
     */
    @ApiModelProperty(value = "发票备注")
    private String invoiceRemark;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 图片URI
     */
    @ApiModelProperty(value = "发票文件URI")
    @TableField(exist = false)
    private List<String> objectKeys;

    public void setObjectKey(String objectKey) {
        this.objectKey = objectKey;
        if (StrUtil.isNotBlank(objectKey)) {
            this.objectKeys = StrUtil.split(objectKey, StrUtil.COMMA);
        }
    }
}
