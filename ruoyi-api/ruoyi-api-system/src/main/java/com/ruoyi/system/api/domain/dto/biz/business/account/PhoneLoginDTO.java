package com.ruoyi.system.api.domain.dto.biz.business.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :
 * @description :
 * @create :2023-09-19 13:53
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel(value="手机号登录信息")
public class PhoneLoginDTO implements Serializable {
    private static final long serialVersionUID = -8797332829562514872L;

    @NotBlank(message = "[手机号]不能为空")
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 手机验证码、SpToken都使用这个字段
     */
    @NotBlank(message = "[验证码]不能为空")
    @ApiModelProperty("验证码")
    private String phoneCaptcha;

    @ApiModelProperty("ticket")
    private String ticket;

    @ApiModelProperty("专属链接code")
    private String linkCode;

    @ApiModelProperty("是否阿里云一键登录：0-否，1-是")
    private Integer isAliyunLogin;

}
