package com.ruoyi.system.api.domain.dto.biz.business.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-28 13:56
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value="检查Ticket是否可用")
public class RefreshTicketDTO implements Serializable {
    private static final long serialVersionUID = -3907830783615103787L;
    @NotBlank(message = "[ticket]不能为空")
    @ApiModelProperty("ticket")
    private String ticket;
}
