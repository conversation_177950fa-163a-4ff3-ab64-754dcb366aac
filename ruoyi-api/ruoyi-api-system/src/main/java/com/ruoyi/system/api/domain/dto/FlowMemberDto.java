package com.ruoyi.system.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-02 11:16
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlowMemberDto implements Serializable {
    private static final long serialVersionUID = 6248596021253434580L;

    @NotNull(message="[商家id]不能为空")
    @ApiModelProperty("商家id")
    private Long businessId;

    @NotNull(message="[登录账号ID]不能为空")
    @ApiModelProperty("登录账号ID")
    private Long bizUserId;

    @ApiModelProperty("选择套餐类型: 0-季度套餐，1-一年会员，2-三年会员")
    private Integer choosePackageType;

    @ApiModelProperty("选择会员状态:0-非会员1-正常，2-即将过期，3-已过期'")
    private Integer chooseMemberStatus;

    @ApiModelProperty("使用余额order_table.use_balance")
    private BigDecimal useBalance;

    @ApiModelProperty(value = "订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;

    /**
     * 订单实付金额（对应币种实付）
     */
    @ApiModelProperty("订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    /**
     * 币种（详见sys_dict_type.dict_type = sys_money_type）
     */
    @ApiModelProperty("币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    /**
     * 支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）
     */
    @ApiModelProperty("支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）")
    private Integer payType;

    @ApiModelProperty("种草码")
    private String seedCode;

    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @ApiModelProperty("订单号")
    private String orderNum;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "税点费用")
    private BigDecimal taxPointCost;

    @ApiModelProperty("加赠时间")
    private Integer presentedTime;

    @ApiModelProperty("加赠时间类型（1-天,2-月,3-年）")
    private Integer presentedTimeType;

    @ApiModelProperty("支付时间")
    private Date payTime;

    @ApiModelProperty("修改商家余额dto")
    @Valid
    private BusinessBalanceDTO businessBalanceDto;


}
