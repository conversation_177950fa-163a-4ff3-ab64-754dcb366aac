package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单_视频_关联标签对象 order_video_tag
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@ApiModel(value = "订单_视频_关联标签对象 order_video_tag")
@TableName("order_video_tag")
@Data
public class OrderVideoTag implements Serializable {

    private static final long serialVersionUID = -3966088283747716962L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    @Excel(name = "视频id")
    private Long videoId;

    /**
     * 标签id
     */
    @ApiModelProperty(value = "标签id")
    @Excel(name = "标签id")
    private Long tagId;

    /**
     * 标签分类id
     */
    @ApiModelProperty(value = "标签分类id")
    @Excel(name = "标签分类id")
    private Long categoryId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
