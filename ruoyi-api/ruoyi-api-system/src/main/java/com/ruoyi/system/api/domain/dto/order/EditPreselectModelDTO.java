package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.PreselectStatusEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 更改预选模特
 *
 * <AUTHOR>
 * @date 2024/6/13 11:11
 */
@Data
public class EditPreselectModelDTO implements Serializable {
    private static final long serialVersionUID = 7759411178523540570L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "[主键]不能为空")
    private Long id;

    /**
     * 状态（0:未对接,1:已对接,2:已选定,3:已淘汰）
     */
    @ApiModelProperty(value = "状态（0:未对接,1:已对接,2:已选定,3:已淘汰）", notes = "0:未对接,1:已对接,2:已选定,3:已淘汰", required = true)
    @NotNull(message = "[状态]不能为空")
    @EnumValid(enumClass = PreselectStatusEnum.class, message = "[预选状态]输入错误")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 256, message = "[备注]长度不能超过32个字符")
    private String remark;

    /**
     * 图片列表
     */
    @ApiModelProperty(value = "图片列表")
    private List<String> objectKeys;

}
