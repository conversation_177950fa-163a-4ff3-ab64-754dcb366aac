package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.OrderPoolPreselectedTimeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/16 18:31
 */
@Data
public class MyPreselectDistributionListDTO implements Serializable {
    private static final long serialVersionUID = -7027088513610760542L;

    /**
     * 关键词
     */
    @ApiModelProperty("关键词")
    private String keyword;

    /**
     * 分发模特ID
     */
    @ApiModelProperty("分发模特ID")
    private List<Long> modelIds = new ArrayList<>();

    /**
     * 英文部客服
     */
    @ApiModelProperty("英文部客服")
    private List<Long> englishCustomerServiceIds;

    /**
     * 添加预选时间
     */
    @ApiModelProperty(value = "添加预选时间（1:24小时内,2:,1~2天,3:2~3天,4:3天以上）")
    @EnumValid(enumClass = OrderPoolPreselectedTimeEnum.class, message = "[添加预选时间]输入错误")
    private List<Integer> addPreselectTimes;

    @ApiModelProperty(value = "沟通状态（0:沟通,1:未沟通）")
    private Integer communicationStatus;

    /**
     * 添加预选时间
     */
    @Null(message = "请勿传递[addPreselectTimeMap]")
    private Map<String, String> addPreselectTimeMap;
}
