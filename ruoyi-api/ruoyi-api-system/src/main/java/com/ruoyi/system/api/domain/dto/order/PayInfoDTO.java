package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayInfoDTO implements Serializable {
    private static final long serialVersionUID = 3520622140768935147L;

    /**
     * 合并单ID
     */
    @ApiModelProperty(value = "合并单ID")
    private Long mergeId;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNum;

    /**
     * 是否对公
     */
    @ApiModelProperty(value = "是否对公（0:是,1:不是）", required = true)
    @NotNull(message = "[是否是对公]不能为空")
    private Integer isPublic;

    /**
     * 使用的余额
     */
    @ApiModelProperty("使用的余额")
    @PositiveOrZero(message = "[使用的余额]必须大于等于0")
    private BigDecimal useBalance = BigDecimal.ZERO;

    /**
     * 是否是代付
     */
    @ApiModelProperty("是否是代付")
    private Boolean isAnother;

    @AssertTrue(message = "[合并单ID]和[订单编号]不能同时为空")
    private boolean isMerge() {
        return CharSequenceUtil.isNotBlank(orderNum) || ObjectUtil.isNotNull(mergeId);
    }
    @AssertTrue(message = "[合并单ID]和[订单编号]两者二选一")
    private boolean isMergeParam() {
        if (ObjectUtil.isNotNull(mergeId)) {
            return CharSequenceUtil.isBlank(orderNum);
        }
        if (CharSequenceUtil.isNotBlank(orderNum)) {
            return ObjectUtil.isNull(mergeId);
        }
        return true;
    }
}
