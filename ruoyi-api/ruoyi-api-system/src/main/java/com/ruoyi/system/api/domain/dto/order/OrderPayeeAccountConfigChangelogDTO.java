package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 收款人账号关联表
 */
@Data
public class OrderPayeeAccountConfigChangelogDTO implements Serializable {

    private Long id;

    @ApiModelProperty(value = "变更类型 1-新增 2-修改 3-删除")
    private Integer type;

    @ApiModelProperty(value = "变更详情")
    private String comments;

    @ApiModelProperty(value = "变更类型 1-微信,2-支付宝,7-全币种,6-对公账户")
    private Integer configType;

    @ApiModelProperty(value = "创建人姓名")
    private String createBy;

    @ApiModelProperty(value = "创建人ID")
    private Long createById;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

}