package com.ruoyi.system.api.domain.dto.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :分销渠道统计数据
 * @create :2024-09-29 15:36
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MemberChannelStatisticDTO implements Serializable {
    private static final long serialVersionUID = -3576164913184450200L;

    @NotNull(message = "渠道ID列表不能为空")
    @Size(min = 1, message = "至少需要有一条渠道数据")
    private List<Long> channelIds;
}
