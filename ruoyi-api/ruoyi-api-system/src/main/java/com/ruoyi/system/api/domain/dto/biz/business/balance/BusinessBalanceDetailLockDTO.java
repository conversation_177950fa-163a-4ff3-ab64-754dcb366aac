package com.ruoyi.system.api.domain.dto.biz.business.balance;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商家余额详情锁定表
 *
 * <AUTHOR>
 * @TableName business_balance_detail_lock
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceDetailLockDTO implements Serializable {

    private static final long serialVersionUID = -937221405709317618L;
    @NotNull(message = "[商家余额详情ID（business_balance_detail.id）]不能为空")
    @ApiModelProperty("商家余额详情ID（business_balance_detail.id）")
    private Long balanceDetailId;

    @NotNull(message = "[已用金额（business_balance_detail.use_balance + lock_balance 快照）]不能为空")
    @ApiModelProperty("已用金额（business_balance_detail.use_balance + lock_balance 快照）")
    private BigDecimal useBalance;

    @NotNull(message = "[提现金额]不能为空")
    @ApiModelProperty("提现金额")
    private BigDecimal payOutAmount;

}
