package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/26 10:49
 */
@Data
@TableName("order_resource")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderResource implements Serializable {

    private static final long serialVersionUID = 2122333373128148975L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 资源URI
     */
    private String objectKey;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
