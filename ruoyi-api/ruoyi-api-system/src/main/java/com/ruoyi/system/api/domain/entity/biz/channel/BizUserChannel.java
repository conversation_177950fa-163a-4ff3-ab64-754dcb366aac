package com.ruoyi.system.api.domain.entity.biz.channel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户渠道信息表
 *
 * <AUTHOR>
 * @TableName biz_user_channel
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BizUserChannel implements Serializable {

    private static final long serialVersionUID = -1934872765910144568L;

    @TableId(type = IdType.AUTO)
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[用户ID (FK:biz_user.id)]不能为空")
    @ApiModelProperty("用户ID (FK:biz_user.id)")
    private Long bizUserId;

    @ApiModelProperty("注册渠道类型(0=普通,1=市场，2=分销)")
    private Integer registerChannelType;

    @ApiModelProperty("注册渠道账户id")
    private Long registerChannelId;

    @ApiModelProperty("注册时间")
    private Date registerTime;

    @ApiModelProperty("企微渠道类型(0=普通,1=市场，2=分销)")
    private Integer wechatChannelType;

    @ApiModelProperty("添加企微渠道账户id")
    private Long wechatChannelId;

    @ApiModelProperty("添加企微时间")
    private Date addWechatTime;

    @ApiModelProperty("是否激活")
    private Integer isActivate;

    @ApiModelProperty(value = "商家id")
    private Long businessId;

}
