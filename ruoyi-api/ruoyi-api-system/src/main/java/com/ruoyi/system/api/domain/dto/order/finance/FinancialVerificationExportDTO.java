package com.ruoyi.system.api.domain.dto.order.finance;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :财务对账导出
 * @create :2025-01-07 11:45
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class FinancialVerificationExportDTO implements Serializable {
    private static final long serialVersionUID = 2302616460293754593L;
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @NotNull(message = "开始时间不能为空")
    private Date endTime;

    @ApiModelProperty(value = "是否全部字段：0-否，1-是")
    private Integer isAll;
}
