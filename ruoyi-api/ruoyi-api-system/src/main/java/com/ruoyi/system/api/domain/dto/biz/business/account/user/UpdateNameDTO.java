package com.ruoyi.system.api.domain.dto.biz.business.account.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-31 15:58
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpdateNameDTO implements Serializable {
    private static final long serialVersionUID = -258984425630979663L;

    @ApiModelProperty("姓名")
    @NotBlank(message = "[姓名]不能为空")
    @Size(max = 32, message = "[姓名]长度不能超过32")
    private String name;

    @ApiModelProperty("手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式有误")
    private String phone;
}
