package com.ruoyi.system.api.domain.dto.biz.channel.fission;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :提现请求参数
 * @create :2025-05-19 14:24
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawalDTO implements Serializable {
    private static final long serialVersionUID = -6818819385880341236L;

    @ApiModelProperty("提现金额")
    @NotNull(message = "[提现金额]不能为空")
    private BigDecimal settleAmount;

    @ApiModelProperty("提现账号类型(2-支付宝，3-银行卡，6-公户收款)")
    @NotNull(message = "[提现账号类型]不能为空")
    private Integer withdrawalAccountType;

    @ApiModelProperty("收款方姓名/收款公司名称")
    @NotBlank(message = "[收款方姓名]不能为空")
    private String payeeName;

    @ApiModelProperty("收款方手机号/联系人手机号")
    @NotBlank(message = "[收款方手机号]不能为空")
    @Size(max = 11, message = "[收款方手机号]不能超过11位")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式有误")
    private String payeePhone;

    @ApiModelProperty("收款方身份证号")
    @Size(max = 20, message = "[收款方身份证号]不能超过20位")
    private String payeeIdentityCard;

    @ApiModelProperty("收款方账号/收款银行账号")
    @NotBlank(message = "[收款方账号]不能为空")
    @Size(max = 32, message = "[收款方账号]不能超过32位")
    private String payeeAccount;

    @ApiModelProperty("开户行名称")
    @Size(max = 30, message = "[开户行名称]不能超过30位")
    private String bankName;

    @ApiModelProperty("渠道ID")
    private Long channelId;

}
