package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/6/17 16:37
 */
@Data
public class InvoiceListDTO implements Serializable {

    private static final long serialVersionUID = 6005690348549383603L;
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 订单类型（0:视频订单,1:会员订单）
     */
    @ApiModelProperty(value = "订单类型（0:视频订单,1:会员订单）")
    private List<Integer> type;

    /**
     * 开票状态（1:待开票,2:待确认,3:已投递,4:已作废）
     */
    @ApiModelProperty(value = "开票状态（1:待开票,2:待确认,3:已投递）", notes = "1:待开票,2:待确认,3:已投递")
    private List<Integer> status;

    /**
     * 支付时间-begin
     */
    @ApiModelProperty(value = "支付时间-begin")
    private Date payTimeBegin;

    /**
     * 支付时间-end
     */
    @ApiModelProperty(value = "支付时间-end")
    private Date payTimeEnd;

    /**
     * 开票时间-begin
     */
    @ApiModelProperty(value = "开票时间-begin")
    private Date invoicingTimeBegin;

    /**
     * 开票时间-end
     */
    @ApiModelProperty(value = "开票时间-end")
    private Date invoicingTimeEnd;

    /**
     * 通过关键字搜索出的商家id
     */
    @Null(message = "请勿传递[businessIds]参数")
    private Set<Long> businessIds;

    /**
     * 通过关键字搜索出的运营id
     */
    @Null(message = "请勿传递[backUserIds]参数")
    private Set<Long> backUserIds;
}
