package com.ruoyi.system.api.domain.dto.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/3 13:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PosterDTO {
    /**
     * 渠道类型：2-分销渠道 7-裂变
     */
    private Integer channelType;

    /**
     * 海报名称
     */
    private String posterName;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 折扣
     */
    private String discount;

    /**
     * 折扣类型：1-固定金额，2-固定比例
     */
    private Integer discountType;

    /**
     * 活动限时
     */
    private String activityTimeLimit;

    /**
     * 活动时间
     */
    private String activityTime;

    /**
     * 种草码
     */
    private String seedCode;

    /**
     * 企微二维码
     */
    private String weChatUrl;

    /**
     * 拍摄视频数量
     */
    private Integer videoNum;
}
