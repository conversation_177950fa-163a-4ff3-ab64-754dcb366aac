package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 13:48
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessStatusDTO implements Serializable {
    private static final long serialVersionUID = 8656948889224656043L;

    @NotNull(message="[账号]不能为空")
    @ApiModelProperty("账号")
    private String ownerAccount;

    @NotNull(message="[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;
}
