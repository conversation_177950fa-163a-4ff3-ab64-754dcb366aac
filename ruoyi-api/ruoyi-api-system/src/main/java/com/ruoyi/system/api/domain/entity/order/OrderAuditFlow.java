package com.ruoyi.system.api.domain.entity.order;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
* 订单财务审核流水
* <AUTHOR>
 * @TableName order_audit_flow
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderAuditFlow implements Serializable {

    private static final long serialVersionUID = 1617035477372398651L;
    @NotNull(message="[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotBlank(message="[订单号]不能为空")
    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("订单号")
    @Length(max= 32,message="编码长度不能超过32")
    private String orderNum;

    @ApiModelProperty("支付单号")
    private String payNum;

    @ApiModelProperty("财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭）")
    private Integer auditStatus;

    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("审核人员名称")
    @Length(max= 32,message="编码长度不能超过32")
    private String auditUserName;

    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @Size(max= 500,message="编码长度不能超过500")
    @ApiModelProperty("订单支付凭证")
    @Length(max= 500,message="编码长度不能超过500")
    private String orderDocumentResource;

    @Size(max= 300,message="编码长度不能超过300")
    @ApiModelProperty("备注")
    @Length(max= 300,message="编码长度不能超过300")
    private String remark;

}
