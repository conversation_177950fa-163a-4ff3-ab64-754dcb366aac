package com.ruoyi.system.api.domain.dto.biz.tag;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 标签排序
 * @create :2024-11-16 11:45
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TagSortDTO implements Serializable {
    private static final long serialVersionUID = -1183103741842761308L;

    @ApiModelProperty(value = "原标签Id")
    @NotNull(message = "[原标签Id]不能为空")
    private Long originTagId;

    @ApiModelProperty(value = "修改标签Id")
    @NotNull(message = "[修改标签Id]不能为空")
    private Long resultTagId;
}
