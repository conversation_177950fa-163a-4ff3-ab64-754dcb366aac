package com.ruoyi.system.api.domain.dto.order.logistic;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 物流跟进表
 *
 * <AUTHOR>
 * @TableName order_video_logistic_follow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoLogisticFollowDTO implements Serializable {
    private static final long serialVersionUID = -371233792383242062L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("物流关联表id order_video_logistic.id")
    private Long orderVideoLogisticId;

    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    @ApiModelProperty("会员编码（business.member_code）")
    private String memberCode;

    @ApiModelProperty("视频编码")
    private String videoCode;

    @ApiModelProperty("视频id FK:order_video.id")
    private Long videoId;

    @ApiModelProperty("视频id裂变")
    private List<Long> videoIds;

    @ApiModelProperty("物流单号")
    private String number;

    @ApiModelProperty("处理状态枚举(0-未通知、1-已通知、2-延迟发货、3-延迟发货提醒、4-延迟发货已提醒、5-催发货提醒、6-催发货已提醒、7-地址变更通知、8-变更已通知、9-补充说明、10-标记发货、11-标记发货提醒、12-标记发货已提醒、13-通知确认模特、14-已通知确认模特、15-催确认模特提醒、16-已通知催确认模特)")
    private Integer handleStatus;

    @ApiModelProperty("物流状态(0-未发货、1-已发货)")
    private Integer logisticStatus;

    @ApiModelProperty("跟进状态码(1-需处理、2-暂不处理、10-已发货、11-需跟进、12-模特待确认、13-无需跟进、14-已结束)")
    private Integer followStatus;

    @ApiModelProperty("跟进状态码(1-需处理、2-暂不处理、10-已发货、11-需跟进、12-模特待确认、13-无需跟进、14-已结束)")
    private List<Integer> followStatusList;

    @ApiModelProperty("通知时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date notifyTime;

    @ApiModelProperty("发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date logisticStartTime;

    @ApiModelProperty("是否默认发货时间：0-否，1-是")
    private Integer isDefaultLogisticStartTime;

    @ApiModelProperty("最新物流主状态")
    private String latestMainStatus;

    @ApiModelProperty("物流系统同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date logisticUpdateTime;

    @ApiModelProperty("模特结果枚举(0-待处理、1-已询问、2-已收货、3-丢件、4-订单回退、5-待通知拍摄、6-已通知拍摄)")
    private Integer modelResult;

    @ApiModelProperty("实际签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signTime;

    @ApiModelProperty("最新处理说明")
    private String latestRemark;
}
