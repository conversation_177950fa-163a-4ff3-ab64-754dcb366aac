package com.ruoyi.system.api.domain.dto.biz.common;

import com.ruoyi.common.core.enums.TextTypeEnum;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:25
 */
@Data
public class TextHelpDTO implements Serializable {

    private static final long serialVersionUID = 1406309406453369611L;
    @ApiModelProperty(value = "主键")
    @NotNull(message = "[主键]不能为空", groups = CommonValidatedGroup.EditValidatedGroup.class)
    private Long id;

    @ApiModelProperty(value = "文本名称", required = true)
    @NotBlank(message = "[文本名称]不能为空")
    @Size(max = 50, message = "[文本名称]长度不能超过50")
    private String name;

    @NotNull(message = "[文本类型]不能为空")
    @EnumValid(enumClass = TextTypeEnum.class, message = "[文本类型]输入错误")
    private Integer type;

    @NotNull(message = "[排序值]不能为空")
    @Max(value = 99999, message = "[排序值]必须小于等于99999")
    private Integer sort;

    @ApiModelProperty(value = "文本内容")
    @NotBlank(message = "[文本内容]不能为空")
    @Size(max = 65535, message = "[文本内容]长度不能超过65535")
    private String content;
}
