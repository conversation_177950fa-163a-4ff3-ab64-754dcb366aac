package com.ruoyi.system.api.domain.dto.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-09-04
 */
@Data
public class ModelResourceDTO implements Serializable {
    private static final long serialVersionUID = 6928059604777240471L;


    /**
     * 模特ID
     */
    @ApiModelProperty(value = "模特ID")
    private Long modelId;

    /**
     * 视频名称
     */
    @ApiModelProperty(value = "视频名称", required = true)
    @NotBlank(message = "[视频名称]不能为空")
    @Size(max = 32, message = "[视频名称]长度不能超过32位字符")
    private String name;

    /**
     * 类型（1：亚马逊案例视频，2：TikTok案例视频）
     */
    @ApiModelProperty(value = "类型（1：亚马逊案例视频，2：TikTok案例视频）")
    private Integer type;

    /**
     * 视频链接
     */
    @ApiModelProperty(value = "视频链接", required = true)
    @NotBlank(message = "[视频链接]不能为空")
    @Size(max = 1000, message = "[视频链接]长度不能超过1000位字符")
    private String videoUrl;

    /**
     * 封面图片
     */
    @ApiModelProperty(value = "封面图片")
    @NotBlank(message = "[封面图片]不能为空")
    @Size(max = 64, message = "[封面图片]长度不能超过64位字符")
    private String picUri;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @NotNull(message = "[排序]不能为空")
    private Integer sort;
}
