package com.ruoyi.system.api.domain.dto.biz.business.account.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-30 17:18
 **/
@Data
public class BizUserDetailListDTO implements Serializable {
    private static final long serialVersionUID = -2491411174027474838L;

    @ApiModelProperty("商家登录账号id列表")
    private List<Long> ids;

    @ApiModelProperty("账号状态：0-普通账号，1-主账号，2-子账号")
    private Integer accountType;

    @ApiModelProperty(value = "用户id")
    private List<Long> bizUserIds;
}
