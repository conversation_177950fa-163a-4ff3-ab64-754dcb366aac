package com.ruoyi.system.api.domain.entity.biz.business;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:17
 */
@Data
@TableName("business_callback_event")
public class BusinessCallbackEvent implements Serializable {
    private static final long serialVersionUID = 6673128864553344768L;


    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 回访ID (FK:business_callback.id)
     */
    @ApiModelProperty("回访ID")
    private Long callbackId;

    /**
     * 回访事件
     */
    @ApiModelProperty("回访事件")
    private Integer callbackEvent;

    /**
     * 最晚回访时间
     */
    @ApiModelProperty("最晚回访时间")
    private Date callbackTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
