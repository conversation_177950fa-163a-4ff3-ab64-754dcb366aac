package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/13 14:59
 */
@Data
public class PreselectModelShootAttentionDTO implements Serializable {
    private static final long serialVersionUID = 3820280566473356097L;

    /**
     * 预选模特ID
     */
    @ApiModelProperty("预选模特ID")
    @NotNull(message = "[预选模特ID]不能为空")
    private Long preselectModelId;

    /**
     * 拍摄模特注意事项
     */
    @ApiModelProperty("拍摄模特注意事项")
    @Size(max = 3500, message = "[拍摄模特注意事项]长度不能超过3500个字符")
    @Pattern(regexp = "^[^\\p{IsHan}]*$", message = "[拍摄模特注意事项]不能包含中文字符")
    private String shootAttention;

    /**
     * 拍摄注意事项对象存储键值
     */
    @ApiModelProperty("拍摄注意事项对象存储键值")
    private List<String> shootAttentionObjectKey;
}
