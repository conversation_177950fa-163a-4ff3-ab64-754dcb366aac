package com.ruoyi.system.api.domain.entity.biz.business;

import com.ruoyi.common.core.enums.BusinessValidityChangeReasonTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家会员有效期修改流水
 *
 * <AUTHOR>
 * @TableName business_member_validity_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessMemberValidityFlow implements Serializable {
    private static final long serialVersionUID = -336993560542566327L;

    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[商家ID]不能为空")
    @ApiModelProperty("商家ID")
    private Long businessId;

    @ApiModelProperty("订单号")
    private String orderNum;

    @NotNull(message = "[原会员有效期]不能为空")
    @ApiModelProperty("原会员有效期")
    private Date originMemberValidity;

    @NotNull(message = "[修改后会员有效期]不能为空")
    @ApiModelProperty("修改后会员有效期")
    private Date resultMemberValidity;

    @ApiModelProperty("加赠时间")
    private Integer presentedTime;

    @ApiModelProperty("加赠时间类型（1-天,2-月,3-年）")
    private Integer presentedTimeType;

    @ApiModelProperty("处理类型：0-系统调整，1-商家购买")
    private Integer type;

    @ApiModelProperty("购买会员次数")
    private Long rechargeCount;

    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberPackageType;

    @ApiModelProperty("订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty("币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    @ApiModelProperty("订单实付金额")
    private BigDecimal realPayAmount;

    @ApiModelProperty("修改原因类型:修改原因类型（1:老会员入驻,2:七天无理由,3:退会,4:其他）")
    @EnumValid(enumClass = BusinessValidityChangeReasonTypeEnum.class, message = "[修改原因类型]不合法")
    @NotNull(message = "[修改原因类型]不能为空")
    private Integer changeReasonType;

    @Size(max = 300, message = "编码长度不能超过300")
    @ApiModelProperty("修改原因")
    @Length(max = 300, message = "编码长度不能超过300")
    private String remark;

    @NotNull(message = "[处理人（sys_user.user_id）]不能为空")
    @ApiModelProperty("处理人（sys_user.user_id）")
    private Long createById;

    @NotNull(message = "[处理人（sys_user.user_id）]不能为空")
    @ApiModelProperty("处理人（sys_user.user_id）")
    private String createBy;

    @ApiModelProperty("处理时间")
    private Date createTime;
}
