package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReturnVisitRecordDTO implements Serializable {
    private static final long serialVersionUID = 1553365219832475738L;


    /**
     * 商家ID
     */
    @ApiModelProperty("商家ID")
    @NotNull(message = "[商家ID]不能为空")
    private Long businessId;

    /**
     * 回访事件
     */
    @ApiModelProperty("回访事件")
    private List<Integer> event;

    /**
     * 回访账号姓名
     */
    @ApiModelProperty("回访账号姓名")
    private List<String> accountName;

    /**
     * 反馈类型
     */
    @ApiModelProperty(value = "反馈类型（1:视频,2:客服,3:系统,4:其他）")
    private List<Integer> feedbackType;
}
