package com.ruoyi.system.api.domain.dto.biz.model;

import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import com.ruoyi.system.api.domain.dto.SectionDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/4 18:05
 */
@Data
public class ModelDataTableListDTO implements Serializable {
    private static final long serialVersionUID = -2466310097399012189L;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 数据范围时间开始
     */
    @ApiModelProperty(value = "数据范围时间开始")
    private Date dataScopeTimeBegin;

    /**
     * 数据范围时间结束
     */
    @ApiModelProperty(value = "数据范围时间结束")
    private Date dataScopeTimeEnd;

    /**
     * 创建时间开始
     */
    @ApiModelProperty(value = "创建时间开始")
    private Date createTimeBegin;

    /**
     * 创建时间结束
     */
    @ApiModelProperty(value = "创建时间结束")
    private Date createTimeEnd;

    /**
     * 排单数（1：0单,2：1-5单,3：6-10单,4：11-15单,5：16-20单,6：21单以上）
     */
    @ApiModelProperty(value = "排单数（1：0单,2：1-5单,3：6-10单,4：11-15单,5：16-20单,6：21单以上）")
    private List<Integer> orderScheduledCounts;

    /**
     * 排单数区间数组
     */
    @Null(message = "请勿传递[orderScheduledCountSections]")
    private List<SectionDTO> orderScheduledCountSections;

    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度（模特等级）(0:一般模特,1:优质模特,2:中度模特)")
    private List<Integer> cooperations;

    /**
     * 客服ID
     */
    @ApiModelProperty(value = "客服ID")
    private List<Long> serviceIds;

    /**
     * 开发人ID
     */
    @ApiModelProperty(value = "开发人ID")
    private List<Long> developerIds;

    /**
     * 模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)
     */
    @ApiModelProperty(value = "模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)")
    private List<Integer> statuses;

    /**
     * 适合平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "适合平台(0:Amazon,1:tiktok,2:其他)")
    private List<Integer> platforms;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型(0:影响者,1:素人)")
    private List<Integer> types;

    /**
     * 国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private List<Integer> nations;

    /**
     * 性别(1:男,0:女)
     */
    @ApiModelProperty(value = "性别(1:男,0:女)")
    private List<Integer> sexes;

    /**
     * 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
     */
    @ApiModelProperty(value = "年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）")
    private List<Integer> ageGroups;

    /**
     * 预警模特（1：预警，0：没预警）
     */
    @ApiModelProperty(value = "预警模特（1：预警，0：没预警）")
    private Integer isWarningModel;

    /**
     * 自定义查询列
     */
    @ApiModelProperty(value = "自定义查询列")
    private List<Integer> customColumns;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private Integer sortColumn;

    /**
     * 排序字段STR
     */
    @Null(message = "请勿传递[sortColumnStr]")
    private String sortColumnStr;

    /**
     * 排序方式（ASC,DESC）
     */
    @ApiModelProperty(value = "排序方式（ASC,DESC）")
    @EnumValid(enumClass = OrderByDto.DIRECTION.class, message = "请传递正确的排序方式", enumField = "value")
    private String sortWay;

    /**
     * 远程模特匹配单数据
     */
    @Null(message = "请勿传递[remoteListJson]")
    private String remoteListJson;
}
