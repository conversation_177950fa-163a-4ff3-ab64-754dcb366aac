package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/9 14:11
 */
@Data
@TableName("order_video_roast")
public class OrderVideoRoast implements Serializable {

    private static final long serialVersionUID = 1267567366503013629L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    @ApiModelProperty(value = "商家id")
    private Long businessId;

    @ApiModelProperty(value = "吐糟账号ID")
    private Long bizUserId;

    @ApiModelProperty(value = "吐槽用户名称")
    private String roastUserName;

    @ApiModelProperty(value = "吐槽用户微信名称")
    private String roastUserNickName;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    @Excel(name = "视频id")
    private Long videoId;

    @ApiModelProperty(value = "吐槽类型(0:视频吐槽,1:系统吐槽)")
    private Integer roastType;

    /**
     * 吐槽对象(1:视频,2:客服,3:其他)
     */
    @ApiModelProperty(value = "吐槽对象(1:视频,2:客服,3:其他)")
    @Excel(name = "吐槽对象(1:视频,2:客服,3:其他)")
    private Integer object;

    /**
     * 吐槽内容
     */
    @ApiModelProperty(value = "吐槽内容")
    @Excel(name = "吐槽内容")
    private String content;

    @ApiModelProperty(value = "中文部客服id")
    private Long contactId;

    @ApiModelProperty(value = "英文部客服id")
    private Long issueId;

    @ApiModelProperty(value = "处理状态（0:待处理,1:已处理）")
    private Integer handleStatus;

    @ApiModelProperty(value = "处理结果")
    private String handleResult;

    @ApiModelProperty(value = "处理时间")
    private Date handleTime;

    @ApiModelProperty(value = "处理人id")
    private Long handleUserId;

    @ApiModelProperty(value = "处理人")
    private String handleUserName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
