package com.ruoyi.system.api.domain.entity.order;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单_发票对象 order_invoice
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@ApiModel(value = "订单_发票对象 order_invoice")
@TableName("order_invoice")
@Data
public class OrderInvoice implements Serializable {
    private static final long serialVersionUID = -3598322964602597712L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 申票码
     */
    @ApiModelProperty(value = "申票码")
    private String ticketCode;

    /**
     * 来源（1：商家申请，2：红冲重开）
     */
    @ApiModelProperty(value = "来源（1：商家申请，2：红冲重开）")
    private Integer source;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 订单类型（0:视频订单,1:会员订单）
     */
    @ApiModelProperty(value = "订单类型（0:视频订单,1:会员订单）")
    private Integer orderType;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家id")
    private Long merchantId;

    /**
     * 商家编码
     */
    @ApiModelProperty(value = "商家编码")
    private String merchantCode;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废,5:待审核,6:已取消,7:已重开）
     */
    @ApiModelProperty(value = "开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废,5:待审核,6:已取消,7:已重开）")
    private Integer status;

    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date invoicingTime;

    /**
     * 发票号
     */
    @ApiModelProperty(value = "发票号")
    private String number;

    /**
     * 发票文件URI
     */
    @ApiModelProperty(value = "发票文件URI")
    private String objectKey;

    /**
     * 发票类型（1：增值税普通发票，2：形式发票）
     */
    @ApiModelProperty(value = "发票类型（1：增值税普通发票，2：形式发票）")
    private Integer invoiceType;

    /**
     * 抬头类型（1：企业单位）
     */
    @ApiModelProperty(value = "抬头类型（1：企业单位）")
    private Integer titleType;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    private String title;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    private String dutyParagraph;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    /**
     * 公司联系电话
     */
    @ApiModelProperty(value = "公司联系电话")
    private String companyPhone;

    /**
     * 公司联系人
     */
    @ApiModelProperty(value = "公司联系人")
    private String companyContact;

    /**
     * 附件URI
     */
    @ApiModelProperty(value = "附件URI")
    private String attachmentObjectKey;

    /**
     * 发票内容
     */
    @ApiModelProperty(value = "发票内容")
    private String content;

    /**
     * 发票备注
     */
    @ApiModelProperty(value = "发票备注")
    private String invoiceRemark;

    /**
     * 提交人姓名
     */
    @ApiModelProperty(value = "提交人姓名")
    private String submitBy;

    /**
     * 提交人ID
     */
    @ApiModelProperty(value = "提交人ID")
    private Long submitById;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    /**
     * 新发票标识（1：新发票，0：不是）
     */
    @ApiModelProperty(value = "新发票标识（1：新发票，0：不是）")
    private Integer isNew;

    /**
     * 操作人类型（0：运营，1：商家）
     */
    @ApiModelProperty(value = "操作人类型")
    private Integer operatorByType;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operatorById;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operatorTime;

    /**
     * 审核人ID
     */
    @ApiModelProperty(value = "审核人ID")
    private Long auditById;

    /**
     * 注意事项
     */
    @ApiModelProperty(value = "注意事项")
    private String cautions;

    /**
     * 能否申请重开（1：可以，0：不行）不以此字段判断能否申请重开，只在申请后的旧发票及新发票设置0，意为此发票已申请过
     */
    @ApiModelProperty(value = "能否申请重开（1：可以，0：不行）不以此字段判断能否申请重开，只在申请后的旧发票及新发票设置0，意为此发票已申请过")
    private Integer isApplyReopen;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 图片URI
     */
    @ApiModelProperty(value = "发票文件URI")
    @TableField(exist = false)
    private List<String> objectKeys;

    public void setObjectKey(String objectKey) {
        this.objectKey = objectKey;
        if (StrUtil.isNotBlank(objectKey)) {
            this.objectKeys = StrUtil.split(objectKey, StrUtil.COMMA);
        }
    }
}
