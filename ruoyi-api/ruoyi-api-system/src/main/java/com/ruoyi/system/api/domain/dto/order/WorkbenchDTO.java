package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :工作台请求参数
 * @create :2025-03-31 10:25
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WorkbenchDTO implements Serializable {
    private static final long serialVersionUID = 1134604720780496397L;

    @ApiModelProperty(value = "是否关于我：1-是")
    @Null(message = "不需要传[关于我]")
    private Integer isAboutMe;

    @ApiModelProperty(value = "登录账号ID")
    @Null(message = "登录账号ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "工作台角色类型：0-无,1-中文部,2-英文部,3-财务部,4-剪辑部")
    private Integer workbenchRoleType;


    public void setUserId(Long userId) {
        this.userId = userId;
        if (ObjectUtil.isNull(userId) || 1L == userId){
            return;
        }
        this.isAboutMe = StatusTypeEnum.YES.getCode();
    }
}
