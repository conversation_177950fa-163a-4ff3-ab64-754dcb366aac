package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 收款人账号信息
 */
@Data
public class OrderPayeeAccountConfigInfoDTO implements Serializable {
    private static final long serialVersionUID = 2446129501617926420L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "类型")
    private Integer type;


    /**
     * 状态 0-弃用 1-使用
     */
    @ApiModelProperty(value = "状态 0-弃用 1-使用")
    private Integer status;

    /**
     * 公司名称/账号名称
     */
    @ApiModelProperty(value = "公司名称/账号名称")
    private String accountName;

    /**
     * 收款账号名称/银行账号
     */
    @ApiModelProperty(value = "收款账号名称/银行账号")
    private String bankAccount;

    /**
     * 开户行名称/银行所在地
     */
    @ApiModelProperty(value = "开户行名称/银行所在地")
    private String bankName;

    /**
     * 收款账号类型
     */
    @ApiModelProperty(value = "收款账号类型")
    private String companyAccountType;

    /**
     * 银行代码
     */
    @ApiModelProperty(value = "银行代码")
    private String companyBankCode;

    /**
     * 分行代码
     */
    @ApiModelProperty(value = "分行代码")
    private String companyBankSubCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    private String companyBankName;

    /**
     * 银行地址
     */
    @ApiModelProperty(value = "银行地址")
    private String companyBankAddress;

    /**
     * 分行代码
     */
    @ApiModelProperty(value = "swiftCode")
    private String companyBankSwiftCode;

    /**
     * 收款人地址
     */
    @ApiModelProperty(value = "收款人地址")
    private String companyBankPayeeAddress;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    @ApiModelProperty(value = "详情id")
    private Long detailId;

}