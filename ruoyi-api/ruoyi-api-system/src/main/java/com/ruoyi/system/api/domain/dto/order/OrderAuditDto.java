package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.AuditStatusEnum;
import com.ruoyi.common.core.enums.PayTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :订单审核
 * @create :2024-06-18 17:47
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderAuditDto implements Serializable {
    private static final long serialVersionUID = 1133739788225559530L;

    @ApiModelProperty(value = "订单id")
    @NotNull(message = "订单id不能为空")
    private Long id;

    @ApiModelProperty(value = "支付单号")
    private String payNum;

    @ApiModelProperty(value = "实付人民币")
    @PositiveOrZero(message = "[实付人民币]必须大于等于0")
    @DecimalMax(value = "999999.99", message = "[实付人民币]不能大于999999.99")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "实付金额")
    @PositiveOrZero(message = "[实付金额]必须大于等于0")
    @DecimalMax(value = "999999.99", message = "[实付金额]不能大于999999.99")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty(value = "订单备注")
    @NotBlank(message = "[订单备注]不能为空")
    @Size(max = 300, message = "[订单备注]长度不能超过300")
    private String orderRemark;

    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty(value = "币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;


    @ApiModelProperty("支付方式(1:微信,2:支付宝支付,6:对公转账,7-全币种)")
    @EnumValid(enumClass = PayTypeEnum.class, message = "[支付方式]输入错误")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    @EnumValid(enumClass = PayTypeEnum.PayTypeDetailEnum.class, message = "[支付方式明细]输入错误")
    private Integer payTypeDetail;

    @ApiModelProperty("收款配置Id")
    private Long payeeId;

    @ApiModelProperty(value = "审核状态")
    @NotNull(message = "审核状态不能为空")
    @EnumValid(enumClass = AuditStatusEnum.class, message = "[审核状态]输入错误")
    private Integer auditStatus;

    @ApiModelProperty(value = "上传凭证id列表")
    private List<String> objectKeys;

    @AssertTrue(message = "[实付金额]不能为空")
    private boolean isFullCurrency() {
        if (ObjectUtil.isNotNull(payType) && PayTypeEnum.FULL_CURRENCY.getCode().equals(payType)) {
            return ObjectUtil.isNotNull(realPayAmountCurrency);
        }
        return true;
    }

    @AssertTrue(message = "[审核状态]输入错误")
    private boolean isAuditStatus() {
        return List.of(AuditStatusEnum.APPROVE.getCode(), AuditStatusEnum.EXCEPTION.getCode()).contains(auditStatus);
    }

    @AssertTrue(message = "选择全币种支付时，[支付方式明细]不能为空")
    private boolean isPayTypeDetail() {
        if (PayTypeEnum.FULL_CURRENCY.getCode().equals(payType) || PayTypeEnum.FULL_CURRENCY_BALANCE.getCode().equals(payType)) {
            return ObjectUtil.isNotNull(payTypeDetail);
        } else {
            return ObjectUtil.isNull(payTypeDetail);
        }
    }
}
