package com.ruoyi.system.api.domain.dto.order.logistic;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 物流跟进表
 *
 * <AUTHOR>
 * @TableName order_video_logistic_follow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoLogisticFollowListDTO implements Serializable {
    private static final long serialVersionUID = -371233792383242062L;


    @ApiModelProperty("物流关联表id order_video_logistic.id")
    private Long orderVideoLogisticId;

    @ApiModelProperty("商家id")
    private Long businessId;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty("会员编码列表")
    private List<String> memberCodes;

    @ApiModelProperty("视频编码")
    private String videoCode;

    @ApiModelProperty("视频Id")
    private Long videoId;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty(value = "与我相关")
    private String aboutMe;

    @ApiModelProperty(value = "对接人id")
    private Long contactId;

    private Long backUserId;

    @ApiModelProperty("物流单号")
    private String number;

    @ApiModelProperty("处理状态枚举(0-未通知、1-已通知、2-延迟发货、3-延迟发货提醒、4-延迟发货已提醒、5-催发货提醒、6-催发货已提醒、7-地址变更通知、8-变更已通知、9-补充说明、10-标记发货、11-标记发货提醒、12-标记发货已提醒、13-通知确认模特、14-已通知确认模特、15-催确认模特提醒、16-已通知催确认模特)")
    private Integer handleStatus;

    @ApiModelProperty("处理状态枚举(0-未通知、1-已通知、2-延迟发货、3-延迟发货提醒、4-延迟发货已提醒、5-催发货提醒、6-催发货已提醒、7-地址变更通知、8-变更已通知、9-补充说明、10-标记发货、11-标记发货提醒、12-标记发货已提醒、13-通知确认模特、14-已通知确认模特、15-催确认模特提醒、16-已通知催确认模特)")
    private List<Integer> handleStatusList;

    @ApiModelProperty("物流状态(0-未发货、1-已发货)")
    private Integer logisticStatus;

    @ApiModelProperty("跟进状态码(1-需处理、2-暂不处理、10-已发货、11-需跟进、12-模特待确认、13-无需跟进、14-已结束)")
    private Integer followStatus;

    @ApiModelProperty("模特结果枚举(0-待处理、1-已询问、2-已收货、3-丢件、4-订单回退、5-待通知拍摄、6-已通知拍摄)")
    private Integer modelResult;

    @ApiModelProperty("物流更新时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date logisticUpdateTimeEnd;

    @ApiModelProperty("物流更新时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date logisticUpdateTimeStart;

    @ApiModelProperty(value = "物流主状态（1:查询不到,2:收到信息,3:运输途中,4:运输过久,5:到达待取,6:派送途中,7:投递失败,8:成功签收,9:可能异常）")
    private List<Integer> logisticMainStatus;

    private List<String> mainStatusLabels;

    @ApiModelProperty(value = "对接人id")
    private List<Long> contactIds = new ArrayList<>();

    @ApiModelProperty(value = "出单人id")
    private List<Long> issueIds;

    @ApiModelProperty(value = "拍摄模特ID")
    private Long shootModelId;

    @ApiModelProperty(value = "列表")
    @Null(message = "请勿传递[shootModelIdList]")
    private List<Long> shootModelIdList;

    @ApiModelProperty(value = "拍摄模特包含家庭成员:1-包含、0-不包含")
    private Integer includeFamily;

    private List<Long> shootModelIdsByKeyword;
    private List<Long> contactIdsByKeyword;

    @ApiModelProperty(value = "排序类型：1-物流更新时间，2-最后更新时间")
    private Integer orderByType;

    @ApiModelProperty(value = "是否正序排序")
    private Integer isAsc;

    @AssertTrue(message = "需要包含家庭成员时，拍摄模特不能为空")
    private boolean isIncludeFamily() {
        if (ObjectUtil.isNotNull(includeFamily) && StatusTypeEnum.YES.getCode().equals(includeFamily)) {
            return ObjectUtil.isNotNull(shootModelId);
        }
        return true;
    }

}
