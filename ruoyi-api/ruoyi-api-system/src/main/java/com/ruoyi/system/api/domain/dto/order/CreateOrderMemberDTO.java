package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.PackageTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-24 16:58
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateOrderMemberDTO implements Serializable {
    private static final long serialVersionUID = 2164650302235188547L;

    @NotNull(message="[套餐类型]不能为空")
    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    @EnumValid(enumClass = PackageTypeEnum.class, message = "[套餐类型]输入错误")
    private Integer packageType;
}
