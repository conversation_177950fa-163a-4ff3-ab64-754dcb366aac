package com.ruoyi.system.api.domain.dto.biz.business.balance;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 商家预付表
 *
 * <AUTHOR>
 * @TableName business_balance_prepay
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalancePrepayListDTO implements Serializable {

    private static final long serialVersionUID = -981735608546630181L;

    @ApiModelProperty("审核状态（0:待处理,1:已审核,2.审核拒绝）")
    private Integer auditStatus;

    @ApiModelProperty("订单类型（2-线下钱包充值，3-线上钱包充值）")
    private Integer orderType;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "预付单号列表")
    private List<String> prepayNums;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private List<Integer> payTypeDetails;
}
