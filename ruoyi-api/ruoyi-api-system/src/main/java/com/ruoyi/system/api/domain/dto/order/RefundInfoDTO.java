package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.RefundTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/20 15:25
 */
@Data
public class RefundInfoDTO implements Serializable {
    private static final long serialVersionUID = -7323807024468635785L;
    /**
     * 视频订单id
     */
    @NotNull(message = "[视频订单id]不能为空")
    @ApiModelProperty(value = "视频订单id", required = true)
    private Long videoId;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @NotNull(message = "[退款类型]不能为空")
    @ApiModelProperty(value = "退款类型（1:补偿,2:取消订单,3:取消选配）", notes = "1:补偿,2:取消订单,3:取消选配", required = true)
    @EnumValid(enumClass = RefundTypeEnum.class, message = "[退款类型]输入错误")
    private Integer refundType;
}
