package com.ruoyi.system.api.domain.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :订单会员表
 * @create :2024-06-24 16:04
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderMemberListDTO implements Serializable {

    private static final long serialVersionUID = 7784239384862661024L;

    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "商家微信昵称")
    private String nickName;

    @ApiModelProperty(value = "商家账号")
    private String businessAccount;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer packageType;

    @ApiModelProperty(value = "商家ID列表")
    private Set<Long> businessIds;

    @ApiModelProperty(value = "订单状态列表")
    private Set<Integer> statusList;

    @ApiModelProperty(value = "财务审核状态列表")
    private Set<Integer> auditStatusList;

    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额）")
    private Set<Integer> payTypes;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private List<Integer> payTypeDetails;

    @ApiModelProperty(value = "下单时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeBegin;

    @ApiModelProperty(value = "下单时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeEnd;

    @ApiModelProperty(value = "支付时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTimeBegin;

    @ApiModelProperty(value = "支付时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTimeEnd;

    @ApiModelProperty(value = "登录账号id列表")
    private List<Long> bizUserIds;

    @ApiModelProperty(value = "关键词")
    private String searchName;

    @ApiModelProperty(value = "关键词查询到的商家ID列表")
    private Set<Long> searchBusinessIds;

    @ApiModelProperty(value = "是否应收审批")
    private Integer isReceivableAudit;

    @ApiModelProperty(value = "是否过滤关闭状态")
    private Integer isFilterClose;

    @ApiModelProperty(value = "是否首次购买1:首次2:续费")
    private Integer isFirstBuy;

    /**
     * 活动类型（-1：无优惠，2：会员订单临期续费半价优惠，3：种草码优惠）
     */
    @ApiModelProperty(value = "活动类型（-1：无优惠，2：会员订单临期续费半价优惠，3：渠道优惠，5：裂变优惠）")
    private List<Integer> promotionActivityTypes;

    public void setMemberCode(String memberCode) {
        this.memberCode = memberCode != null ? memberCode.toUpperCase() : null;
    }

}

