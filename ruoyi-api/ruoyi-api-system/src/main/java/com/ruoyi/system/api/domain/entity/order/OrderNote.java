package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单_商家开票信息对象 order_note
 *
 * <AUTHOR>
 * @date 2024-06-22
 */
@ApiModel(value = "订单_商家开票信息对象 order_note")
@TableName("order_note")
@Data
public class OrderNote implements Serializable {

    private static final long serialVersionUID = -3891031332724129349L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 订单编号
     */
    @NotNull(message = "[订单编号]不能为空")
    @ApiModelProperty(value = "订单编号", required = true)
    @Excel(name = "订单编号")
    private String orderNum;

    /**
     * 发票抬头
     */
    @NotNull(message = "[发票抬头]不能为空")
    @ApiModelProperty(value = "发票抬头", required = true)
    @Excel(name = "发票抬头")
    private String title;

    /**
     * 税号
     */
    @NotNull(message = "[税号]不能为空")
    @ApiModelProperty(value = "税号", required = true)
    @Excel(name = "税号")
    private String dutyParagraph;

    /**
     * 发票内容
     */
    @NotNull(message = "[发票内容]不能为空")
    @ApiModelProperty(value = "发票内容", required = true)
    @Excel(name = "发票内容")
    private String content;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
