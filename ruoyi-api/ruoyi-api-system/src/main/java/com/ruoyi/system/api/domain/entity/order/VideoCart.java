package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/7 15:20
 */
@ApiModel(value = "购物车对象 video_cart")
@TableName("video_cart")
@Data
public class VideoCart implements Serializable {
    private static final long serialVersionUID = 510898267681793942L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类", required = true)
    @NotNull(message = "[使用平台]不能为空")
    private Integer platform;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）", notes = "1:横屏16：9,2:竖屏9：16", required = true)
    @NotNull(message = "[视频格式]不能为空")
    private Integer videoFormat;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国", required = true)
    @NotNull(message = "[拍摄国家]不能为空")
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人", required = true)
    @NotNull(message = "[模特类型]不能为空")
    private Integer modelType;

    /**
     * 产品中文名
     */
    @NotNull(message = "[产品中文名]不能为空")
    @Size(max = 30, message = "[产品中文名]不能超过30个字符")
    @ApiModelProperty(value = "产品中文名", required = true)
    private String productChinese;

    /**
     * 产品英文名
     */
    @NotNull(message = "[产品英文名]不能为空")
    @Size(max = 50, message = "[产品中文名]不能超过50个字符")
    @ApiModelProperty(value = "产品英文名", required = true)
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 意向模特id
     */
    @ApiModelProperty(value = "意向模特id")
    private Long intentionModelId;

    /**
     * 参考视频链接
     */
    @ApiModelProperty(value = "参考视频链接")
    private String referenceVideoLink;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）", notes = "1:2张/$10,2:5张/$20")
    private Integer picCount;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    @TableField(value = "reference_pic")
    private String referencePicId;

    /**
     * 视频金额（单位：$）
     */
    @ApiModelProperty(value = "视频金额（单位：$）")
    private BigDecimal amount;

    /**
     * 视频价格（单位：$）
     */
    @ApiModelProperty(value = "视频价格（单位：$）")
    private BigDecimal videoPrice;

    /**
     * 图片费用（单位：$）
     */
    @ApiModelProperty(value = "图片费用（单位：$）")
    private BigDecimal picPrice;

    /**
     * 佣金代缴税费（单位：$）
     */
    @ApiModelProperty(value = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    /**
     * 手续费（单位：$）
     */
    @ApiModelProperty(value = "手续费（单位：$）")
    private BigDecimal exchangePrice;

    /**
     * 服务费用（单位：$）
     */
    @ApiModelProperty(value = "服务费用（单位：$）")
    private BigDecimal servicePrice;

    /**
     * 创建订单商家id
     */
    @ApiModelProperty(value = "创建订单商家id")
    private Long createOrderBusinessId;

    /**
     * 创建订单登录用户id
     */
    @ApiModelProperty(value = "创建订单登录用户id")
    private Long createOrderBizUserId;

    /**
     * 创建订单用户id
     */
    @ApiModelProperty(value = "创建订单用户id")
    private Long createOrderUserId;

    /**
     * 创建订单用户账号
     */
    @ApiModelProperty(value = "创建订单用户账号")
    private String createOrderUserAccount;

    /**
     * 创建订单用户名称
     */
    @ApiModelProperty(value = "创建订单用户名称")
    private String createOrderUserName;

    /**
     * 创建订单用户微信名称
     */
    @ApiModelProperty(value = "创建订单用户微信名称")
    private String createOrderUserNickName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
