package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/21 13:44
 */
@Data
public class AgreeRefundDTO implements Serializable {

    private static final long serialVersionUID = 4461808599089598464L;
    @ApiModelProperty(value = "id", required = true)
    @NotEmpty(message = "[id]不能为空")
    private List<Long> id;

    @ApiModelProperty(value = "备注", required = true)
    @Size(max = 500, message = "[备注]长度不能超过500个字符")
    private String remark;
}
