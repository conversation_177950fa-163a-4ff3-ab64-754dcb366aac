package com.ruoyi.system.api.domain.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发票导出对应字段
 *
 * <AUTHOR>
 * @date 2024/6/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InvoiceExportDTO implements Serializable {
    private static final long serialVersionUID = 6901901884330802400L;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    @Excel(name = "关联订单")
    private String orderNum;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 订单类型（0:视频订单,1:会员订单）
     */
    @ApiModelProperty(value = "订单类型（0:视频订单,1:会员订单）")
    @Excel(name = "订单类型", readConverterExp = "0=视频订单,1=会员订单")
    private Integer type;

    /**
     * 会员编码
     */
    @ApiModelProperty(value = "会员编码")
    @Excel(name = "会员编码")
    private String merchantCode;

    /**
     * 对接客服
     */
    @ApiModelProperty(value = "对接客服")
    @Excel(name = "对接客服")
    private String waiterUserName;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    @Excel(name = "发票抬头")
    private String title;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    @Excel(name = "税号")
    private String dutyParagraph;

    /**
     * 发票号
     */
    @ApiModelProperty(value = "发票号")
    @Excel(name = "发票号")
    private String number;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    @Excel(name = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 开票状态（1:待开票,2:待确认,3:已投递,4:已作废）
     */
    @ApiModelProperty(value = "开票状态（1:待开票,2:待确认,3:已投递,4:已作废）", notes = "1:待开票,2:待确认,3:已投递,4:已作废")
    @Excel(name = "开票状态", readConverterExp = "1=待开票,2=待确认,3=已投递")
    private Integer status;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    @Excel(name = "操作人")
    private String operator;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operatorTime;
}
