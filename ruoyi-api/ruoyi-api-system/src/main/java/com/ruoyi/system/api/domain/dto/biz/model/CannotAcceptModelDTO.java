package com.ruoyi.system.api.domain.dto.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :无法接单模特dto
 * @create :2025-01-09 17:07
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CannotAcceptModelDTO implements Serializable {
    private static final long serialVersionUID = -6394631127478710830L;

    @ApiModelProperty("主键Id")
    private Collection<Long> modelId;

    @ApiModelProperty("登录账号Id")
    private Long bizUserId;
}
