package com.ruoyi.system.api.domain.entity.order.datastatistics;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2025-05-16 09:33:32
 */
@Data
@TableName("customer_service_data_statistics_month")
public class CustomerServiceDataStatisticsMonth implements Serializable {
    private static final long serialVersionUID = 8700759280311386811L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 记录时间-开始
     */
    @ApiModelProperty(value = "记录时间-开始")
    private Date writeTimeBegin;

    /**
     * 记录时间-结束
     */
    @ApiModelProperty(value = "记录时间-结束")
    private Date writeTimeEnd;

    /**
     * 英文部客服新增/淘汰模特数量JSON
     */
    @ApiModelProperty(value = "英文部客服新增/淘汰模特数量JSON")
    private String englishCustomerServiceAddedOustModelCountJson;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
