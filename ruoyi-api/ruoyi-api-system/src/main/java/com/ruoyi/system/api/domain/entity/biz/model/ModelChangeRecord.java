package com.ruoyi.system.api.domain.entity.biz.model;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/11 17:02
 */
@Data
@TableName("model_change_record")
public class ModelChangeRecord implements Serializable {

    private static final long serialVersionUID = -7342336170037136995L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模特id
     */
    @ApiModelProperty("模特id")
    private Long modelId;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private Date operateTime;

    /**
     * 操作对象（1:商家,2:运营,3:模特,9:系统）
     */
    @ApiModelProperty("操作对象（1:商家,2:运营,3:模特,9:系统）")
    private Integer operateObject;

    /**
     * 操作人id
     */
    @ApiModelProperty("操作人id")
    private Long operateUserId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty("操作人姓名")
    private String operateUserName;

    /**
     * 操作类型（1:新增模特,2:修改模特信息,3:变更状态）
     */
    @ApiModelProperty("操作类型（1:新增模特,2:修改模特信息,3:变更状态）")
    private Integer operateType;

    /**
     * 操作详情
     */
    @ApiModelProperty("操作详情")
    private String operateDetail;

    /**
     * 操作说明
     */
    @ApiModelProperty("操作说明")
    private String operateExplain;

    /**
     * 变更状态前模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)
     */
    @ApiModelProperty("变更状态前模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)")
    private Integer stateBeforeChange;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
