package com.ruoyi.system.api.domain.dto.order.casus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
* 案例分组表
* <AUTHOR>
 * @TableName case_group
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CasusGroupSaveDTO implements Serializable {

    private static final long serialVersionUID = 1167203794075966757L;

    @NotBlank(message="[分组名称]不能为空")
    @Size(max= 32,message="[分组名称]编码长度不能超过32")
    @Size(min= 2,message="[分组名称]编码长度小于2")
    @ApiModelProperty(value = "分组名称", required = true)
    @Length(max= 32,message="[分组名称]编码长度不能超过32")
    @Length(min= 2,message="[分组名称]编码长度小于2")
    private String name;

    @NotNull(message="[平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类]不能为空")
    @ApiModelProperty(value = "平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类", required = true)
    private Integer platform;
}
