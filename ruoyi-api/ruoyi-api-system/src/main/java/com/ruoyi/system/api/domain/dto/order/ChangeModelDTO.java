package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ChangeModelDTO implements Serializable {
    private static final long serialVersionUID = 8210800648621234946L;
    @ApiModelProperty(value = "视频id")
    @NotNull(message = "[视频id]不能为空")
    private Long id;
    @ApiModelProperty(value = "模特Id")
    private Long modelId;
    @ApiModelProperty(value = "原因")
    @NotBlank(message = "[原因]不能为空")
    private String reason;
}
