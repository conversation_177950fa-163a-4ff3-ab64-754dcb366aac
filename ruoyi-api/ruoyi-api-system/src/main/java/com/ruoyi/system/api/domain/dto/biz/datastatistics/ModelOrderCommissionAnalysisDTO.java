package com.ruoyi.system.api.domain.dto.biz.datastatistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/9 10:32
 */
@Data
public class ModelOrderCommissionAnalysisDTO implements Serializable {
    private static final long serialVersionUID = -690670069952499943L;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 模特合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "模特合作深度(0:一般模特,1:优质模特,2:中度模特)")
    private Integer modelCooperation;
}
