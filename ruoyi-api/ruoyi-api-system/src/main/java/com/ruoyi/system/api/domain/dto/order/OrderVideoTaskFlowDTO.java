package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.OrderTaskStatusEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-11 16:48
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoTaskFlowDTO implements Serializable {
    private static final long serialVersionUID = -5349374037999954445L;

    @ApiModelProperty("选择任务单流转状态")
    @NotNull(message = "[选择任务单流转状态]不能为空")
    private OrderTaskStatusEnum orderTaskStatus;

    @ApiModelProperty("任务单数据")
    @NotNull(message = "[任务单数据]不能为空")
    private OrderVideoTaskDetail orderVideoTaskDetail;

}
