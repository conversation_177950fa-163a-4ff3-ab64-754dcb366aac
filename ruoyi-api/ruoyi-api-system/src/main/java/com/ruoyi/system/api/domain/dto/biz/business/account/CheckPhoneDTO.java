package com.ruoyi.system.api.domain.dto.biz.business.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-28 13:56
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value="检查手机号")
public class CheckPhoneDTO implements Serializable {
    private static final long serialVersionUID = -3907830783615103787L;

    @ApiModelProperty("手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式有误")
    @NotBlank(message = "手机号不为空")
    private String phone;

    private String ticket;
}
