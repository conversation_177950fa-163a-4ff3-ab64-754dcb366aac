package com.ruoyi.system.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 新增模特关联订单
 *
 * <AUTHOR>
 * @date 2024/6/14 14:16
 */
@Data
public class ModelOrderDTO implements Serializable {
    private static final long serialVersionUID = 3917511722159795988L;
    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id")
    private Long modelId;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    private Long videoId;

    /**
     * 接单时间
     */
    @ApiModelProperty(value = "接单时间")
    private Date acceptTime;
}
