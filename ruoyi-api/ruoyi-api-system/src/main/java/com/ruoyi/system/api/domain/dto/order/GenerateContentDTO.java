package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.FontAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/3 13:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GenerateContentDTO {
    /**
     * 内容
     */
    private String content;

    /**
     * 左边距
     */
    private Integer left;

    /**
     * 上边距
     */
    private Integer top;

    /**
     * 下边距
     */
    private Integer bottom;

    /**
     * 右边距
     */
    private Integer right;

    /**
     * 字体
     */
    private String font;

    /**
     * 字体大小
     */
    private Integer fontSize;

    /**
     * 字体颜色
     */
    private String color;

    /**
     * 字体对齐方式 center-居中 left-左对齐
     */
    private FontAlignmentEnum alignment;

    /**
     * 文本框宽度
     */
    private Integer textboxWidth;

    /**
     * 文本框高度
     */
    private Integer textboxHeight;

    public GenerateContentDTO(Integer left, Integer top, Integer bottom, Integer right, String font, Integer fontSize, String color, FontAlignmentEnum alignment, Integer textboxWidth, Integer textboxHeight) {
        this.left = left;
        this.top = top;
        this.bottom = bottom;
        this.right = right;
        this.font = font;
        this.fontSize = fontSize;
        this.color = color;
        this.alignment = alignment;
        this.textboxWidth = textboxWidth;
        this.textboxHeight = textboxHeight;
    }

    public GenerateContentDTO(Integer left, Integer top, Integer bottom, Integer right, Integer textboxWidth, Integer textboxHeight) {
        this.left = left;
        this.top = top;
        this.bottom = bottom;
        this.right = right;
        this.textboxWidth = textboxWidth;
        this.textboxHeight = textboxHeight;
    }
}
