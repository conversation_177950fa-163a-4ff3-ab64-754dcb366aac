package com.ruoyi.system.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :视频订单统计
 * @create :2024-06-25 09:57
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "视频订单统计dto")
@Builder
public class OrderVideoStatisticsDTO implements Serializable {
    private static final long serialVersionUID = 3832096627784909821L;

    /**
     * 时间-开始
     */
    @ApiModelProperty(value = "时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timeBegin;
    /**
     * 时间-结束
     */
    @ApiModelProperty(value = "时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timeEnd;

    @ApiModelProperty(value = "订单进入待确认的时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date unConfirmTimeBegin;
    /**
     * 时间-结束
     */
    @ApiModelProperty(value = "订单进入待确认的时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date unConfirmTimeEnd;

    @ApiModelProperty(value = "订单进入待确认的时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTimeBegin;
    /**
     * 时间-结束
     */
    @ApiModelProperty(value = "订单进入待确认的时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTimeEnd;

    @ApiModelProperty(value = "商家id列表")
    private List<Long> businessIds;

    @ApiModelProperty(value = "订单状态列表")
    private List<Integer> statusList;


}
