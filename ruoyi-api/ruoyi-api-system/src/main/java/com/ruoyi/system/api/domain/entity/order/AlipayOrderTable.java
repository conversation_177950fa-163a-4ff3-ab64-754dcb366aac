package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/24 18:12
 */
@Data
@TableName("alipay_order_table")
public class AlipayOrderTable implements Serializable {
    private static final long serialVersionUID = 3915476182775271662L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * appId
     */
    private String appId;

    /**
     * 商户订单号
     */
    private String mchntOrderNo;

    /**
     * 内部订单号
     */
    private String orderNum;

    /**
     * 跳转页面数据
     */
    private String pageRedirectionData;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 是否有效（1-有效， 0-无效）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;
}
