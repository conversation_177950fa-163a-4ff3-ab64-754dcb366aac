package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.RoastObjectEnum;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/27
 */
@Data
public class OrderVideoRoastDTO implements Serializable {

    private static final long serialVersionUID = 6220716850654606270L;
    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    @NotNull(message = "[视频id]不能为空", groups = CommonValidatedGroup.SaveValidatedGroup.class)
    private Long videoId;

    /**
     * 吐槽对象(1:视频,2:客服,3:其他)
     */
    @ApiModelProperty(value = "吐槽对象(1:视频,2:客服,3:其他)")
    @EnumValid(enumClass = RoastObjectEnum.class, message = "[吐槽对象]输入错误")
    private Integer object;

    /**
     * 吐槽内容
     */
    @ApiModelProperty(value = "吐槽内容")
    @Size(max = 300, message = "[吐槽内容]长度不能超过300个字符")
    private String content;

    @ApiModelProperty(value = "吐槽用户名称")
    @Null(message = "请勿传递[吐槽用户名称]")
    private String roastUserName;

    @ApiModelProperty(value = "吐槽用户微信名称")
    @Null(message = "请勿传递[吐槽用户微信名称]")
    private String roastUserNickName;

    @ApiModelProperty(value = "商家id")
    @Null(message = "请勿传递[商家id]")
    private Long businessId;

    @ApiModelProperty(value = "吐糟账号ID")
    @Null(message = "请勿传递[吐糟账号ID]")
    private Long bizUserId;

    @ApiModelProperty(value = "吐槽类型")
    @Null(message = "请勿传递[吐槽类型]")
    private Integer roastType;

}
