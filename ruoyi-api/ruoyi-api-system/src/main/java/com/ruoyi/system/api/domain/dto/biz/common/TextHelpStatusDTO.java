package com.ruoyi.system.api.domain.dto.biz.common;

import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-11-20 15:09
 **/
@Data
public class TextHelpStatusDTO implements Serializable {
    private static final long serialVersionUID = -5808820033177101174L;

    @NotNull(message = "[主键]不能为空")
    private Long id;

    @NotNull(message = "[状态不能为空]不能为空")
    @EnumValid(enumClass = StatusEnum.class, message = "[帮助状态]输入错误")
    private Integer status;
}
