package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.MaterialInfoStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/10 15:11
 */
@Data
public class UploadLinkDTO implements Serializable {
    private static final long serialVersionUID = -1858871184006100393L;

    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id", required = true)
    @NotNull(message = "[视频订单id]不能为空")
    private Long videoId;

    /**
     * 任务详情ID
     */
    @ApiModelProperty(value = "任务详情ID")
    private Long taskDetailId;

    /**
     * 视频链接
     */
    @ApiModelProperty(value = "视频链接", required = true)
    @NotBlank(message = "[视频链接]不能为空")
    @Pattern(regexp = "^https://.*$", message = "[视频链接]格式不正确")
    @Size(max = 1000, message = "[视频链接]长度不能超过1000个字符")
    private String link;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 300, message = "[备注]长度不能超过300个字符")
    private String note;

    /**
     * 反馈素材id
     */
    @ApiModelProperty(value = "反馈素材id")
    private Long materialId;

    /**
     * 关联任务ID
     */
    @ApiModelProperty(value = "关联任务ID")
    private List<Long> taskDetailIds;

    /**
     * 拍摄模特ID
     */
    @Null(message = "请勿传递[shootModelId]")
    private Long shootModelId;

    /**
     * 模特反馈素材详情状态
     */
    @Null(message = "请勿传递[materialInfoStatusEnum]")
    private MaterialInfoStatusEnum materialInfoStatusEnum;
}
