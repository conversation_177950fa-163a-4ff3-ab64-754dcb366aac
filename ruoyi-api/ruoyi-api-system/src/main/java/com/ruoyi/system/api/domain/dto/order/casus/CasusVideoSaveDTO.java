package com.ruoyi.system.api.domain.dto.order.casus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
* 案例视频表
* <AUTHOR>
 * @TableName case_video
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CasusVideoSaveDTO implements Serializable {

    private static final long serialVersionUID = -6674922225769613902L;

    @NotBlank(message = "[视频名称]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty(value = "视频名称", required = true)
    @Length(max = 32, message = "编码长度不能超过32")
    private String name;

    @NotBlank(message = "[封面图片]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty(value = "封面图片", required = true)
    @Length(max = 255, message = "编码长度不能超过255")
    private String pic;

    @NotBlank(message = "[视频链接]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty(value = "视频链接", required = true)
    @Length(max = 255, message = "编码长度不能超过255")
    private String link;

    @NotNull(message = "[平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类]不能为空")
    @ApiModelProperty(value = "平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类", required = true)
    private Integer platform;
}

