package com.ruoyi.system.api.domain.dto.biz.tag;

import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 标签入参对象
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@ApiModel(value = "标签入参对象")
@Data
public class TagDTO implements Serializable {
    private static final long serialVersionUID = 2497355281400311612L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "[主键]不能为空", groups = CommonValidatedGroup.EditValidatedGroup.class)
    private Long id;

    /**
     * 父标签id
     */
    @ApiModelProperty(value = "父标签id")
    private Long parentId = 0L;

    /**
     * 分类id
     */
    @ApiModelProperty(value = "分类id（1009:模特标签,1008:类目标签）")
    private Long categoryId;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称", required = true)
    @NotBlank(message = "[标签名称]不能为空")
    @Size(max = 50, message = "[标签名称]长度不能超过50个字符")
    private String name;

    /**
     * 状态（0:启用,1:禁用）
     */
    @ApiModelProperty(value = "状态（0:启用,1:禁用）", required = true)
    @NotNull(message = "[状态]不能为空")
    @EnumValid(enumClass = StatusEnum.class, message = "[状态]输入错误")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 32, message = "[备注]长度不能超过32个字符")
    private String remark;
}
