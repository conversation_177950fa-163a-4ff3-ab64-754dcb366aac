package com.ruoyi.system.api.domain.dto.biz.aliyun;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-01-06 17:24
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PhoneWithTokenDTO implements Serializable {
    private static final long serialVersionUID = -422253122443121064L;

    @NotBlank(message = "[spToken]不能为空")
    @ApiModelProperty("spToken")
    private String spToken;
}
