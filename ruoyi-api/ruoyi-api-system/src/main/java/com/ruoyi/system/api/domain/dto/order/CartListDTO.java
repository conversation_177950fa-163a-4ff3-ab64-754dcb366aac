package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 购物车列表条件入参
 *
 * <AUTHOR>
 * @date 2024/08/01
 */
@Data
public class CartListDTO implements Serializable {
    private static final long serialVersionUID = -8634301686854900792L;
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 平台
     */
    @ApiModelProperty(value = "平台")
    private List<Long> platforms;

    /**
     * 模特id
     */
    @Null(message = "请勿传递[modelIds]参数")
    private Collection<Long> modelIds;

    @ApiModelProperty(value = "下单运营")
    private List<String> userName;
}
