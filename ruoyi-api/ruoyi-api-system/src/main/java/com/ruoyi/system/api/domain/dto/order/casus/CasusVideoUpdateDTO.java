package com.ruoyi.system.api.domain.dto.order.casus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
* 案例视频表
* <AUTHOR>
 * @TableName case_video
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CasusVideoUpdateDTO implements Serializable {

    private static final long serialVersionUID = 3829202046537531601L;
    @NotNull(message="[主键id]不能为空")
    @ApiModelProperty(value = "主键id", required = true)
    private Long id;

    @NotBlank(message="[视频名称]不能为空")
    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty(value = "视频名称", required = true)
    @Length(max= 32,message="编码长度不能超过32")
    private String name;

    @NotBlank(message="[封面图片]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty(value = "封面图片", required = true)
    @Length(max= 255,message="编码长度不能超过255")
    private String pic;

    @NotBlank(message="[视频链接]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty(value = "视频链接", required = true)
    @Length(max= 255,message="编码长度不能超过255")
    private String link;

}
