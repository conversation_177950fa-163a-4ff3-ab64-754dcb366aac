package com.ruoyi.system.api.domain.dto.biz.business;

import com.ruoyi.common.core.enums.BusinessCallbackFeedbackTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/13 14:43
 */
@Data
public class WriteReturnVisitDTO {

    /**
     * 回访ID
     */
    @ApiModelProperty(value = "回访ID", required = true)
    @NotNull(message = "[回访ID]不能为空")
    private Long id;

    /**
     * 回访账号ID
     */
    @ApiModelProperty(value = "回访账号ID", required = true)
    @NotNull(message = "[回访账号]不能为空")
    private Long accountId;

    /**
     * 反馈类型
     */
    @ApiModelProperty(value = "反馈类型（1:视频,2:客服,3:系统,4:其他）", required = true)
    @NotEmpty(message = "[回访账号]不能为空")
    @Size(min = 1, max = 4, message = "[反馈类型]可选数量为1~4")
    @EnumValid(enumClass = BusinessCallbackFeedbackTypeEnum.class, message = "[反馈类型]输入错误")
    private List<Integer> feedbackType;

    /**
     * 回访内容
     */
    @ApiModelProperty(value = "回访内容", required = true)
    @NotBlank(message = "[回访内容]不能为空")
    @Size(min = 1, max = 1000, message = "[回访内容]长度为1~1000")
    private String callbackContent;

    /**
     * 图片
     */
    @ApiModelProperty(value = "图片")
    @Size(max = 10, message = "[图片]最多上传10张")
    private List<String> objectKeys;

    /**
     * 商家ID
     */
    @Null(message = "请勿传递[businessId]")
    private Long businessId;

    /**
     * 记录时间
     */
    @Null(message = "请勿传递[writeTime]")
    private Date writeTime;
}
