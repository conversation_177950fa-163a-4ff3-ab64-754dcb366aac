package com.ruoyi.system.api.domain.dto.biz.message;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :模特选择dto
 * @create :2025-02-24 09:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModelSelectMessageDTO implements Serializable {
    private static final long serialVersionUID = 5647038470642634657L;

    @ApiModelProperty("英文部客服Id")
    private Long userId;

    @ApiModelProperty("消息内容")
    private MessageDTO data;
}
