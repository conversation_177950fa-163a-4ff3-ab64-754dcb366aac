package com.ruoyi.system.api.domain.dto.biz.channel.fission;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.ChannelDiscountTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-02 17:11
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EditFissionChannelDiscountDTO implements Serializable {

    private static final long serialVersionUID = 2897836668727339009L;

    @ApiModelProperty("渠道ID")
    private Long channelId;

    @Null(message = "请勿传递渠道类型")
    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @NotNull(message = "[会员折扣类型]不能为空")
    @ApiModelProperty("会员折扣类型（1-固定金额，2-固定比例）")
    @EnumValid(enumClass = ChannelDiscountTypeEnum.class, message = "会员折扣类型不合法")
    private Integer memberDiscountType;

    @NotNull(message = "[会员折扣]不能为空")
    @ApiModelProperty("会员折扣")
    private BigDecimal memberDiscount;

    @NotNull(message = "[结算佣金类型]不能为空")
    @ApiModelProperty("结算佣金类型（1-固定金额，2-固定比例）")
    @EnumValid(enumClass = ChannelDiscountTypeEnum.class, message = "结算佣金类型不合法")
    private Integer settleDiscountType;

    @NotNull(message = "[结算佣金]不能为空")
    @ApiModelProperty("结算佣金")
    private BigDecimal settleDiscount;

    @NotNull(message = "[活动开始时间]不能为空")
    @ApiModelProperty("活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @NotNull(message = "[活动结束时间]不能为空")
    @ApiModelProperty("活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @AssertTrue(message = "[会员折扣]有误")
    private boolean isMemberDiscountParam() {
        if (ObjectUtil.isNull(memberDiscount)){
            return Boolean.TRUE;
        }
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType)){
            //固定金额
            return memberDiscount.compareTo(BigDecimal.ZERO) >= 0 &&  memberDiscount.compareTo(BigDecimal.valueOf(999)) <= 0;
        }else {
            //固定比例
            return memberDiscount.compareTo(BigDecimal.ZERO) >= 0 &&  memberDiscount.compareTo(BigDecimal.valueOf(99)) <= 0;
        }
    }
    @AssertTrue(message = "[结算佣金]有误")
    private boolean isSettleDiscountParam() {
        if (ObjectUtil.isNull(settleDiscount)){
            return Boolean.TRUE;
        }
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(settleDiscountType)){
            //固定金额
            return settleDiscount.compareTo(BigDecimal.ZERO) >= 0 &&  settleDiscount.compareTo(BigDecimal.valueOf(999)) <= 0;
        }else {
            //固定比例
            return settleDiscount.compareTo(BigDecimal.ZERO) >= 0 &&  settleDiscount.compareTo(BigDecimal.valueOf(99)) <= 0;
        }
    }
    @AssertTrue(message = "结束时间需要大于开始时间")
    private boolean isEndTimeParam() {
        return endTime.after(startTime);
    }



}
