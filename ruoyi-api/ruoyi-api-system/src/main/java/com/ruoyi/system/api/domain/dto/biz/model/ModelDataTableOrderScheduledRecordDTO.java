package com.ruoyi.system.api.domain.dto.biz.model;

import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/9 15:51
 */
@Data
public class ModelDataTableOrderScheduledRecordDTO implements Serializable {
    private static final long serialVersionUID = 8032984938149990295L;

    /**
     * 模特ID
     */
    @ApiModelProperty("模特ID")
    @NotNull(message = "[模特ID]不能为空")
    private Long modelId;

    /**
     * 标签
     */
    @ApiModelProperty("标签（0：无异常、1：工单、2：售后单、3：补偿订单、4：取消订单、5：回退订单）")
    private Integer tag;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private Integer sortColumn;

    /**
     * 排序字段STR
     */
    @Null(message = "请勿传递[sortColumnStr]")
    private String sortColumnStr;

    /**
     * 排序方式（ASC,DESC）
     */
    @ApiModelProperty(value = "排序方式（ASC,DESC）")
    @EnumValid(enumClass = OrderByDto.DIRECTION.class, message = "请传递正确的排序方式", enumField = "value")
    private String sortWay;
}
