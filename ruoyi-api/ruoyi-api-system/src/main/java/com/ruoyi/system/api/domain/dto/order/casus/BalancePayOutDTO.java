package com.ruoyi.system.api.domain.dto.order.casus;

import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceDetailLockDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-16 10:38
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BalancePayOutDTO implements Serializable {

    private static final long serialVersionUID = 6362381180436783044L;

    @ApiModelProperty("商家id")
    @NotNull(message = "[商家id]不能为空")
    private Long businessId;

    @ApiModelProperty("合计提现金额")
    @NotNull(message = "[合计提现金额]不能为空")
    @DecimalMin(value = "0", message = "合计提现金额不能小于0")
    private BigDecimal useBalance;

    @ApiModelProperty("提现方式（1-微信，2-支付宝，3-银行卡，4-境外汇款，5-其他，6-对公）")
    @NotNull(message = "提现方式不可为空")
    private Integer withdrawWay;

    @ApiModelProperty("备注")
    private String applyRemark;

    @ApiModelProperty("提现订单数据列表")
    @NotNull(message = "提现订单数据列表不可为空")
    @Size(min = 1, message = "至少需要有一条提现订单数据")
    private List<BusinessBalanceDetailLockDTO> businessBalanceDetailLocks;

    @ApiModelProperty("提现申请图片")
    @Size(max = 10, message = "[提现申请图片]最多上传10张")
    private List<String> payoutResourceUrlList;
}
