package com.ruoyi.system.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 微信登录qrcode生成DTO
 *
 * <AUTHOR>
 * @date 2024/6/19
 */
@AllArgsConstructor
@NoArgsConstructor
public class QrCodeDTO implements Serializable {

    private static final long serialVersionUID = -3044116356015217219L;
    @ApiModelProperty("二维码链接")
    private String qrcode;

    @ApiModelProperty("查询ticket")
    private String ticket;

    public String getQrcode() {
        return qrcode;
    }

    public void setQrcode(String qrcode) {
        this.qrcode = qrcode;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }
}
