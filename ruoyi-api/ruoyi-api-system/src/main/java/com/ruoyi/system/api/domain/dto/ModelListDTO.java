package com.ruoyi.system.api.domain.dto;

import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/21 17:41
 */
@Data
@ApiModel("模特列表入参")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModelListDTO implements Serializable {

    private static final long serialVersionUID = -943321716821174163L;
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;
    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id")
    private Collection<Long> id;

    @ApiModelProperty(value = "模特id，前端传参")
    private Long modelId;

    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String account;

    @ApiModelProperty(value = "家庭id")
    private Long familyId;

    @ApiModelProperty(value = "家庭id列表")
    private List<Long> familyIds;

    @ApiModelProperty(value = "亲属关系(0=发起人,1=母子,2=母女,3=夫妻,4=父子,5=父女,6=兄弟,7=姐妹,8=兄妹,9=姐弟")
    private Integer modelFamilyRelationship;

    @ApiModelProperty(value = "亲属关系列表(0=发起人,1=母子,2=母女,3=夫妻,4=父子,5=父女,6=兄弟,7=姐妹,8=兄妹,9=姐弟")
    private List<Integer> modelFamilyRelationships;

    @ApiModelProperty(value = "是否家庭模特发起者：0-否,1-是")
    private Integer isInitiator;

    @ApiModelProperty(value = "是否家庭模特：0-否,1-是")
    private Integer isFamilyModel;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
     * 关联人员
     */
    @ApiModelProperty(value = "关联人员")
    private List<Long> persons;
    /**
     * 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "平台（0:Amazon,1:tiktok,2:其他,3:APP/解说类）", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private List<Integer> platform;
    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private List<Integer> nation;
    /**
     * 性别(0:男,1:女)
     */
    @ApiModelProperty(value = "性别（1:男,0:女）", notes = "1:男,0:女")
    private List<Integer> sex;
    /**
     * 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
     */
    @ApiModelProperty(value = "年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）", notes = "1:婴幼儿,2:儿童,3:成年人,4:老年人")
    private List<Integer> ageGroup;

    /**
     * 擅长品类
     */
    @ApiModelProperty(value = "擅长品类")
    private List<Long> specialtyCategory;
    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    private List<Integer> type;
    /**
     * 模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)
     */
    @ApiModelProperty(value = "模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)")
    private List<Integer> status;
    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度（0:一般模特,1:优质模特,2:中度模特）", notes = "0:一般模特,1:优质模特,2:中度模特")
    private List<Integer> cooperation;

    /**
     * 待拍数排序 ASC=升序 DESC=降序
     */
    @ApiModelProperty(value = "待拍数排序 ASC=升序 DESC=降序")
    @EnumValid(enumClass = OrderByDto.DIRECTION.class, message = "请传递正确的[waitsSort]参数", enumField = "value")
    private String waitsSort;

    /**
     * 待确认数排序 ASC=升序 DESC=降序
     */
    @ApiModelProperty(value = "待确认数排序 ASC=升序 DESC=降序")
    @EnumValid(enumClass = OrderByDto.DIRECTION.class, message = "请传递正确的[toBeConfirmSort]参数", enumField = "value")
    private String toBeConfirmSort;

    /**
     * 模特id ： 模特待拍数
     */
    @Null(message = "请勿传递[waitsMap]")
    private Map<Long, Long> waitsMap;

    /**
     * 模特id ： 模特待确认数
     */
    @Null(message = "请勿传递[toBeConfirmMap]")
    private Map<Long, Long> toBeConfirmMap;

    @Null(message = "请勿传递[包含成员的家庭ID列表]")
    private List<Long> includeFamilyIds;

    @ApiModelProperty(value = "拍摄模特包含家庭成员")
    private Integer includeFamily;

    @ApiModelProperty(value = "过滤的模特ID")
    private Long filterModelId;

    /**
     * 家庭模特ID
     * 用于查询模特ID对应家庭模特ID列表
     */
    @ApiModelProperty(value = "家庭模特id")
    private List<Long> familyModelIds;

    @ApiModelProperty(value = "需要杯名单：1-需要")
    private Integer needBlacklist;

    /**
     * 是否展示（1:展示,0:不展示）
     */
    @ApiModelProperty(value = "是否展示（1:展示,0:不展示）")
    private List<Integer> isShow;

    /**
     * 创建时间-开始
     */
    @ApiModelProperty(value = "创建时间-开始")
    private Date createTimeBegin;

    /**
     * 创建时间-结束
     */
    @ApiModelProperty(value = "创建时间-结束")
    private Date createTimeEnd;

    /**
     * 有蜗牛照（1：有，0：没有）
     */
    @ApiModelProperty(value = "有蜗牛照（1：有，0：没有）")
    private List<Integer> haveSnailPics;
}
