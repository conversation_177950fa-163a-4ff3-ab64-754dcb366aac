package com.ruoyi.system.api.domain.entity.order.datastatistics;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2025-05-16 09:33:27
 */
@Data
@TableName("customer_service_data_statistics_day")
public class CustomerServiceDataStatisticsDay implements Serializable {
    private static final long serialVersionUID = 7721186582093851586L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 记录时间-开始
     */
    @ApiModelProperty(value = "记录时间-开始")
    private Date writeTimeBegin;

    /**
     * 记录时间-结束
     */
    @ApiModelProperty(value = "记录时间-结束")
    private Date writeTimeEnd;

    /**
     * 中文部客服新增/完成订单数量JSON
     */
    @ApiModelProperty(value = "中文部客服新增/完成订单数量JSON")
    private String chineseCustomerServiceAddedCompleteOrderCountJson;

    /**
     * 中文部客服新增/完成任务单数量JSON
     */
    @ApiModelProperty(value = "中文部客服新增/完成任务单数量JSON")
    private String chineseCustomerServiceAddedCompleteTaskCountJson;

    /**
     * 英文部客服新增/完成订单数量JSON
     */
    @ApiModelProperty(value = "英文部客服新增/完成订单数量JSON")
    private String englishCustomerServiceAddedCompleteOrderCountJson;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
