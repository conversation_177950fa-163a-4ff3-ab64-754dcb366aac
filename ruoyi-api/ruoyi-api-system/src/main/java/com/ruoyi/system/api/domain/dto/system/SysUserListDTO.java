package com.ruoyi.system.api.domain.dto.system;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysUserListDTO implements Serializable {
    private static final long serialVersionUID = 2604455299228373761L;
    /**
     * 用户ID
     */
    private Collection<Long> userId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 部门ID
     */
    private Long deptId;
}
