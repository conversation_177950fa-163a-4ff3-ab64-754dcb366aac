package com.ruoyi.system.api.domain.dto.biz.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 商家账号表
 * <AUTHOR>
 * @TableName business_account
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessAccountDTO implements Serializable {
    private static final long serialVersionUID = -2422254661466517180L;

    @NotNull(message="[主键ID]不能为空")
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotNull(message="[账号]不能为空")
    @ApiModelProperty("账号")
    private String account;

    @NotNull(message="[密码]不能为空")
    @ApiModelProperty("密码")
    @Size(max= 200, message="编码长度不能超过200")
    @Length(max= 200, message="编码长度不能超过200")
    private String password;

    @NotNull(message="[商家id]不能为空")
    @ApiModelProperty("商家id")
    private Long businessId;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("商家id列表")
    private List<Long> businessIds;

    @ApiModelProperty("商家账号ID列表")
    private List<Long> ids;

    @ApiModelProperty("账号列表")
    private List<String> accountList;

    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("名称")
    @Length(max= 20,message="编码长度不能超过20")
    private String name;

    @Size(max = 32, message = "微信昵称不能超过32个字")
    @ApiModelProperty("微信昵称")
    @Length(max = 32, message = "微信昵称不能超过32个字")
    private String nickName;

    @Size(max= 60,message="编码长度不能超过60")
    @ApiModelProperty("头像")
    @Length(max= 60,message="编码长度不能超过60")
    private String pic;

    @Size(max= 16,message="编码长度不能超过16")
    @ApiModelProperty("unionId")
    @Length(max= 16,message="编码长度不能超过16")
    private String unionid;

    @ApiModelProperty("手机号")
    private String phone;

    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("ExternalUserID企业微信外部联系人id")
    @Length(max= 32,message="编码长度不能超过32")
    private String externalUserId;

    @NotNull(message="[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;

    @ApiModelProperty("用户列表账号状态（0正常 1禁用）")
    private Integer userStatus;

    @ApiModelProperty("商家信息")
    private BusinessVO businessVO;

    @ApiModelProperty("最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    @ApiModelProperty("登录账号ID")
    private Long bizUserId;

    @ApiModelProperty("是否主账号（0-否， 1-是）")
    private Integer isOwnerAccount;


    @ApiModelProperty("是否需要携带活动")
    private Integer needActivity;

    @ApiModelProperty("套餐类型")
    private Integer packageType;
}

