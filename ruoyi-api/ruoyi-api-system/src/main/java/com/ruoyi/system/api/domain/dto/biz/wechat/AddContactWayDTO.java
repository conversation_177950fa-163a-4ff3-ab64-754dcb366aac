package com.ruoyi.system.api.domain.dto.biz.wechat;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/25
 */
@Data
@Builder
public class AddContactWayDTO implements Serializable {
    private static final long serialVersionUID = -9079235050618985833L;
    /**
     *
     * 联系方式类型,1-单人, 2-多人
     */
    private Integer type = 1;
    /**
     * 场景，1-在小程序中联系，2-通过二维码联系
     */
    private Integer scene =2;
    private String state;
    private String[] user;
}
