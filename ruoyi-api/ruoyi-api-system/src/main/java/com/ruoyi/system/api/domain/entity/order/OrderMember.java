package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :订单会员表
 * @create :2024-06-24 16:04
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderMember implements Serializable {

    private static final long serialVersionUID = -7105337902246005875L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;
    /**
     * 订单号
     */
    @NotBlank(message="[订单号]不能为空")
    @Size(max= 30,message="编码长度不能超过30")
    @ApiModelProperty("订单号")
    @Length(max= 30,message="编码长度不能超过30")
    private String orderNum;
    /**
     * 订单状态(1待支付、2待审核、3交易成功、4交易关闭)
     */
    @NotNull(message="[订单状态(1待支付、2待审核、3交易成功、4交易关闭)]不能为空")
    @ApiModelProperty("订单状态(1待支付、2待审核、3交易成功、4交易关闭)")
    private Integer status;
    /**
     * 套餐类型：0-季度套餐，1-一年会员，2-三年会员
     */
    @NotNull(message="[套餐类型：0-季度套餐，1-一年会员，2-三年会员]不能为空")
    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer packageType;

    @ApiModelProperty("会员开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberStartTime;

    @ApiModelProperty("会员结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberEndTime;

    @ApiModelProperty("加赠时间")
    private Integer presentedTime;

    @ApiModelProperty("加赠时间类型（1-天,2-月,3-年）")
    private Integer presentedTimeType;

    @ApiModelProperty("下单账号ID")
    private Long bizUserId;

    /**
     * 创建人id FK: business.id
     */
    @ApiModelProperty("创建人id FK: business.id")
    private Long createUserId;
    /**
     * 创建人名称： FK: business.name
     */
    @Size(max= 30,message="编码长度不能超过30")
    @ApiModelProperty("创建人名称： FK: business.name")
    @Length(max= 30,message="编码长度不能超过30")
    private String createUserName;
    /**
     * 创建时间
     */
    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人id	FK:  business.id
     */
    @ApiModelProperty("修改人id	FK:  business.id")
    private Long updateUserId;
    /**
     * 修改人名称  FK: business.name
     */
    @Size(max= 30,message="编码长度不能超过30")
    @ApiModelProperty("修改人名称  FK: business.name")
    @Length(max= 30,message="编码长度不能超过30")
    private String updateUserName;
    /**
     * 修改时间
     */
    @NotNull(message="[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty(value = "是否首次购买1:首次2:续费")
    private Integer isFirstBuy;

}

