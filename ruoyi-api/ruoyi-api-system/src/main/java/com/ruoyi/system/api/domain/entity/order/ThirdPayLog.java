package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 支付回调记录表
 *
 * @TableName third_pay_log
 */
@TableName(value = "third_pay_log")
@Data
public class ThirdPayLog implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 603634330398278487L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 货币种类
     */
    private String curr_type;

    /**
     * 商户订单号
     */
    private String mchnt_order_no;

    /**
     * 内部订单号
     */
    private String orderNumber;
    /**
     * 订单金额
     */
    private String order_amt;

    /**
     * 订单类型：ALIPAY, WECHAT, UNIONPAY(银联二维码)等
     */
    private String order_type;

    /**
     *
     */
    private String reserved_addn_inf;

    /**
     * 付款方式
     */
    private String reserved_bank_type;

    /**
     * 买家登录账号
     */
    private String reserved_buyer_logon_id;

    /**
     * 银行交易号
     */
    private String reserved_channel_order_id;

    /**
     * 优惠金额
     */
    private String reserved_coupon_fee;

    /**
     * 渠道信息
     */
    private String reserved_fund_bill_list;

    /**
     * 富友清算日
     */
    private String reserved_fy_settle_dt;

    /**
     * 富友追踪号
     */
    private String reserved_fy_trace_no;

    /**
     * 信用卡标识
     */
    private String reserved_is_credit;

    /**
     * 微信营销详情
     */
    private String reserved_promotion_detail;

    /**
     * 应结算订单金额
     */
    private String reserved_settlement_amt;

    /**
     * 应结订单金额
     */
    private String settle_order_amt;

    /**
     * 终端号
     */
    private String term_id;

    /**
     * 渠道流水号
     */
    private String transaction_id;

    /**
     * 支付完成时间
     */
    private String txn_fin_ts;

    /**
     * 用户在商户的ID
     */
    private String user_id;

    /**
     * 创建时间
     */
    private Date create_time;

    /**
     * 修改时间
     */
    private Date update_time;

    private String remark;

}
