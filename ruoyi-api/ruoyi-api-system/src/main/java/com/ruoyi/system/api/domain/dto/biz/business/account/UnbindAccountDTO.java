package com.ruoyi.system.api.domain.dto.biz.business.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-28 11:38
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnbindAccountDTO implements Serializable {
    private static final long serialVersionUID = 2272055337674420300L;

    @ApiModelProperty("账号ID")
    @NotNull(message = "[账号ID]不能为空")
    private Long businessAccountId;
}
