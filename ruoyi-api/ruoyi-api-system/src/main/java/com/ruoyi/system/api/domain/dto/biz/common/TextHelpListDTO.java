package com.ruoyi.system.api.domain.dto.biz.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TextHelpListDTO implements Serializable {
    private static final long serialVersionUID = -6425026229562472264L;

    @ApiModelProperty(value = "标题内容")
    private String name;

    @ApiModelProperty(value = "文本类型：0-协议信息,1-帮助中心-常见问题,2-帮助中心-新手指南")
    private Integer type;

    @ApiModelProperty(value = "状态（0:启用,1:禁用）")
    private Integer status;
}
