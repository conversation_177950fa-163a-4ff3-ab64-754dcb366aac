package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/18 10:48
 */
@Data
public class UpdateOrderVideoPriceDTO {

    /**
     * 视频订单id
     */
    @ApiModelProperty("视频订单id")
    @NotNull(message = "[视频订单id]不能为空")
    private Long videoId;

    /**
     * 订单费用
     */
    @ApiModelProperty("订单费用")
    @NotNull(message = "[订单费用]不能为空")
    @DecimalMin(value = "0.00", message = "[视频费用]不能小于0.00")
    @DecimalMax(value = "999.99", message = "[视频费用]不能大于999.99")
    private BigDecimal videoPrice;

    /**
     * 图片费用
     */
    @ApiModelProperty("图片费用")
    @NotNull(message = "[图片费用]不能为空")
    @DecimalMin(value = "0.00", message = "[图片费用]不能小于0.00")
    @DecimalMax(value = "999.99", message = "[图片费用]不能大于999.99")
    private BigDecimal picPrice;

    /**
     * 佣金代缴税费
     */
    @ApiModelProperty("佣金代缴税费")
    @NotNull(message = "[佣金代缴税费]不能为空")
    @DecimalMin(value = "0.00", message = "[佣金代缴税费]不能小于0.00")
    @DecimalMax(value = "999.99", message = "[佣金代缴税费]不能大于999.99")
    private BigDecimal commissionPaysTaxes;

    /**
     * 手续费
     */
    @ApiModelProperty("手续费")
    @NotNull(message = "[手续费]不能为空")
    @DecimalMin(value = "0.00", message = "[手续费]不能小于0.00")
    @DecimalMax(value = "999.99", message = "[手续费]不能大于999.99")
    private BigDecimal exchangePrice;

    /**
     * 服务费用
     */
    @ApiModelProperty("服务费用")
    @NotNull(message = "[服务费用]不能为空")
    @DecimalMin(value = "0.00", message = "[服务费用]不能小于0.00")
    @DecimalMax(value = "999.99", message = "[服务费用]不能大于999.99")
    private BigDecimal servicePrice;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Size(max = 800, message = "[备注]长度不能超过800")
    private String remark;
}
