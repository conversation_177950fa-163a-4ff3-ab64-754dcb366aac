package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-06 10:35
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class AddOrderMemberFlowDTO implements Serializable {
    private static final long serialVersionUID = -7059211390630237755L;

    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty("事件名称")
    private String eventName;

    @ApiModelProperty("原先订单状态（1待支付、2待审核、3交易成功、4交易关闭）")
    private Integer originStatus;

    @ApiModelProperty("目标订单状态（1待支付、2待审核、3交易成功、4交易关闭）")
    private Integer targetStatus;
}
