package com.ruoyi.system.api.domain.entity.biz.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 模特案例资源 model_video_resource
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@ApiModel(value = "模特案例资源 model_video_resource")
@TableName("model_video_resource")
@Data
public class ModelVideoResource implements Serializable
{

    private static final long serialVersionUID = -7888098409221004857L;
    /** 主键 */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "资源id", required = true)
    @Excel(name = "主键")
    @NotNull(message = "[资源id]不能为空")
    private Long id;

    /**
     * 模特ID
     */
    @ApiModelProperty(value = "模特ID")
    private Long modelId;

    /** 资源名称 */
    @ApiModelProperty(value = "资源名称")
    @Excel(name = "资源名称")
    private String name;

    /**
     * 类型（1：亚马逊案例视频，2：TikTok案例视频）
     */
    @ApiModelProperty(value = "类型（1：亚马逊案例视频，2：TikTok案例视频）")
    private Integer type;

    /** 视频url */
    @ApiModelProperty(value = "视频url")
    @Excel(name = "视频url")
    private String videoUrl;

    /** 图片uri */
    @ApiModelProperty(value = "图片uri")
    @Excel(name = "图片uri")
    private String picUri;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 商家端高亮标识
     */
    @ApiModelProperty(value = "商家端高亮标识")
    @TableField(exist = false)
    private boolean highlight;
}
