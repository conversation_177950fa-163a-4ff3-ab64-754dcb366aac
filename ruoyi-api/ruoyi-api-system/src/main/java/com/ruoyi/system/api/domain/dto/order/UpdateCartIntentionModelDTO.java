package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/14 17:02
 */
@Data
public class UpdateCartIntentionModelDTO implements Serializable {

    private static final long serialVersionUID = -577729663582584778L;
    /**
     * 购物车id
     */
    @ApiModelProperty(value = "购物车id", required = true)
    @NotNull(message = "[购物车id]不能为空")
    private Long id;

    /**
     * 模特id
     */
    @ApiModelProperty("模特id")
    private Long modelId;
}
