package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.validated.CommonValidatedGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-10-16 14:36
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderUploadLinkDTO implements Serializable {
    private static final long serialVersionUID = -8856577819052299849L;

    @ApiModelProperty(value = "视频订单主键id", required = true)
    @NotNull(message = "视频订单主键id不能为空")
    private Long id;

    @ApiModelProperty(value = "上传的链接")
    @NotBlank(message = "上传的链接不能为空", groups = CommonValidatedGroup.SaveValidatedGroup.class)
    @Pattern(regexp = "^https://www\\.amazon\\.com.*$", message = "[上传的链接]非Amazon链接", groups = CommonValidatedGroup.SaveValidatedGroup.class)
    private String uploadLink;

    /**
     * 操作备注
     */
    @ApiModelProperty(value = "操作备注")
    @Size(max = 300, message = "[操作备注]不能超过300个字符")
    @NotBlank(message = "[操作备注]不能为空", groups = CommonValidatedGroup.EditValidatedGroup.class)
    private String operateRemark;
}
