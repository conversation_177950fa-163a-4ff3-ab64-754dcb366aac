package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.OrderFlowButtonEnum;
import com.ruoyi.common.core.enums.OrderMemberStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-26 15:33
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlowOrderMemberDTO implements Serializable {

    private static final long serialVersionUID = 1516212066709950104L;
    @ApiModelProperty(value = "流转状态")
    @NotNull(message = "订单流转状态不能为空")
    private OrderMemberStatusEnum orderMemberStatusEnum;

    @ApiModelProperty(value = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderNum;

    @ApiModelProperty(value = "使用余额")
    private BigDecimal useBalance;

    @ApiModelProperty(value = "按钮类型")
    private OrderFlowButtonEnum orderFlowButtonEnum;

    @ApiModelProperty(value = "用户id")
    private Long bizUserId;

}
