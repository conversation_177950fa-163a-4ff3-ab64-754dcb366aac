package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 17:39
 */
@Data
public class OrderVideoReminderRecordListDTO implements Serializable {
    private static final long serialVersionUID = -5719711331266284745L;
    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keyword;
    /**
     * 催单状态
     */
    @ApiModelProperty("催单状态")
    private List<Integer> status;

    /**
     * 商家ids
     */
    @Null(message = "[businessIds]不能传值")
    private List<Long> businessIds;
    /**
     * 出单人ids
     */
    @Null(message = "[issueIds]不能传值")
    private List<Long> issueIds;
}
