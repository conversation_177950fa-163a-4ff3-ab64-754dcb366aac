package com.ruoyi.system.api.domain.dto.order.casus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-10 15:55
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GroupAddVideoDTO implements Serializable {

    private static final long serialVersionUID = -1424174449182294297L;

    @ApiModelProperty("视频ID")
    private Long id;

    @ApiModelProperty("视频名称")
    private String name;

    @NotNull(message="[分组id]不能为空")
    @ApiModelProperty("分组id")
    private Long groupId;

    @ApiModelProperty("平台类型")
    private Integer platform;
}
