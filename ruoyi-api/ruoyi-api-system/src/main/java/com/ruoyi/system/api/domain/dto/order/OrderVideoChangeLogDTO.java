package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/28 15:32
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoChangeLogDTO implements Serializable {
    private static final long serialVersionUID = -4219336863387857426L;

    private Long videoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    private Integer logType;

    private Map<String, Object> data;
}
