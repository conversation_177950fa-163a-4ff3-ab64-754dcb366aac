package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :修改外联人员dto
 * @create :2024-06-24 11:54
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateWaiterDTO implements Serializable {
    private static final long serialVersionUID = -8463321058838443199L;

    @ApiModelProperty(value = "商家id列表")
    @NotNull(message = "[商家id列表]不能为空")
    @Size(min = 1, message = "[商家id列表]至少需要一条数据")
    private List<Long> businessIds;

    @ApiModelProperty(value = "关联人员id")
    @NotNull(message = "[关联人员id]不能为空")
    private Long waiterId;
}
