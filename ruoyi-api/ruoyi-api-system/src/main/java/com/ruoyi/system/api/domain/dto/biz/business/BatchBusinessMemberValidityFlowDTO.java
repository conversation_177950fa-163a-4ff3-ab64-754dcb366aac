package com.ruoyi.system.api.domain.dto.biz.business;

import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberValidityFlow;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-11-19 14:48
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchBusinessMemberValidityFlowDTO implements Serializable {
    private static final long serialVersionUID = 185481601161142183L;

    private List<BusinessMemberValidityFlow> list;
}
