package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单_视频_携带对象 order_video_carry
 *
 * <AUTHOR>
 * @date 2024-06-15
 */
@ApiModel(value = "订单_视频_携带对象 order_video_carry")
@TableName("order_video_carry")
@Data
public class OrderVideoCarry implements Serializable {

    private static final long serialVersionUID = 6302042754910610139L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 模特id
     */
    @NotNull(message = "[模特id]不能为空")
    @ApiModelProperty(value = "模特id", required = true)
    @Excel(name = "模特id")
    private Long modelId;

    /**
     * 主携带视频订单id
     */
    @NotNull(message = "[主携带视频订单id]不能为空")
    @ApiModelProperty(value = "主携带视频订单id", required = true)
    @Excel(name = "主携带视频订单id")
    private Long mainCarryVideoId;

    /**
     * 被携带视频订单id
     */
    @ApiModelProperty(value = "被携带视频订单id", required = true)
    @NotNull(message = "[被携带视频订单id]不能为空")
    @Excel(name = "被携带视频订单id")
    private Long carryVideoId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
