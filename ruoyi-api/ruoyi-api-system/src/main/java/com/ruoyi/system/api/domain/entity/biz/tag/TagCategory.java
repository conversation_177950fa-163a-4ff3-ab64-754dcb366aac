package com.ruoyi.system.api.domain.entity.biz.tag;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 标签分类对象 tag_category
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@ApiModel(value = "标签分类对象 tag_category")
@TableName("tag_category")
@Data
public class TagCategory implements Serializable {

    private static final long serialVersionUID = -1613968998207822647L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 父分类id
     */
    @ApiModelProperty(value = "父分类id")
    @Excel(name = "父分类id")
    private Long parentId = 0L;

    /**
     * 分类path
     */
    @ApiModelProperty(value = "分类path")
    @Excel(name = "分类path")
    private String path;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    @Excel(name = "分类名称")
    private String name;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @Excel(name = "排序")
    private Long sort;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remark;

    /**
     * 状态（0:启用,1:禁用）
     */
    @ApiModelProperty(value = "状态（0:启用,1:禁用）")
    @Excel(name = "状态")
    private Integer status;
    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;
    /**
     * 创建者手机号
     */
    @ApiModelProperty(value = "创建者手机号")
    private String createPhone;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private String updateBy;
    /**
     * 更新者手机号
     */
    @ApiModelProperty(value = "更新者手机号")
    private String updatePhone;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
