package com.ruoyi.system.api.domain.dto.biz.channel.fission;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-05-20 13:40
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlowMemberSeedRecordDTO implements Serializable {
    private static final long serialVersionUID = 2792928940634992306L;

    @ApiModelProperty("提现Id列表")
    @NotNull(message = "提现Id列表不能为空")
    @Size(min = 1,message = "提现Id列表不能为空")
    private List<Long> ids;

    @ApiModelProperty("状态（2-待审核，3-待打款，4-已打款，5-审核不通过，6-打款异常）")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty("备注/理由")
    private String remark;
//
//    @ApiModelProperty("结算总金额")
//    @DecimalMin(value = "0.01",message = "结算总金额不能小于0.01")
//    @DecimalMax(value = "999999.99", message = "结算总金额不能大于999999.99")
//    private BigDecimal settleAmountTotal;

    @ApiModelProperty("打款账号")
    @Size(max= 32,message="[打款账号]长度不能超过32")
    private String payAccount;

    @ApiModelProperty("打款时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date payoutTime;

    @ApiModelProperty("打款凭证")
    private List<String> resourceUrlList;

    @ApiModelProperty("用户信息")
    private LoginUserInfoVO loginUserInfoVo;
}
