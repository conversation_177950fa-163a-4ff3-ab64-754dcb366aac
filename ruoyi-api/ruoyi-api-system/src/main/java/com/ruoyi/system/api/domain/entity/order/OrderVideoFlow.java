package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/2 16:51
 */
@Data
@TableName("order_video_flow")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoFlow implements Serializable {

    private static final long serialVersionUID = -7397870912342439146L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty("视频id (FK:order_video.id)")
    private Long videoId;

    /**
     * 事件名称
     */
    @ApiModelProperty("事件名称")
    private String eventName;

    /**
     * 事件执行对象（1:商家,2:运营,3:模特,9:系统）
     */
    @ApiModelProperty("事件执行对象（1:商家,2:运营,3:模特,9:系统）")
    private Integer eventExecuteObject;

    /**
     * 事件执行人用户id
     */
    @ApiModelProperty("事件执行人用户id")
    private Long eventExecuteUserId;

    /**
     * 事件执行人用户名称
     */
    @ApiModelProperty("事件执行人用户名称")
    private String eventExecuteUserName;

    /**
     * 事件执行人微信昵称
     */
    @ApiModelProperty("事件执行人微信昵称")
    private String eventExecuteNickName;

    /**
     * 事件执行人手机号
     */
    @ApiModelProperty("事件执行人手机号")
    private String eventExecutePhone;

    /**
     * 事件执行时间
     */
    @ApiModelProperty("事件执行时间")
    private Date eventExecuteTime;

    /**
     * 原先视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）
     */
    @ApiModelProperty("原先视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer originStatus;


    /**
     * 目标视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）
     */
    @ApiModelProperty("目标视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer targetStatus;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
