package com.ruoyi.system.api.domain.entity.order;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单_视频对象 order_video
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@ApiModel(value = "订单_视频对象 order_video")
@TableName("order_video")
@Data
public class OrderVideo implements Serializable {

    private static final long serialVersionUID = 8061122203434852941L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 订单号
     */
    @NotNull(message = "[订单号]不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    @Excel(name = "订单号")
    private String orderNum;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    @Excel(name = "视频编码")
    private String videoCode;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）", required = true)
    @NotNull(message = "[订单状态]不能为空")
    @Excel(name = "订单状态")
    private Integer status;

    /**
     * 订单进入新状态的时间
     */
    @ApiModelProperty(value = "订单进入新状态的时间")
    private Date statusTime;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类", required = true)
    @NotNull(message = "[使用平台]不能为空")
    @Excel(name = "使用平台", readConverterExp = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    @ApiModelProperty(value = "视频风格(0:Amazon,1:tiktok,2:APP/解说类)", notes = "0:Amazon,1:tiktok,2:APP/解说类", required = true)
    @Excel(name = "视频风格", readConverterExp = "0:Amazon,1:tiktok,2:APP/解说类")
    private Integer videoStyle;


    @ApiModelProperty(value = "是否照顾单（0=否,1=是）")
    @Excel(name = "是否照顾单", readConverterExp = "0=否,1=是")
    @NotNull(message = "[是否照顾单]不能为空")
    private Integer isCare;

    /**
     * 手机号是否可见(0-不可见,1-可见)
     */
    @ApiModelProperty(value = "手机号是否可见(0-不可见,1-可见)")
    private Integer modelPhoneVisible;

    /**
     * 注意事项图片（FK:order_resource.id，多个用,隔开）
     */
    @ApiModelProperty(value = "注意事项图片（FK:order_resource.id，多个用,隔开）")
    @TableField("cautions_pic")
    private String cautionsPicId;

    @ApiModelProperty(value = "特别强调图片（FK:order_resource.id，多个用,隔开）")
    private String particularEmphasisPicIds;

    /**
     * 产品中文名
     */
    @NotNull(message = "[产品中文名]不能为空")
    @ApiModelProperty(value = "产品中文名", required = true)
    @Excel(name = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @NotNull(message = "[产品英文名]不能为空")
    @ApiModelProperty(value = "产品英文名", required = true)
    @Excel(name = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    @Excel(name = "产品链接")
    private String productLink;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    @Excel(name = "产品图")
    private String productPic;

    /**
     * 参考视频链接
     */
    @NotNull(message = "[参考视频链接]不能为空")
    @ApiModelProperty(value = "参考视频链接", required = true)
    @Excel(name = "参考视频链接")
    private String referenceVideoLink;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）", notes = "1:横屏16：9,2:竖屏9：16", required = true)
    @NotNull(message = "[视频格式]不能为空")
    @Excel(name = "视频格式", readConverterExp = "1:横屏16：9,2:竖屏9：16")
    private Integer videoFormat;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "视频时长")
    @Excel(name = "视频时长")
    private Integer videoDuration;

    /** 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国） */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）",notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国",required = true)
    @NotNull(message = "[拍摄国家]不能为空")
    @Excel(name = "拍摄国家", readConverterExp = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国")
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人", required = true)
    @NotNull(message = "[模特类型]不能为空")
    @Excel(name = "模特类型", readConverterExp = "0:影响者,1:素人")
    private Integer modelType;

    /**
     * 实物（0:是,1:不是）
     */
    @ApiModelProperty(value = "实物（0:是,1:不是）", notes = "0:是,1:不是", required = true)
    @NotNull(message = "[实物]不能为空")
    @Excel(name = "实物", readConverterExp = "0:是,1:不是")
    private Integer isObject;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）", notes = "1:2张/$10,2:5张/$20")
    @Excel(name = "照片数量", readConverterExp = "1:2张/$10,2:5张/$20")
    private Integer picCount;

    @ApiModelProperty(value = "退款照片数量")
    private Integer refundPicCount;

    /**
     * 参考图片（关联资源id）
     */
    @ApiModelProperty(value = "参考图片（关联资源id）")
    @Excel(name = "参考图片")
    @TableField("reference_pic")
    private String referencePicId;

    /**
     * 意向模特id
     */
    @ApiModelProperty(value = "意向模特id")
    @Excel(name = "意向模特id")
    private Long intentionModelId;

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    @Excel(name = "拍摄模特id")
    private Long shootModelId;

    /**
     * 标记物流状态（1:标记发货）
     */
    @ApiModelProperty(value = "标记物流状态（1:标记发货）", notes = "1:标记发货")
    @Excel(name = "标记物流状态", readConverterExp = "1:标记发货")
    private Integer logisticFlag;

    /**
     * 发货备注
     */
    @ApiModelProperty(value = "发货备注")
    private String shippingRemark;
    /**
     * 发货图片(关联资源id)
     */
    @ApiModelProperty(value = "发货图片(关联资源id)")
    private String shippingPic;

    /**
     * 对接人id
     */
    @ApiModelProperty(value = "对接人id")
    @Excel(name = "对接人id")
    private Long contactId;

    /**
     * 出单人id
     */
    @ApiModelProperty(value = "出单人id")
    @Excel(name = "出单人id")
    private Long issueId;

    /**
     * 视频金额（单位：￥）
     */
    @ApiModelProperty(value = "视频金额（单位：￥）")
    @Excel(name = "视频金额（单位：￥）")
    private BigDecimal amount;

    /**
     * 视频金额（单位：$）
     */
    @ApiModelProperty(value = "视频金额（单位：$）")
    @Excel(name = "视频金额（单位：$）")
    private BigDecimal amountDollar;

    @ApiModelProperty(value = "需支付金额（单位：￥）")
    @Excel(name = "需支付金额（单位：￥）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "需支付金额（单位：$）")
    @Excel(name = "需支付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @ApiModelProperty(value = "使用余额（单位：￥）")
    @Excel(name = "使用余额（单位：￥）")
    private BigDecimal useBalance;

    /**
     * 视频活动优惠金额
     */
    @ApiModelProperty(value = "视频活动优惠金额")
    private BigDecimal videoPromotionAmount;

    /**
     * 只有差额为负数才分配差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal differenceAmount;

    /**
     * 视频价格（单位：$）
     */
    @ApiModelProperty(value = "视频价格（单位：$）")
    @Excel(name = "视频价格（单位：$）")
    private BigDecimal videoPrice;

    /**
     * 图片费用（单位：$）
     */
    @ApiModelProperty(value = "图片费用（单位：$）")
    @Excel(name = "图片费用（单位：$）")
    private BigDecimal picPrice;

    /**
     * 佣金代缴税费（单位：$）
     */
    @ApiModelProperty(value = "佣金代缴税费（单位：$）")
    @Excel(name = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    /**
     * 手续费（单位：$）
     */
    @ApiModelProperty(value = "手续费（单位：$）")
    @Excel(name = "手续费（单位：$）")
    private BigDecimal exchangePrice;

    /**
     * 服务费用（单位：$）
     */
    @ApiModelProperty(value = "服务费用（单位：$）")
    @Excel(name = "服务费用（单位：$）")
    private BigDecimal servicePrice;

    /**
     * 订单释放时间（商家无意向模特且首次到达待匹配状态、首个意向模特不想要，以上两种情况会设置此值）
     */
    @ApiModelProperty(value = "订单释放时间（商家无意向模特且首次到达待匹配状态、首个意向模特不想要，以上两种情况会设置此值）")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date releaseTime;

    /**
     * 订单完全释放flag（1:表示视频订单不需要再过滤24小时，可以放到模特订单公池中）
     */
    @ApiModelProperty(value = "订单完全释放flag（1:表示视频订单不需要再过滤24小时，可以放到模特订单公池中）")
    private Integer releaseFlag;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 当前操作是否是回退
     */
    @ApiModelProperty("当前操作是否是回退")
    @TableField(exist = false)
    private Boolean isRollback;

    /**
     * 订单进入待确认的时间
     */
    @ApiModelProperty(value = "订单进入待确认的时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date unConfirmTime;

    /**
     * 订单进入待完成的时间
     */
    @ApiModelProperty(value = "订单进入待完成的时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date    unFinishedTime;

    /**
     * 订单进入需确认的时间
     */
    @ApiModelProperty(value = "订单进入需确认的时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date needConfirmTime;

    /**
     * 视频订单自动完成时间
     */
    @ApiModelProperty(value = "视频订单自动完成时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date autoCompleteTime;

    /**
     * 视频订单自动完成起始时间
     */
    @ApiModelProperty(value = "视频订单自动完成起始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date autoCompleteStartTime;

    /**
     * 视频订单最后变更时间
     */
    @ApiModelProperty(value = "视频订单最后变更时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date lastChangeTime;

    /**
     * 最新提交模特时间
     */
    @ApiModelProperty(value = "最新提交模特时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date lastModelSubmitTime;

    /**
     * 创建订单商家id
     */
    @ApiModelProperty(value = "创建订单商家id")
    private Long createOrderBusinessId;

    /**
     * 创建订单商家代理类型(0:否,1:是)
     */
    @ApiModelProperty(value = "创建订单商家id")
    private Integer createOrderBusinessProxyStatus;

    /**
     * 创建订单登录用户id
     */
    @ApiModelProperty(value = "创建订单登录用户id")
    private Long createOrderBizUserId;

    /**
     * 创建订单用户id
     */
    @ApiModelProperty(value = "创建订单用户id")
    private Long createOrderUserId;

    /**
     * 创建订单用户账号
     */
    @ApiModelProperty(value = "创建订单用户账号")
    private String createOrderUserAccount;

    /**
     * 创建订单用户名称（订单运营）
     */
    @ApiModelProperty(value = "创建订单用户名称（订单运营）")
    private String createOrderUserName;

    /**
     * 创建订单用户微信名称（订单运营）
     */
    @ApiModelProperty(value = "创建订单用户微信名称（订单运营）")
    private String createOrderUserNickName;

    /**
     * 创建订单用户名称（下单运营）
     */
    @ApiModelProperty(value = "创建订单用户名称（下单运营）")
    private String createOrderOperationUserName;

    /**
     * 创建订单用户微信名称（下单运营）
     */
    @ApiModelProperty(value = "创建订单用户微信名称（下单运营）")
    private String createOrderOperationUserNickName;

    /**
     * 是通品（1：是，0：不是）
     */
    @ApiModelProperty(value = "是通品（1：是，0：不是）")
    private Integer isGund;

    /**
     * 首次匹配时间
     */
    @ApiModelProperty(value = "首次匹配时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date firstMatchTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 携带单忽略标记（1：表示携带该订单的主携带被回退后，且该订单状态为已完成时，会设置此值，表示该订单不计入携带数统计）
     * 临时字段 用于初始化order_video_match.carry_ignore字段
     */
    private Integer carryIgnore;

    /**
     * 排单类型（1:排单,2:携带排单）
     */
    @ApiModelProperty(value = "临时字段")
    private Integer scheduleType;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "临时字段")
    private Integer carryType;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "临时字段")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "临时字段")
    private BigDecimal commission;

    /**
     * 超额说明
     */
    @ApiModelProperty(value = "临时字段")
    private String overstatement;

    /**
     * 主携带数量
     */
    @ApiModelProperty(value = "临时字段")
    private Integer mainCarryCount;

    /**
     * 主携带视频订单id (FK:order_video.id)
     */
    @ApiModelProperty(value = "临时字段")
    private Long mainCarryVideoId;
}
