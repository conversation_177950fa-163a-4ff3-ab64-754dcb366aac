package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 类未使用
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-24 09:22
 **/
@Data
@Deprecated(since = "2024-11-25", forRemoval = true)
@AllArgsConstructor
@NoArgsConstructor
public class BusinessSaveDTO implements Serializable {
    private static final long serialVersionUID = -812137893737686377L;

    @Size(max= 50,message="[商家名称]长度不能超过50")
    @ApiModelProperty("商家名称")
    private String name;

    /**
     * 是否为代理(0:否,1:是)
     */
    @NotNull(message="[是否为代理(0:否,1:是)]不能为空")
    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;

    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;

    @NotNull(message="[对接客服  FK：sys_user.user_id]不能为空")
    @ApiModelProperty("对接客服  FK：sys_user.user_id")
    private Long waiterId;

    @ApiModelProperty("手机号")
    @Size(max= 15,message="[手机号]不能超过20")
    @Length(max= 15,message="[手机号]不能超过20")
    private String phone;

    @ApiModelProperty("ticket")
    @NotBlank(message = "[ticket]不能为空")
    private String ticket;
}
