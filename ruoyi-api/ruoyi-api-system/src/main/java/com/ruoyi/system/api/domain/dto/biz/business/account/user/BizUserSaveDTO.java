package com.ruoyi.system.api.domain.dto.biz.business.account.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
* 登录用户表
* <AUTHOR>
 * @TableName biz_user
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BizUserSaveDTO implements Serializable {

    private static final long serialVersionUID = 5040642647752241015L;

    @NotBlank(message="[手机号]不能为空")
    @Size(max= 15,message="编码长度不能超过15")
    @ApiModelProperty("手机号")
    @Length(max= 15,message="编码长度不能超过15")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式有误")
    private String phone;

    @Size(max= 32,message="[员工名称]不能超过32")
    @ApiModelProperty("员工名称")
    @Length(max= 32,message="[员工名称]不能超过32")
    @NotBlank(message="[员工名称]不能为空")
    private String name;

    @ApiModelProperty("重要程度：客户类型 （2-普通客户 0-一般客户 1-重要客户）")
    @NotNull(message = "[重要程度]不能为空")
    private Integer customerType;

    @ApiModelProperty("对接客服")
    private Long waiterId;

    @ApiModelProperty("ticket")
    @NotBlank(message="[ticket]不能为空")
    private String ticket;
}
