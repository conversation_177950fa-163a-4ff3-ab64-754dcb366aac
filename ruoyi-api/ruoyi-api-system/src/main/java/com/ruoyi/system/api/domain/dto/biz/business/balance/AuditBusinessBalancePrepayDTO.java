package com.ruoyi.system.api.domain.dto.biz.business.balance;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家预付表
 *
 * <AUTHOR>
 * @TableName business_balance_prepay
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuditBusinessBalancePrepayDTO implements Serializable {

    private static final long serialVersionUID = -981735608546630181L;

    @NotNull(message = "[商家预付id]不能为空")
    @ApiModelProperty("商家预付id")
    private Long id;

    @ApiModelProperty("商家ID")
    @NotNull(message = "[商家ID]不能为空")
    private Long businessId;

    @NotNull(message = "[审核状态（0:待处理,1:审核通过,2.审核拒绝）]不能为空")
    @ApiModelProperty("审核状态（0:待处理,1:审核通过,2.审核拒绝）")
    private Integer auditStatus;

    @ApiModelProperty("预付款增加金额（单位：￥）")
    @DecimalMax(value = "9999999.99", message = "[预付款增加金额]不能大于9999999.99")
    private BigDecimal realAmount;

    @ApiModelProperty("实付人民币（单位：￥）")
    @DecimalMax(value = "9999999.99", message = "[实付人民币]不能大于9999999.99")
    private BigDecimal realPayAmount;

    @ApiModelProperty("支付金额（对应币种实付）")
    @DecimalMax(value = "9999999.99", message = "[支付金额]不能大于9999999.99")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty("币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @Size(max = 300, message = "编码长度不能超过300")
    @ApiModelProperty("备注")
    @Length(max = 300, message = "编码长度不能超过300")
    private String remark;


    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("拒绝原因")
    @Length(max = 100, message = "编码长度不能超过100")
    private String rejectCause;

}
