package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.NationEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/9 18:26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoModelShippingAddressDTO implements Serializable {
    private static final long serialVersionUID = -578311306454869084L;
    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty("视频id")
    @NotNull(message = "[视频id]不能为空")
    private Long videoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 模特id (FK:order_video.shoot_model_id)
     */
    @ApiModelProperty("模特id")
    @NotNull(message = "[模特id]不能为空")
    private Long shootModelId;

    /**
     * 国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty("国家")
    @NotNull(message = "[国家]不能为空")
    private Integer nation;

    /**
     * 收件人
     */
    @ApiModelProperty("收件人")
    @NotBlank(message = "[收件人]不能为空")
    private String recipient;

    /**
     * 城市
     */
    @ApiModelProperty("城市")
    @NotBlank(message = "[城市]不能为空")
    private String city;

    /**
     * 州
     */
    @ApiModelProperty("州")
    private String state;

    /**
     * 邮编
     */
    @ApiModelProperty("邮编")
    @NotBlank(message = "[邮编]不能为空")
    private String zipcode;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    @NotBlank(message = "[详细地址]不能为空")
    private String detailAddress;

    /**
     * 发货备注
     */
    @ApiModelProperty("发货备注")
    private String shippingRemark;

    /**
     * 发货备注图片（FK:order_resource.id，多个用,隔开）
     */
    @ApiModelProperty("发货备注图片")
    private String shippingPic;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 手机号是否可见(0-不可见,1-可见)
     */
    @ApiModelProperty(value = "手机号是否可见(0-不可见,1-可见)")
    private Integer phoneVisible;

    @AssertTrue(message = "[州]不能为空")
    private boolean isStateValid() {
        if (NationEnum.USA.getCode().equals(nation) || NationEnum.CANADA.getCode().equals(nation) || NationEnum.FRANCE.getCode().equals(nation)) {
            return StrUtil.isNotBlank(state);
        }
        return true;
    }
}
