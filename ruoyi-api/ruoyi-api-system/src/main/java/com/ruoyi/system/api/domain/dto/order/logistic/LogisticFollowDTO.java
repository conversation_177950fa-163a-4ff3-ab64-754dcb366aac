package com.ruoyi.system.api.domain.dto.order.logistic;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :物流跟进服务
 * @create :2025-04-25 10:51
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LogisticFollowDTO implements Serializable {
    private static final long serialVersionUID = -7143496351735969102L;

    @ApiModelProperty("物流跟进Id")
    @NotNull(message = "物流跟进Id不能为空")
    private Long id;

    @ApiModelProperty(value = "物流主状态（1:查询不到,2:收到信息,3:运输途中,4:运输过久,5:到达待取,6:派送途中,7:投递失败,8:成功签收,9:可能异常）")
    @NotNull(message = "物流主状态不能为空")
    private Integer logisticMainStatus;

    @ApiModelProperty(value = "物流时间")
    @NotNull(message = "物流时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date logisticUpdateTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("图片地址")
    private List<String> resourceIds;

    @ApiModelProperty("是否回调：0-否，1-是")
    private Integer isCallBack;

}
