package com.ruoyi.system.api.domain.dto.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 商家会员有效期修改流水
 *
 * <AUTHOR>
 * @TableName business_member_validity_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessMemberValidityFlowDTO implements Serializable {
    private static final long serialVersionUID = -336993560542566327L;

    @NotNull(message = "[商家ID]不能为空")
    @ApiModelProperty("商家ID")
    private Long businessId;

    @ApiModelProperty("商家id列表")
    private List<Long> businessIds;

    @ApiModelProperty("处理类型：0-系统调整，1-商家购买")
    private Integer type;

    @ApiModelProperty("修改原因类型（1:老会员入驻,2:七天无理由,3:退会,4:其他）")
    private Integer changeReasonType;

}
