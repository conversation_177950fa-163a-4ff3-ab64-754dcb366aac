package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.PackageTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderMemberChannelDTO implements Serializable {
    private static final long serialVersionUID = 906076343718230318L;

    @NotNull(message = "[商家id（business.id）]不能为空")
    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    @NotBlank(message = "[商家名称（business.name）]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("商家名称（business.name）")
    @Length(max = 50, message = "编码长度不能超过50")
    private String businessName;

    @NotBlank(message = "[会员编码（business.member_code）]不能为空")
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("会员编码（business.member_code）")
    @Length(max = 10, message = "编码长度不能超过10")
    private String memberCode;

    @NotNull(message = "[套餐类型：0=季度会员,1=年度会员,2=三年会员]不能为空")
    @ApiModelProperty("套餐类型：0=季度会员,1=年度会员,2=三年会员")
    @EnumValid(enumClass = PackageTypeEnum.class, message = "[套餐类型]输入错误")
    private Integer memberPackageType;

    @ApiModelProperty(value = "订单实付金额（单位：￥）")
    @NotNull(message = "[订单实付金额]不能为空")
    private BigDecimal realPayAmount;

    /**
     * 订单实付金额（对应币种实付）
     */
    @ApiModelProperty("订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    /**
     * 币种（详见sys_dict_type.dict_type = sys_money_type）
     */
    @ApiModelProperty("币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    /**
     * 支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）
     */
    @ApiModelProperty("支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）")
    private Integer payType;


    @ApiModelProperty(value = "税点费用")
    private BigDecimal taxPointCost;

    /**
     * 种草码
     */
    @ApiModelProperty("种草码")
    @Size(min = 1, max = 10, message = "[种草码]长度应在1~10个字符之间")
    @NotBlank(message = "[种草码]不能为空")
    private String seedCode;

    /**
     * 订单金额
     */
    @NotNull(message = "[订单金额]不能为空")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;


    /**
     * 商家端登录用户id FK:biz_user.id
     */
    @ApiModelProperty("商家端登录用户id FK:biz_user.id")
    @NotNull(message = "[商家端登录用户id]不能为空")
    private Long bizUserId;

    /**
     * 商家端登录用户微信昵称
     */
    @ApiModelProperty("商家端登录用户微信昵称")
    private String bizUserNickName;

    @ApiModelProperty("订单号")
    private String orderNum;

    /**
     * 商家端登录用户手机号
     */
    @ApiModelProperty("商家端登录用户手机号")
    private String bizUserPhone;

    /**
     * 购买时间
     */
    @ApiModelProperty("购买时间")
    private Date buyTime;
}
