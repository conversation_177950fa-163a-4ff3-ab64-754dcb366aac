package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 预选管理-添加分发DTO
 *
 * <AUTHOR>
 * @date 2025/6/16 14:52
 */
@Data
public class AddDistributionDTO implements Serializable {
    private static final long serialVersionUID = 8724998960282450689L;

    /**
     * 匹配单ID
     */
    @ApiModelProperty("匹配单ID")
    @NotNull(message = "[匹配单ID]不能为空")
    private Long matchId;

    /**
     * 分配的模特ID
     */
    @ApiModelProperty("分配的模特ID")
    @NotEmpty(message = "[分配的模特ID]不能为空")
    private List<Long> modelIds;
}
