package com.ruoyi.system.api.domain.dto.order;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/11 11:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoTaskDetailFlowRecordDTO {

    /**
     * 操作类型 详见OrderTaskDetailFlowOperateTypeEnum
     * @see com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum
     */
    @ApiModelProperty(value = "操作类型 详见OrderTaskDetailFlowOperateTypeEnum")
    private Integer operateType;

    /**
     * 完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）
     */
    @ApiModelProperty(value = "完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）")
    private Integer completionMode;

    /**
     * 问题图片（FK:order_resource.id）
     */
    @ApiModelProperty(value = "问题图片")
    @TableField("issue_pic")
    private String issuePicId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
