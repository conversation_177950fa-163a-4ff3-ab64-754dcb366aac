package com.ruoyi.system.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceDetailFlowDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 余额流水表
 *
 * <AUTHOR>
 * @TableName business_balance_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceFlowDTO implements Serializable {
    private static final long serialVersionUID = -3206162264681086313L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("关键字")
    @Size(max = 100, message = "[关键字]长度不能超过100字符")
    private String keyword;

    @ApiModelProperty("商家id")
    private Long businessId;

    @ApiModelProperty("商家ID列表")
    private Set<Long> businessIds;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("订单号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String orderNum;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("退款审批号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String refundNum;

    @Size(max = 32, message = "编码长度不能超过30")
    @Length(max = 32, message = "编码长度不能超过30")
    @ApiModelProperty("预付单号")
    private String prepayNum;

    @Size(max = 32, message = "编码长度不能超过30")
    @Length(max = 32, message = "编码长度不能超过30")
    @ApiModelProperty("提现单号")
    private String withdrawNumber;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("视频编码")
    @Length(max = 30, message = "编码长度不能超过30")
    private String videoCode;

    @ApiModelProperty("视频订单ID")
    private Long videoId;

    @ApiModelProperty("帐号余额")
    private BigDecimal balance;

    @ApiModelProperty("订单金额（单位：￥）")
    private BigDecimal amount;

    @ApiModelProperty("订单类型(0-收入、1-支出)")
    private Integer type;

    @ApiModelProperty("订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6.余额提现、7.线下钱包充值收入、8.线上钱包充值")
    private Integer origin;

    @ApiModelProperty("订单来源列表(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6.余额提现、7.线下钱包充值收入、8.线上钱包充值)")
    private Set<Integer> origins;

    @ApiModelProperty("订单交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    @ApiModelProperty("订单交易开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeBegin;

    @ApiModelProperty("订单交易结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeEnd;

    @ApiModelProperty("登录人")
    private LoginBaseEntity loginBase;

    @ApiModelProperty("下单运营")
    private String createOrderUserName;

    @ApiModelProperty("下单运营微信名")
    private String createOrderUserNickName;

    @ApiModelProperty("支付方式")
    private Integer payType;

    @ApiModelProperty("订单下单时间")
    private Date orderTableOrderTime;

    /**
     * 只有在视频订单、审核通过 需要使用
     */
    @ApiModelProperty("商家余额详情流水")
    private List<BusinessBalanceDetailFlowDTO> businessBalanceDetailFlowList;
}
