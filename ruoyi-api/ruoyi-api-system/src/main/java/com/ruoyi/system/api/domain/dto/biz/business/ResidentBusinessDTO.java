package com.ruoyi.system.api.domain.dto.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 入驻会员请求参数
 *
 * <AUTHOR>
 * @TableName business
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ResidentBusinessDTO implements Serializable {

    private static final long serialVersionUID = 4521051894166649868L;

    @ApiModelProperty("商家账号或会员账号")
    private String searchName;

    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;

    @ApiModelProperty(value = "注册时间-begin")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date businessCreateBegin;

    @ApiModelProperty(value = "注册时间-end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date businessCreateEnd;

    @ApiModelProperty(value = "会员有效期-begin")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberValidityBegin;

    @ApiModelProperty(value = "会员有效期-end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberValidityEnd;

    @ApiModelProperty(value = "对接客服id")
    private Long waiterId;
}