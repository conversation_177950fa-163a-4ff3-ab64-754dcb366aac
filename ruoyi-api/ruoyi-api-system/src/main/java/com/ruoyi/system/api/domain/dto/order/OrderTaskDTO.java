package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class OrderTaskDTO implements Serializable {
    private static final long serialVersionUID = 3682549956713293870L;

    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id", required = true)
    @NotNull(message = "[视频订单id]不能为空")
    private Long videoId;

    /**
     * 任务单ID
     */
    private Long taskId;

    /**
     * 任务单类型（1：售后单，2：工单）
     */
    @ApiModelProperty(value = "任务单类型（1：售后单，2：工单）", required = true)
    @NotNull(message = "[任务单类型]不能为空")
    @EnumValid(enumClass = OrderTaskTypeEnum.class, message = "[任务单类型]输入错误")
    private Integer taskType;

    /**
     * 售后分类（1：视频，2：照片）
     */
    @ApiModelProperty(value = "售后分类（1：视频，2：照片）")
    @EnumValid(enumClass = OrderTaskAfterSaleClassTypeEnum.class, message = "[售后分类]输入错误")
    private Integer afterSaleClass;

    /**
     * 售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）
     */
    @ApiModelProperty(value = "售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）")
    @EnumValid(enumClass = OrderTaskAfterSaleVideoTypeEnum.class, message = "[售后视频类型]输入错误")
    private Integer afterSaleVideoType;

    /**
     * 售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）
     */
    @ApiModelProperty(value = "售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）")
    @EnumValid(enumClass = OrderTaskAfterSalePicTypeEnum.class, message = "[售后照片类型]输入错误")
    private Integer afterSalePicType;

    /**
     * 工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）
     */
    @ApiModelProperty(value = "工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）")
    @EnumValid(enumClass = OrderTaskWorkOrderTypeEnum.class, message = "[工单类型]输入错误")
    private Integer workOrderType;

    /**
     * 工单内容
     */
    @ApiModelProperty(value = "工单内容", required = true)
    @NotBlank(message = "[问题描述]不能为空")
    @Size(max = 1000, message = "[问题描述]长度不能超过1000")
    private String content;

    /**
     * 补充剪辑要求
     */
    @ApiModelProperty(value = "补充剪辑要求")
    @Size(max = 1000, message = "[补充剪辑要求]长度不能超过1000")
    private String clipRecord;

    /**
     * 问题图片
     */
    @ApiModelProperty(value = "问题图片")
    @Size(max = 5, message = "[问题图片]不能超过5张")
    private List<String> issuePic;

    /**
     * 优先级（1:紧急,2:一般）
     */
    @ApiModelProperty(value = "优先级（1:紧急,2:一般）", notes = "1:紧急,2:一般", required = true)
    @NotNull(message = "[优先级]不能为空")
    @EnumValid(enumClass = OrderTaskPriorityEnum.class, message = "[优先级]输入错误")
    private Integer priority;

    /**
     * 处理人id
     */
    @ApiModelProperty(value = "处理人id", required = true)
    @NotNull(message = "[处理人]不能为空")
    private Long assigneeId;

    @AssertTrue(message = "[售后分类]不能为空")
    private boolean isAfterSaleClass() {
        if (!OrderTaskTypeEnum.AFTER_SALE.getCode().equals(taskType)) {
            return true;
        }
        return ObjectUtil.isNotNull(afterSaleClass);
    }

    @AssertTrue(message = "[售后视频类型]不能为空")
    private boolean isAfterSaleVideoType() {
        if (!OrderTaskTypeEnum.AFTER_SALE.getCode().equals(taskType)) {
            return true;
        }
        if (!OrderTaskAfterSaleClassTypeEnum.VIDEO.getCode().equals(afterSaleClass)) {
            return true;
        }
        return ObjectUtil.isNotNull(afterSaleVideoType);
    }

    @AssertTrue(message = "[售后照片类型]不能为空")
    private boolean isAfterSalePicType() {
        if (!OrderTaskTypeEnum.AFTER_SALE.getCode().equals(taskType)) {
            return true;
        }
        if (!OrderTaskAfterSaleClassTypeEnum.PIC.getCode().equals(afterSaleClass)) {
            return true;
        }
        return ObjectUtil.isNotNull(afterSalePicType);
    }

    @AssertTrue(message = "[工单类型]不能为空")
    private boolean isWorkOrderType() {
        if (!OrderTaskTypeEnum.WORK_ORDER.getCode().equals(taskType)) {
            return true;
        }
        return ObjectUtil.isNotNull(workOrderType);
    }

    @AssertTrue(message = "传递了多余的参数，请检查~")
    private boolean isTaskType() {
        if (OrderTaskTypeEnum.AFTER_SALE.getCode().equals(taskType)) {
            if (OrderTaskAfterSaleClassTypeEnum.VIDEO.getCode().equals(afterSaleClass)) {
                return ObjectUtil.isNull(afterSalePicType) && ObjectUtil.isNull(workOrderType);
            } else {
                return ObjectUtil.isNull(afterSaleVideoType) && ObjectUtil.isNull(workOrderType);
            }
        } else {
            return ObjectUtil.isNull(afterSaleClass) && ObjectUtil.isNull(afterSalePicType) && ObjectUtil.isNull(afterSalePicType);
        }
    }
}
