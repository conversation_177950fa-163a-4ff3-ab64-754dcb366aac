package com.ruoyi.system.api.domain.dto.biz.business.account.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
* 登录用户表
* <AUTHOR>
 * @TableName biz_user
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BizUserEditDTO implements Serializable {

    private static final long serialVersionUID = 5040642647752241015L;

    @NotNull(message = "[id]不能为空")
    @ApiModelProperty("id")
    private Long id;

    @Size(max= 32,message="[员工名称]不能超过32")
    @ApiModelProperty("员工名称")
    @NotBlank(message="[员工名称]不能为空")
    private String name;

    @ApiModelProperty("重要程度：客户类型 （2-普通客户 0-一般客户 1-重要客户）")
    @NotNull(message = "[重要程度]不能为空")
    private Integer customerType;

    @ApiModelProperty("对接客服")
    private Long waiterId;

    @ApiModelProperty("状态")
    private Integer status;
}
