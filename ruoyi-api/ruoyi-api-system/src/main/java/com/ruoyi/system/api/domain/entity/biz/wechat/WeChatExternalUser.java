package com.ruoyi.system.api.domain.entity.biz.wechat;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 企业微信外部联系人信息对象 we_chat_external_user
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@ApiModel(value = "企业微信外部联系人信息对象 we_chat_external_user")
@TableName("we_chat_external_user")
@Data
public class WeChatExternalUser implements Serializable {

    private static final long serialVersionUID = 7149002490669088267L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * ExternalUserID企业微信外部联系人id
     */
    @NotNull(message = "[ExternalUserID企业微信外部联系人id]不能为空")
    @ApiModelProperty(value = "ExternalUserID企业微信外部联系人id", required = true)
    @Excel(name = "ExternalUserID企业微信外部联系人id")
    private String externalUserid;

    /**
     * unionid
     */
    @NotNull(message = "[unionid]不能为空")
    @ApiModelProperty(value = "unionid", required = true)
    @Excel(name = "unionid")
    private String unionid;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    @Excel(name = "用户名")
    private String name;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    @Excel(name = "职位")
    private String position;

    /**
     * 头像url
     */
    @ApiModelProperty(value = "头像url")
    @Excel(name = "头像url")
    private String avatar;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @Excel(name = "公司名称")
    private String corpName;

    /**
     * 公司全名称
     */
    @ApiModelProperty(value = "公司全名称")
    @Excel(name = "公司全名称")
    private String corpFullName;

    /**
     * 该成员添加此外部联系人所打标签类型（1:企业设,2:用户自定义,3:规则组标签）
     */
    @ApiModelProperty(value = "该成员添加此外部联系人所打标签类型（1:企业设,2:用户自定义,3:规则组标签）", notes = "1:企业设,2:用户自定义,3:规则组标签")
    @Excel(name = "该成员添加此外部联系人所打标签类型", readConverterExp = "1:企业设,2:用户自定义,3:规则组标签")
    private Integer type;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @Excel(name = "性别")
    private Integer gender;

    /**
     * 启用状态（0:启用,1:禁用）
     */
    @ApiModelProperty(value = "启用状态（0:启用,1:禁用）", notes = "0:启用,1:禁用")
    @Excel(name = "启用状态", readConverterExp = "0:启用,1:禁用")
    private Integer status;

    @ApiModelProperty(value = "渠道")
    private String channel;

    @ApiModelProperty(value = "渠道类型")
    private Integer channelType;

    @ApiModelProperty(value = "渠道id")
    private Long channelId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty(value = "企业微信外部人员id")
    private String connectUserId;

    @ApiModelProperty(value = "企业微信外部人员姓名")
    private String connectUserName;
}
