package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.OrderPoolPreselectedTimeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.*;

/**
 * 订单列表条件入参
 *
 * <AUTHOR>
 * @date 2024/6/1 11:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderListDTO implements Serializable {

    private static final long serialVersionUID = -4141172918318464905L;
    @ApiModelProperty("关键字模糊搜索")
    private String searchName;

    @ApiModelProperty("关键字查询商家列表")
    private List<Long> searchBusinessIds;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 订单号
     */
    @Null(message = "请勿传递[orderNums]参数")
    private Set<String> orderNums;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    /**
     * 根据商家名称获取的商家id
     */
    @Null(message = "请勿传递[merchantIds]参数")
    private List<Long> merchantIds;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 模特姓名
     */
    @ApiModelProperty(value = "模特姓名")
    private String modelName;

    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String modelAccount;

    @ApiModelProperty(value = "家庭Id")
    private Long familyId;


    /**
     * 根据模特姓名或者模特账号获取的模特id
     */
    @Null(message = "请勿传递[modelIds]参数")
    private List<Long> modelIds;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private List<Integer> platform = new ArrayList<>();

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    private List<Long> shootModelId;

    @ApiModelProperty(value = "拍摄模特包含家庭成员")
    private Integer includeFamily;

    /**
     * 对接人id
     */
    @ApiModelProperty(value = "对接人id")
    private List<Long> contactId = new ArrayList<>();

    /**
     * 出单人id
     */
    @ApiModelProperty(value = "出单人id")
    private List<Long> issueId;

    /**
     * 下单用户id
     */
    @ApiModelProperty(value = "下单用户id")
    private List<Long> orderUserId;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private List<Integer> status = new ArrayList<>();

    /**
     * 下单时间-开始
     */
    @ApiModelProperty(value = "下单时间-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date orderTimeBegin;

    /**
     * 下单时间-结束
     */
    @ApiModelProperty(value = "下单时间-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date orderTimeEnd;

    @ApiModelProperty(value = "下单时间标记-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date orderTimeSignBegin;

    /**
     * 下单时间-结束
     */
    @ApiModelProperty(value = "下单时间标记-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date orderTimeSignEnd;
    /**
     * 支付时间-开始
     */
    @ApiModelProperty(value = "支付时间-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date payTimeBegin;

    /**
     * 支付时间-结束
     */
    @ApiModelProperty(value = "支付时间-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date payTimeEnd;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式：5=银行,6:对公,7:全币种")
    private List<Integer> payType;

    /**
     * 预选添加人姓名
     */
    @ApiModelProperty(value = "预选添加人姓名")
    private List<String> preselectAddUserName;

    /**
     * 预选添加人id
     */
    @ApiModelProperty(value = "预选添加人id")
    private List<Long> preselectAddUserId;

    /**
     * 预选模特id
     */
    @ApiModelProperty(value = "预选模特id")
    private List<Long> preselectModelId;

    /**
     * 预选状态
     */
    @ApiModelProperty(value = "预选状态")
    private List<Integer> preselectStatus;
    /**
     * 财务审核状态列表
     */
    @ApiModelProperty(value = "审核状态")
    private List<Integer> auditStatusList;

    /**
     * 催单状态（1:未处理,2:已确认,3:已完成）
     */
    @ApiModelProperty(value = "催单状态（1:未处理,2:已确认,3:已完成）")
    private List<Integer> reminderStatus;

    /**
     * 匹配情况反馈(0:待反馈,1:同意,2:不同意)
     */
    @ApiModelProperty(value = "匹配情况反馈(0:待反馈,1:同意,2:不同意)")
    private List<Integer> replyContent;

    /**
     * 物流主状态（1:查询不到,2:收到信息,3:运输途中,4:运输过久,5:到达待取,6:派送途中,7:投递失败,8:成功签收,9:可能异常）
     */
    @ApiModelProperty(value = "物流主状态（1:查询不到,2:收到信息,3:运输途中,4:运输过久,5:到达待取,6:派送途中,7:投递失败,8:成功签收,9:可能异常）")
    private List<Integer> logisticMainStatus;

    /**
     * 通过物流主状态和订单关联物流查询出的视频订单id
     */
    private Collection<Long> videoIds = new ArrayList<>();

    private Collection<Long> rollbackIds = new ArrayList<>();

    /**
     * 订单状态天数
     */
    @ApiModelProperty(value = "订单状态天数")
    @Min(value = 0, message = "订单状态天数不能小于0")
    private Integer statusDays;

    @ApiModelProperty(value = "新订单状态天数")
    @Min(value = 0, message = "新订单状态天数不能小于0")
    private Integer newStatusDays;

    /**
     * 当前登录用户类型
     */
    private Integer loginUserType;

    /**
     * 有退款的视频订单id
     */
    @Null(message = "请勿传递[refundVideoIds]参数")
    private List<Long> refundVideoIds;

    @ApiModelProperty(value = "下单账号ID")
    private List<Long> userIds;

    @ApiModelProperty(value = "是否确认收货：1:已收货,0:未收货")
    private Integer receipt;

    @ApiModelProperty(value = "上传需求时间-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date uploadLinkTimeBegin;

    @ApiModelProperty(value = "上传需求时间-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date uploadLinkTimeEnd;

    @ApiModelProperty(value = "下载视频: 0:未下载,1:已下载")
    private Integer downloadStatus;

    /**
     * 订单进入新状态时间-开始
     */
    @ApiModelProperty(value = "订单进入新状态时间-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date statusTimeBegin;

    /**
     * 订单进入新状态时间-结束
     */
    @ApiModelProperty(value = "订单进入新状态时间-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date statusTimeEnd;

    /**
     * 订单进入新状态时间排序（ASC=升序，DESC=降序）
     */
    @ApiModelProperty(value = "订单进入新状态时间排序（ASC=升序，DESC=降序）")
    @EnumValid(enumClass = OrderByDto.DIRECTION.class, message = "请传递正确的[statusTimeSort]参数", enumField = "value")
    private String statusTimeSort;

    /**
     * 订单进入新状态时间-之前
     */
    @ApiModelProperty(value = "订单进入新状态时间-之前")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date beforeStatusTime;

    /**
     * 订单进入新状态时间-之前-开始
     */
    @ApiModelProperty(value = "订单进入新状态时间-之前-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date beforeStatusTimeStart;

    /**
     * 订单进入新状态时间-之前-结束
     */
    @ApiModelProperty(value = "订单进入新状态时间-之前-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date beforeStatusTimeEnd;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）")
    private List<Integer> modelType;

    @ApiModelProperty(value = "是否应收审批")
    private Integer isReceivableAudit;

    @ApiModelProperty(value = "是否分页")
    private Integer startPage;


    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国", required = true)
    private List<Integer> shootingCountry;

    private Long videoId;

    /**
     * 开始匹配时间
     */
    @ApiModelProperty(value = "开始匹配时间（1:24小时内,2:,1~2天,3:2~3天,4:3天以上）")
    @EnumValid(enumClass = OrderPoolPreselectedTimeEnum.class, message = "[开始匹配时间]输入错误")
    private Integer matchStartTime;
    /**
     * 添加预选时间开始
     */
    @Null(message = "请勿传递[searchMap]")
    private Map<String,String> searchMap;

    /**
     * 匹配单开始时间
     */
    @ApiModelProperty(value = "匹配单开始时间（1:24小时内,2:,1~2天,3:2~3天,4:3天以上）")
    private List<Integer> matchStartTimes;


    /**
     * 添加预选时间
     */
    @ApiModelProperty(value = "添加预选时间（1:24小时内,2:,1~2天,3:2~3天,4:3天以上）")
    @EnumValid(enumClass = OrderPoolPreselectedTimeEnum.class, message = "[添加预选时间]输入错误")
    private Integer addPreselectTime;

    /**
     * 匹配单开始时间开始
     */
    @Null(message = "请勿传递[preselectedTimeBegin]")
    private String matchStartTimeBegin;

    /**
     * 匹配单开始时间结束
     */
    @Null(message = "请勿传递[preselectedTimeEnd]")
    private String matchStartTimeEnd;

    /**
     * 添加预选时间开始
     */
    @Null(message = "请勿传递[preselectedTimeBegin]")
    private String addPreselectTimeBegin;

    /**
     * 添加预选时间结束
     */
    @Null(message = "请勿传递[preselectedTimeEnd]")
    private String addPreselectTimeEnd;

    /**
     * 下单运营
     */
    @ApiModelProperty(value = "下单运营")
    private List<String> createOrderUserName;

    /**
     * 与我相关
     */
    @ApiModelProperty(value = "与我相关")
    private String aboutMe;

    /**
     * 当前登录蜗牛运营ID
     */
    private Long backUserId;

    @ApiModelProperty(value = "财务审核状态")
    private Integer auditStatus;

    /**
     * 匹配状态（1:正常,2:暂停）
     */
    @ApiModelProperty(value = "匹配状态（1:正常,2:暂停）")
    private Integer matchStatus;

    @ApiModelProperty(value = "是否过滤关闭状态")
    private Integer isFilterClose;

    @ApiModelProperty(value = "是否照顾单（0=否,1=是）")
    private Integer care;

    /**
     * 兼容预选状态
     */
    private List<Integer> newPreselectStatus;

    /**
     * 确认签收时间
     */
    @ApiModelProperty(value = "确认签收时间（1:24小时内,2:,1~2天,3:2~3天,4:3天以上）")
    private List<Integer> confirmReceiptTimes;

    /**
     * 确认签收时间
     */
    @Null(message = "请勿传递[confirmReceiptTimeMap]")
    private Map<String, String> confirmReceiptTimeMap;

    /**
     * 确认签收时间-之前
     */
    @ApiModelProperty(value = "确认签收时间-之前")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date beforeConfirmReceiptTime;

    @ApiModelProperty(value = "关闭订单时状态：1-需发货/待完成")
    private Integer closeOrderStatus;

    /**
     * 模特待完成订单
     */
    @ApiModelProperty(value = "模特待完成订单")
    private boolean modelWaitOrder;

    /**
     * 照片数量
     */
    @ApiModelProperty(value = "照片数量")
    private Boolean hasPicCount;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private List<Integer> payTypeDetails;

    /**
     * 意向模特ID
     */
    @ApiModelProperty(value = "意向模特ID")
    private List<Long> intentionModelIds;

    /**
     * 是通品（1：是，0：不是）
     */
    @ApiModelProperty(value = "是通品（1：是，0：不是）")
    private List<Integer> isGunds;
}
