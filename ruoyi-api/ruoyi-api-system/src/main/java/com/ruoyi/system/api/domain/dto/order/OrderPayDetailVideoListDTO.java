package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-25 09:23
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderPayDetailVideoListDTO implements Serializable {
    private static final long serialVersionUID = -3018878504632614422L;

    @ApiModelProperty(value = "关键字")
    private String keyword;

     @ApiModelProperty(value = "订单号")
     private String orderNum;


    private List<Long> keywordModelIds;

    @ApiModelProperty(value = "支付方式")
    private List<Integer> payTypes;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private List<Integer> payTypeDetails;

    // @ApiModelProperty(value = "是否入账")
    // private Integer isRecord;


    @ApiModelProperty(value = "商家id")
    private Long businessId;


    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date endTime;


}
