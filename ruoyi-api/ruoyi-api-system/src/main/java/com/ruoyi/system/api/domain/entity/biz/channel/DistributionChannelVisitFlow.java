package com.ruoyi.system.api.domain.entity.biz.channel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 分销渠道访问流水数据
 * @TableName distribution_channel_visit_flow
 */
@TableName(value ="distribution_channel_visit_flow")
@Data
public class DistributionChannelVisitFlow implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分销id（distribution_channel.id）
     */
    @TableField(value = "channel_id")
    private Long channelId;

    /**
     * 独立访客
     */
    @TableField(value = "unique_visitor")
    private Integer uniqueVisitor;

    /**
     * 访问量
     */
    @TableField(value = "page_view")
    private Integer pageView;

    /**
     * 跳出率
     */
    @TableField(value = "bounce_rate")
    private BigDecimal bounceRate;

    /**
     * 采集时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DistributionChannelVisitFlow other = (DistributionChannelVisitFlow) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getChannelId() == null ? other.getChannelId() == null : this.getChannelId().equals(other.getChannelId()))
            && (this.getUniqueVisitor() == null ? other.getUniqueVisitor() == null : this.getUniqueVisitor().equals(other.getUniqueVisitor()))
            && (this.getPageView() == null ? other.getPageView() == null : this.getPageView().equals(other.getPageView()))
            && (this.getBounceRate() == null ? other.getBounceRate() == null : this.getBounceRate().equals(other.getBounceRate()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getChannelId() == null) ? 0 : getChannelId().hashCode());
        result = prime * result + ((getUniqueVisitor() == null) ? 0 : getUniqueVisitor().hashCode());
        result = prime * result + ((getPageView() == null) ? 0 : getPageView().hashCode());
        result = prime * result + ((getBounceRate() == null) ? 0 : getBounceRate().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", channelId=").append(channelId);
        sb.append(", uniqueVisitor=").append(uniqueVisitor);
        sb.append(", pageView=").append(pageView);
        sb.append(", bounceRate=").append(bounceRate);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}