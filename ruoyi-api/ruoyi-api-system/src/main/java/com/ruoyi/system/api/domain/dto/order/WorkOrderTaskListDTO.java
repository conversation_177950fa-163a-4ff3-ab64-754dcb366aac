package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.LogicalSymbolEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class WorkOrderTaskListDTO implements Serializable {
    private static final long serialVersionUID = -3890243556747566295L;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
        private String keyword;

    /**
     * 优先级（1:紧急,2:一般）
     */
    @ApiModelProperty(value = "优先级（1:紧急,2:一般）", notes = "1:紧急,2:一般")
    private List<Integer> priority;

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    private List<Long> shootModelId;

    /**
     * 工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）
     */
    @ApiModelProperty(value = "工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）")
    private List<Integer> workOrderType;

    /**
     * 状态（1：待处理，2：处理中，3：申请取消中，4：已完成，5：已拒绝，6：已关闭）
     */
    @ApiModelProperty(value = "状态（1：待处理，2：处理中，3：申请取消中，4：已完成，5：已拒绝，6：已关闭）")
    private List<Integer> status;

    /**
     * 处理人ID
     */
    @ApiModelProperty(value = "处理人ID")
    private List<Long> assigneeId;

    /**
     * 最新回复时间
     */
    @ApiModelProperty(value = "最新回复时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date lastReplyTime;

    /**
     * 最新回复时间逻辑符 1:>=,2:>,3:=,4:<,5:<=
     */
    @ApiModelProperty(value = "最新回复时间逻辑符 1:>=,2:>,3:=,4:<,5:<=")
    @EnumValid(enumClass = LogicalSymbolEnum.class, message = "[最新回复时间逻辑符]参数有误")
    private String lastReplyTimeLogicalSymbol;

    /**
     * 历史处理人ID
     */
    @ApiModelProperty(value = "历史处理人ID")
    private List<Long> historyAssigneeId;

    /**
     * 提交人ID
     */
    @ApiModelProperty(value = "提交人ID")
    private List<Long> submitById;

    /**
     * 提交时间开始
     */
    @ApiModelProperty(value = "提交时间开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTimeBegin;

    /**
     * 提交时间结束
     */
    @ApiModelProperty(value = "提交时间结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTimeEnd;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 任务单ID
     */
    @Null(message = "请勿传递[taskIds]")
    private Collection<Long> taskIds;


    @ApiModelProperty(value = "任务关联人Id列表")
    private List<Long> relevanceUserId;
}
