package com.ruoyi.system.api.domain.dto.biz.business.account.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-30 17:18
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BizUserChannelListDTO implements Serializable {
    private static final long serialVersionUID = -2491411174027474838L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("关键字搜索登录账号列表")
    private List<Long> keywordBizUserIds;

    @ApiModelProperty("登录账号列表")
    private List<Long> bizUserIds;

    @ApiModelProperty("渠道id")
    private List<Long> channelIds;
}
