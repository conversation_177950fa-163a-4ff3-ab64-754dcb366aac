package com.ruoyi.system.api.domain.entity.order.logistic;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 物流跟进表
 *
 * <AUTHOR>
 * @TableName order_video_logistic_follow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoLogisticFollow implements Serializable {

    private static final long serialVersionUID = 6778532497701832488L;

    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[物流关联表id]不能为空")
    @ApiModelProperty("物流关联表id order_video_logistic.id")
    private Long orderVideoLogisticId;

    @NotNull(message = "[商家id（business.id）]不能为空")
    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    @NotBlank(message = "[会员编码（business.member_code）]不能为空")
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("会员编码（business.member_code）")
    @Length(max = 10, message = "编码长度不能超过10")
    private String memberCode;

    @NotBlank(message = "[视频编码]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("视频编码")
    @Length(max = 30, message = "编码长度不能超过30")
    private String videoCode;

    @NotNull(message = "[视频id FK:order_video.id]不能为空")
    @ApiModelProperty("视频id FK:order_video.id")
    private Long videoId;

    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("物流单号")
    @Length(max = 100, message = "编码长度不能超过100")
    private String number;

    @NotNull(message = "[处理状态枚举]不能为空")
    @ApiModelProperty("处理状态枚举(0-未通知、1-已通知、2-延迟发货、3-延迟发货提醒、4-延迟发货已提醒、5-催发货提醒、6-催发货已提醒、7-地址变更通知、8-变更已通知、9-补充说明、10-标记发货、11-标记发货提醒、12-标记发货已提醒、13-通知确认模特、14-已通知确认模特、15-催确认模特提醒、16-已通知催确认模特)")
    private Integer handleStatus;

    @NotNull(message = "[物流状态(0-未发货、1-已发货)]不能为空")
    @ApiModelProperty("物流状态(0-未发货、1-已发货)")
    private Integer logisticStatus;

    @ApiModelProperty("跟进状态码(1-需处理、2-暂不处理、10-已发货、11-需跟进、12-模特待确认、13-无需跟进、14-已结束)")
    private Integer followStatus;

    @ApiModelProperty("通知时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date notifyTime;

    @ApiModelProperty("发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date logisticStartTime;

    @ApiModelProperty("是否默认发货时间：0-否，1-是")
    private Integer isDefaultLogisticStartTime;

    @ApiModelProperty("最新物流主状态")
    private String latestMainStatus;

    @ApiModelProperty("物流系统同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date logisticUpdateTime;

    @ApiModelProperty("是否回调（0-手动 1-系统）")
    private Integer isCallBack;

    @ApiModelProperty("模特结果枚举(0-待处理、1-已询问、2-已收货、3-丢件、4-订单回退、5-待通知拍摄、6-已通知拍摄)")
    private Integer modelResult;

    @ApiModelProperty("实际签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    @Size(max = 300, message = "[最新处理说明]不能超过300")
    @ApiModelProperty("最新处理说明")
    @Length(max = 300, message = "[最新处理说明]不能超过300")
    private String latestRemark;

    @ApiModelProperty("最新处理说明图片")
    private String latestResourceId;

    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("创建人姓名")
    @Length(max = 32, message = "编码长度不能超过32")
    private String createBy;

    @ApiModelProperty("创建人ID")
    private Long createById;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("更新人姓名")
    @Length(max = 32, message = "编码长度不能超过32")
    private String updateBy;

    @ApiModelProperty("更新人ID")
    private Long updateById;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty("产品图")
    private String productPic;

    @ApiModelProperty(value = "产品链接")
    private String productLink;

    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    @ApiModelProperty(value = "拍摄模特id")
    private Long shootModelId;

    @ApiModelProperty(value = "中文部客服id")
    private Long contactId;

    @ApiModelProperty(value = "英文部客服id")
    private Long issueId;

    @ApiModelProperty(value = "创建订单用户名称（订单运营）")
    private String createOrderUserName;

    @ApiModelProperty(value = "创建订单用户微信名称（订单运营）")
    private String createOrderUserNickName;

    @ApiModelProperty(value = "创建订单用户名称（下单运营）")
    private String createOrderOperationUserName;

    @ApiModelProperty(value = "创建订单用户微信名称（下单运营）")
    private String createOrderOperationUserNickName;

    @ApiModelProperty(value = "平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;
}
