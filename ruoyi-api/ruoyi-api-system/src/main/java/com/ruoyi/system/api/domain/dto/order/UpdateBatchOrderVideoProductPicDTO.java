package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/6 10:35
 */
@Data
public class UpdateBatchOrderVideoProductPicDTO implements Serializable {
    private static final long serialVersionUID = -9015376609231766995L;
    private Long id;
    private String productLink;
    private String productPic;

    public static Map<String, String> getMap(List<UpdateBatchOrderVideoProductPicDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return Collections.emptyMap();
        }

        Map<String, String> map = new HashMap<>();
        for (UpdateBatchOrderVideoProductPicDTO productPicDTO : dtoList) {
            if (!map.containsKey(productPicDTO.getProductLink())) {
                map.put(productPicDTO.getProductLink(), productPicDTO.getProductPic());
            }
        }
        return map;
    }
}
