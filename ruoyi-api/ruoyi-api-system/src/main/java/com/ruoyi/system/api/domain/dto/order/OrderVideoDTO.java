package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.ReUtil;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import com.ruoyi.system.api.domain.entity.order.promotion.OrderPromotionDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.Default;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/31 9:28
 */
@ApiModel("订单创建修改入参")
@Data
@Slf4j
public class OrderVideoDTO implements Serializable {
    private static final long serialVersionUID = 515061003354016451L;

    public interface AddVideoCartValidGroup extends Default {

    }

    public interface EditVideoCartValidGroup extends Default {

    }

    public interface ManagerTypeAuditOrAuditValidGroup extends Default {

    }

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "[主键]不能为空", groups = {CommonValidatedGroup.EditValidatedGroup.class, ManagerTypeAuditOrAuditValidGroup.class, EditVideoCartValidGroup.class})
    protected Long id;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类", required = true)
    @NotNull(message = "[使用平台]不能为空")
    @EnumValid(enumClass = PlatformEnum.class, message = "[使用平台]输入错误")
    private Integer platform;

    @ApiModelProperty(value = "视频风格(0:Amazon,1:tiktok,2:APP/解说类)")
    @EnumValid(enumClass = PlatformEnum.class, message = "[视频风格]输入错误")
    private Integer videoStyle;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）", notes = "1:横屏16：9,2:竖屏9：16", required = true)
    @NotNull(message = "[视频格式]不能为空")
    @EnumValid(enumClass = VideoFormatEnum.class, message = "[视频格式]输入错误")
    private Integer videoFormat;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国", required = true)
    @NotNull(message = "[拍摄国家]不能为空")
    @EnumValid(enumClass = NationEnum.class, message = "[拍摄国家]输入错误")
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人", required = true)
    @NotNull(message = "[模特类型]不能为空")
    @EnumValid(enumClass = ModelTypeEnum.class, message = "[模特类型]输入错误")
    private Integer modelType;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名", required = true)
    @NotBlank(message = "[中文名称]不能为空")
    @Size(max = 255, message = "[中文名称]不能超过255个字符")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名", required = true)
    @NotBlank(message = "[产品名称]不能为空")
    @Size(max = 255, message = "[产品名称]不能超过255个字符")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    @Size(max = 1000, message = "[产品链接]不能超过1000个字符")
    private String productLink;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    @Size(max = 64, message = "[产品图URI]不能超过64个字符")
    private String productPic;

    /**
     * 拍摄数量
     */
    @ApiModelProperty(value = "拍摄数量")
    @Range(min = 1, max = 99, message = "[拍摄数量]必须在1~99之间", groups = {AddVideoCartValidGroup.class, CommonValidatedGroup.SaveValidatedGroup.class})
    @NotNull(message = "[拍摄数量]不能为空", groups = {AddVideoCartValidGroup.class, CommonValidatedGroup.SaveValidatedGroup.class})
    private Integer shootCount;

    /**
     * 加入购物车时的意向模特id
     */
    @ApiModelProperty(value = "加入购物车时的意向模特id")
    @Size(max = 99, message = "[意向模特]最多选择99个")
    private List<Long> intentionModelIds;

    /**
     * 编辑购物车时的意向模特id
     */
    @ApiModelProperty(value = "编辑购物车时的意向模特id")
    private Long intentionModelId;

    /**
     * 拍摄建议
     */
    @ApiModelProperty("拍摄建议")
    @Valid
    private List<VideoContentDTO> shootRequired = new ArrayList<>();

    /**
     * 产品卖点
     */
    @ApiModelProperty("产品卖点")
    @Size(max = 8000, message = "[产品卖点]不能超过8000个字符")
    @Pattern(regexp = "^[^\\u4E00-\\u9FFF]*$", message = "[产品卖点]请勿输入中文")
    private String sellingPointProduct;

    /**
     * 参考视频链接
     */
    @ApiModelProperty(value = "参考视频链接")
    @Size(max = 1000, message = "[参考视频链接]不能超过1000个字符")
    private String referenceVideoLink;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）", notes = "1:2张/$10,2:5张/$20")
    @EnumValid(enumClass = PicCountEnum.class, message = "[照片数量]输入错误")
    private Integer picCount;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    @Size(max = 5, message = "[参考图片]最多上传5张")
    private List<String> referencePic = new ArrayList<>();

    /**
     * 视频_关联内容对象
     */
    protected List<OrderVideoContent> orderVideoContents = new ArrayList<>();

    /**
     * 不符合的模特
     */
    @ApiModelProperty(value = "不符合的模特")
    private List<Long> cannotModelIds;

    /**
     * 创建订单时订单序号
     */
    @ApiModelProperty(value = "创建订单时订单序号")
    @NotNull(message = "[创建订单时订单序号]不能为空", groups = CommonValidatedGroup.SaveValidatedGroup.class)
    private Integer serialNumber;

    /**
     * 爬取到的产品图
     */
    @ApiModelProperty(value = "爬取到的产品图")
    private String crawlProductPic;

    /**
     * 参考图片
     */
    @Null(message = "请勿传递[referencePicId]")
    private String referencePicId;

    /**
     * 视频金额（单位：￥）
     */
    @Null(message = "请勿传递[amount]")
    private BigDecimal amount;

    /**
     * 视频金额（单位：$）
     */
    @Null(message = "请勿传递[amountDollar]")
    private BigDecimal amountDollar;
    /**
     * 视频金额（单位：￥）
     */
    @Null(message = "请勿传递[payAmount]")
    private BigDecimal payAmount;

    /**
     * 视频金额（单位：$）
     */
    @Null(message = "请勿传递[payAmountDollar]")
    private BigDecimal payAmountDollar;

    /**
     * 视频活动优惠金额
     */
    @Null(message = "请勿传递[videoPromotionAmount]")
    private BigDecimal videoPromotionAmount;

    /**
     * 视频价格（单位：$）
     */
    @Null(message = "请勿传递[videoPrice]")
    private BigDecimal videoPrice;

    /**
     * 图片费用（单位：$）
     */
    @Null(message = "请勿传递[picPrice]")
    private BigDecimal picPrice;

    /**
     * 佣金代缴税费（单位：$）
     */
    @Null(message = "请勿传递[commissionPaysTaxes]")
    private BigDecimal commissionPaysTaxes;

    /**
     * 手续费（单位：$）
     */
    @Null(message = "请勿传递[exchangePrice]")
    private BigDecimal exchangePrice;

    /**
     * 服务费用（单位：$）
     */
    @Null(message = "请勿传递[servicePrice]")
    private BigDecimal servicePrice;

    /**
     * 当前汇率
     */
    @Null(message = "请勿传递[currentExchangeRate]")
    private BigDecimal currentExchangeRate;

    /**
     * 是否使用的默认汇率（0:不是,1:默认）
     */
    @Null(message = "请勿传递[isDefaultExchangeRate]")
    private Boolean isDefaultExchangeRate;

    /**
     * 创建订单商家id
     */
    @Null(message = "请勿传递[createOrderBusinessId]")
    private Long createOrderBusinessId;

    /**
     * 创建订单登录用户id
     */
    @Null(message = "请勿传递[createOrderBizUserId]")
    private Long createOrderBizUserId;

    /**
     * 创建订单用户id
     */
    @Null(message = "请勿传递[createOrderUserId]")
    private Long createOrderUserId;

    /**
     * 创建订单用户账号
     */
    @Null(message = "请勿传递[createOrderUserAccount]")
    private String createOrderUserAccount;

    /**
     * 创建订单用户名称
     */
    @Null(message = "请勿传递[createOrderUserName]")
    private String createOrderUserName;

    /**
     * 创建订单用户微信名称
     */
    @Null(message = "请勿传递[createOrderUserNickName]")
    private String createOrderUserNickName;

    @Null(message = "请勿传递[videoCartId]")
    private Long videoCartId;

    /**
     * 视频订单参与的活动
     */
    private Set<OrderPromotionDetail> orderPromotionDetails = new HashSet<>();

    @AssertTrue(message = "[意向模特]数量不能超过拍摄数量")
    private boolean isIntentionModelIdsSize() {
        if (intentionModelIds == null || shootCount == null) {
            return true;
        }
        return intentionModelIds.size() <= shootCount;
    }

    @AssertTrue(message = "[拍摄建议]内容不能超过8000个字符")
    private boolean isShootRequiredContentSizeVerify() {
        if (CollUtil.isEmpty(shootRequired)) {
            return true;
        }
        return shootRequired.stream().map(VideoContentDTO::getContent).filter(StrUtil::isNotBlank).map(String::length).reduce(0, Integer::sum) <= 8000;
    }

    @AssertTrue(message = "只有[使用平台]是亚马逊和[拍摄国家]是美国时才有影响者")
    private boolean isAmericanInfluenceVerify() {
        if (ModelTypeEnum.INFLUENT.getCode().equals(modelType)) {
            return NationEnum.USA.getCode().equals(shootingCountry) && PlatformEnum.AMAZON.getCode().equals(platform);
        }
        return true;
    }

    @AssertTrue(message = "只有[使用平台]是亚马逊和[拍摄国家]是美国时才能多选")
    private boolean isAllModelTypeVerify() {
        if (ModelTypeEnum.ALL.getCode().equals(modelType)) {
            return NationEnum.USA.getCode().equals(shootingCountry) && PlatformEnum.AMAZON.getCode().equals(platform);
        }
        return true;
    }

    @AssertTrue(message = "[使用平台]是TikTok，[视频格式]只能选择 竖屏拍摄9:16")
    private boolean isTikTokVerify() {
        if (PlatformEnum.TIKTOK.getCode().equals(platform)) {
            return VideoFormatEnum.PORTRAIT.getCode().equals(videoFormat);
        }
        return true;
    }

    @AssertTrue(message = "[使用平台]是APP/解说类，不能选择[照片数量]和[参考图片]")
    private boolean isAppInterpretiveVerify() {
        if (PlatformEnum.APP.getCode().equals(platform)) {
            return ObjectUtil.isNull(picCount) && CollUtil.isEmpty(referencePic);
        }
        return true;
    }


    @AssertTrue(message = "请上传产品图或填写产品链接")
    private boolean isProductLinkAndProductLinkVerify() {
        return CharSequenceUtil.isNotBlank(productLink) || CharSequenceUtil.isNotBlank(productPic);
    }

    @AssertTrue(message = "亚马逊影响者不能选择[照片数量]")
    private boolean isPicCount() {
        if (ObjectUtil.isNotNull(picCount)) {
            return ModelTypeEnum.AVERAGE_PEOPLE.getCode().equals(modelType) || ModelTypeEnum.ALL.getCode().equals(modelType);
        }
        return true;
    }

    @AssertTrue(message = "选择[拍摄建议]时，[参考视频]应该为空，选择[参考视频]时，[拍摄建议]应该为空", groups = {CommonValidatedGroup.SaveValidatedGroup.class, CommonValidatedGroup.EditValidatedGroup.class})
    private boolean isShootRequired() {
        if (CollUtil.isNotEmpty(shootRequired)) {
            return CharSequenceUtil.isBlank(referenceVideoLink);
        } else if (CharSequenceUtil.isNotBlank(referenceVideoLink)) {
            return CollUtil.isEmpty(shootRequired);
        }
        return true;
    }

    public void initOrderVideoContents() {
        if (CollUtil.isNotEmpty(shootRequired)) {
            VideoContentDTO videoContentDTO = new VideoContentDTO();
            videoContentDTO.setVideoId(id);
            videoContentDTO.setType(VideoContentTypeEnum.REQUIRE.getCode());
            List<String> collect = shootRequired.stream().map(VideoContentDTO::getContent).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            videoContentDTO.setContent(CharSequenceUtil.join(StrPool.LF, collect));
            orderVideoContents.add(BeanUtil.copyProperties(videoContentDTO, OrderVideoContent.class));
        }
        if (CharSequenceUtil.isNotBlank(sellingPointProduct)) {
            VideoContentDTO videoContentDTO = new VideoContentDTO();
            videoContentDTO.setVideoId(id);
            videoContentDTO.setType(VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode());
            videoContentDTO.setContent(sellingPointProduct);
            orderVideoContents.add(BeanUtil.copyProperties(videoContentDTO, OrderVideoContent.class));
        }
    }

    public void formatVideoLink() {
        try {
            Function<String, String> formatLink = link -> {
                if (link == null || link.trim().isEmpty()) {
                    return link;
                }
                String trimmed = link.trim().replaceAll("^'|'$", "");
                // 已有http/https前缀，直接返回
                if (trimmed.startsWith(Constants.HTTP) || trimmed.startsWith(Constants.HTTPS)) {
                    return trimmed;
                }
                // 仅当以www开头时才加https://
                if (trimmed.startsWith("www.")) {
                    return Constants.HTTPS + trimmed;
                }
                // 其他情况保持原样
                return trimmed;
            };

            if (getProductLink() != null) {
                setProductLink(formatLink.apply(getProductLink()));
            }
            if (getReferenceVideoLink() != null) {
                setReferenceVideoLink(formatLink.apply(getReferenceVideoLink()));
            }
        } catch (Exception e) {
            log.info("格式化视频链接错误: {}", e.getMessage());
        }
    }
}
