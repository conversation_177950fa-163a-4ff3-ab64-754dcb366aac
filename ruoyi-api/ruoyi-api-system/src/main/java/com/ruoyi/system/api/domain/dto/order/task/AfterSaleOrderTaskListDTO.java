package com.ruoyi.system.api.domain.dto.order.task;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class AfterSaleOrderTaskListDTO implements Serializable {

    private static final long serialVersionUID = -2171096662700161148L;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "优先级（1:紧急,2:一般）", notes = "1:紧急,2:一般")
    private List<Integer> priority;

    @ApiModelProperty(value = "拍摄模特id")
    private List<Long> shootModelId;

    @ApiModelProperty(value = "售后类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑,11：重拍照片，12：要高清照片，13：补拍照片，14：图片原素材）")
    private List<Integer> afterSaleType;

    @Null(message = "请勿传递[afterSaleVideoTypes]")
    private List<Integer> afterSaleVideoTypes;

    @Null(message = "请勿传递[afterSalePicTypes]")
    private List<Integer> afterSalePicTypes;

    @ApiModelProperty(value = "状态（1：待处理，2：处理中，3：申请取消中，4：已完成，5：已拒绝，6：已关闭）")
    private List<Integer> status;

    @ApiModelProperty(value = "处理人ID")
    private List<Long> assigneeId;

    @ApiModelProperty(value = "提交人ID")
    private List<Long> submitById;

    @ApiModelProperty(value = "售后时间开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date afterSaleTimeBegin;

    @ApiModelProperty(value = "售后时间结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date afterSaleTimeEnd;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    @Null(message = "请勿传递[taskIds]")
    private Collection<Long> taskIds;

    @ApiModelProperty(value = "任务关联人Id列表")
    private List<Long> relevanceUserId;
}
