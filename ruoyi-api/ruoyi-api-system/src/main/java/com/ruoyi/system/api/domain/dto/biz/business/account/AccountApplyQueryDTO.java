package com.ruoyi.system.api.domain.dto.biz.business.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
* 账号申请表
* <AUTHOR>
 * @TableName business_account_apply
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountApplyQueryDTO implements Serializable {

    private static final long serialVersionUID = 7595819103098953049L;

    @NotBlank(message="[主账号]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("主账号")
    @Length(max= 20,message="编码长度不能超过20")
    private String ownerAccount;

    @NotBlank(message="[员工名称]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("员工名称")
    @Length(max= 20,message="编码长度不能超过20")
    private String name;

//    @NotBlank(message="[微信昵称]不能为空")
    @Size(max = 32, message = "微信昵称不能超过32个字")
    @ApiModelProperty("微信昵称")
    @Length(max = 32, message = "微信昵称不能超过32个字")
    private String nickName;

    
    @Size(max= 500,message="编码长度不能超过500")
    @ApiModelProperty("头像")
    @Length(max= 500,message="编码长度不能超过500")
    private String pic;

    @NotBlank(message="[unionid]不能为空")
    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("unionid")
    @Length(max= 32,message="编码长度不能超过32")
    private String unionid;

    @NotBlank(message="[企业微信外部联系人id]不能为空")
    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("企业微信外部联系人id")
    @Length(max= 32,message="编码长度不能超过32")
    private String externalUserId;

    @NotNull(message="[审核状态（0:待审核,1:审核通过,2.拒绝）]不能为空")
    @ApiModelProperty("审核状态（0:待审核,1:审核通过,2.拒绝）")
    private Integer auditStatus;

    @ApiModelProperty("审核状态列表")
    private List<Integer> auditStatusList;

}
