package com.ruoyi.system.api.domain.entity.biz.business.balance;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 商家余额详情流水表
 *
 * <AUTHOR>
 * @TableName business_balance_detail_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceDetailFlow implements Serializable {

    private static final long serialVersionUID = 6366007409240789732L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[商家余额流水ID（business_balance_flow.id）]不能为空")
    @ApiModelProperty("商家余额流水ID（business_balance_flow.id）")
    private Long balanceFlowId;

    @NotBlank(message = "[余额单号（business_balance_detail.number）]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("余额单号（business_balance_detail.number）")
    @Length(max = 32, message = "编码长度不能超过32")
    private String balanceNumber;

    @ApiModelProperty("单号（订单号、提现单号、预付单号）")
    private String number;

    @ApiModelProperty("视频订单ID")
    private Long videoId;

    @ApiModelProperty("视频订单ID")
    private String videoCode;

    @NotNull(message = "[使用余额]不能为空")
    @ApiModelProperty("使用余额")
    private BigDecimal useBalance;

    @NotNull(message = "[订单类型(0-收入、1-支出)]不能为空")
    @ApiModelProperty("订单类型(0-收入、1-支出)")
    private Integer type;

    @ApiModelProperty("创建人id")
    private Long createById;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("创建人名称")
    @Length(max = 30, message = "编码长度不能超过30")
    private String createBy;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;

}
