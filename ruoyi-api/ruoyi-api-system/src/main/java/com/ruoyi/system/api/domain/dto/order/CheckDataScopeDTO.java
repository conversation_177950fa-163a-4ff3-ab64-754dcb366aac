package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.BackUserTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/8 11:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckDataScopeDTO {
    private List<Long> videoIds;
    /**
     * 商家端-同个商家内数据共享    true 共享，false 不共享
     */
    private boolean businessShare;

    /**
     * 商家端-订单提交人 或者 视频订单创建人 可操作
     */
    private boolean submitter;

    /**
     * 校验中文部客服或者英文部客服
     */
    private BackUserTypeEnum backUserType;

    /**
     * 或者是工单处理人
     */
    private boolean workOrderAssignee;
}
