package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.OrderVideoFeedBackTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import javax.validation.groups.Default;
import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Data
public class OrderVideoFeedBackDTO implements Serializable {
    private static final long serialVersionUID = 7848290647000137958L;

    public interface newAddFeedBackValidGroup extends Default {

    }

    /**
     * 模特反馈素材详情ID
     */
    @ApiModelProperty(value = "模特反馈素材详情ID")
    @NotNull(message = "[模特反馈素材详情ID]不能为空", groups = newAddFeedBackValidGroup.class)
    private Long materialInfoId;

    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id", required = true)
    @NotNull(message = "[视频id]不能为空")
    private Long videoId;

    /**
     * 反馈类型（1:视频,2:视频和照片,3:照片）
     */
    @ApiModelProperty(value = "反馈类型（1:视频,2:视频和照片,3:照片）", required = true)
    @NotNull(message = "[反馈类型]不能为空")
    @EnumValid(enumClass = OrderVideoFeedBackTypeEnum.class, message = "[反馈类型]输入错误")
    private Integer type;

    /**
     * 视频地址
     */
    @ApiModelProperty("视频地址")
    @Pattern(regexp = "^https://.*$", message = "[视频地址]格式不正确")
    @Size(max = 1000, message = "[视频地址]长度不能超过1000个字符")
    private String videoUrl;

    /**
     * 图片地址
     */
    @ApiModelProperty("图片地址")
    @Pattern(regexp = "^https://.*$", message = "[图片地址]格式不正确")
    @Size(max = 1000, message = "[图片地址]长度不能超过1000个字符")
    private String picUrl;

    /**
     * 视频评分
     */
    @ApiModelProperty("视频评分")
    @Digits(integer = 2, fraction = 1, message = "[视频评分]只能是整数或小数点后一位")
    @DecimalMin(value = "0.5", message = "[视频评分]不能小于0.5")
    @DecimalMax(value = "10.0", message = "[视频评分]不能大于10.0")
    private Float videoScore;

    /**
     * 评价内容
     */
    @ApiModelProperty("评价内容")
    @Size(max = 300, message = "[评价内容]长度不能超过300个字符")
    private String videoScoreContent;

    /**
     * 关联任务ID
     */
    @ApiModelProperty(value = "关联任务ID")
    private List<Long> taskDetailIds;

    /**
     * 修改的记录ID
     */
    @ApiModelProperty("修改的记录ID")
    private Long modifyId;

    /**
     * 修改理由
     */
    @ApiModelProperty("修改理由")
    @Size(max = 300, message = "[修改理由]长度不能超过300个字符")
    private String modifyReason;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    @Null(message = "请勿传递[rollbackId]")
    private Long rollbackId;

    @AssertTrue(message = "[反馈类型]对应的必填项未填写或填写的多余的信息")
    private boolean isTypeVerify() {
        if (OrderVideoFeedBackTypeEnum.VIDEO.getCode().equals(type)) {
            return CharSequenceUtil.isNotBlank(videoUrl) && CharSequenceUtil.isBlank(picUrl);
        } else if (OrderVideoFeedBackTypeEnum.VIDEO_PIC.getCode().equals(type)) {
            return CharSequenceUtil.isNotBlank(videoUrl) && CharSequenceUtil.isNotBlank(picUrl);
        } else if (OrderVideoFeedBackTypeEnum.PIC.getCode().equals(type)) {
            return CharSequenceUtil.isNotBlank(picUrl) && CharSequenceUtil.isBlank(videoUrl);
        }
        return false;
    }

    @AssertTrue(message = "修改记录时[修改理由]不能为空或非修改时[修改理由]必须为空")
    private boolean isModifyIdVerify() {
        if (ObjectUtil.isNotNull(modifyId)) {
            return CharSequenceUtil.isNotBlank(modifyReason);
        } else {
            return CharSequenceUtil.isBlank(modifyReason);
        }
    }
}
