package com.ruoyi.system.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/8 14:13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SectionDTO implements Serializable {
    private static final long serialVersionUID = -1236929330070667445L;

    /**
     * 开始区间
     */
    @ApiModelProperty(value = "开始区间")
    private BigDecimal begin;

    /**
     * 结束区间
     */
    @ApiModelProperty(value = "结束区间")
    private BigDecimal end;
}
