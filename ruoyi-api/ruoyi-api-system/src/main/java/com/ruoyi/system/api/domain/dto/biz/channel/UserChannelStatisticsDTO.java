package com.ruoyi.system.api.domain.dto.biz.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-29 10:36
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserChannelStatisticsDTO implements Serializable {
    private static final long serialVersionUID = 3074916333562807173L;

    @ApiModelProperty("渠道类型")
    @NotNull(message = "渠道类型不能为空")
    private Integer channelType;

    @ApiModelProperty("查询渠道列表")
    private List<Long> channelIds;

}
