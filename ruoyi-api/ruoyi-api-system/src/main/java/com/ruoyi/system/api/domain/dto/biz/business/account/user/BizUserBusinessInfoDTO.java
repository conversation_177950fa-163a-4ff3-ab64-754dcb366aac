package com.ruoyi.system.api.domain.dto.biz.business.account.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 商家账号数据
 * @create :2024-08-31 19:28
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BizUserBusinessInfoDTO implements Serializable {
    private static final long serialVersionUID = 42638904350137986L;

    @ApiModelProperty("登录账号主键id")
    private Long id;

    @ApiModelProperty("账号类型：账号状态：0-普通账号，1-主账号，2-子账号")
    private Integer accountType;

    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;

    @ApiModelProperty("客服id")
    private Long waiterId;

}
