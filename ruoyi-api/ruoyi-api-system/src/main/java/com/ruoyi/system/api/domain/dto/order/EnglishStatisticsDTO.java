package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-04-01 09:51
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EnglishStatisticsDTO implements Serializable {

    private static final long serialVersionUID = -7544187893810807755L;

    @ApiModelProperty(value = "模特ID列表")
    private Set<Long> modelIds;
}
