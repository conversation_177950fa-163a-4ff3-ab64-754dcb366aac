package com.ruoyi.system.api.domain.dto.biz.business.account.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
* 登录用户表
* <AUTHOR>
 * @TableName biz_user
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BizUserDTO implements Serializable {

    private static final long serialVersionUID = 5040642647752241015L;

    @ApiModelProperty("对接客服")
    private Long waiterId;

    @ApiModelProperty("客户类型 （2-普通客户 0-一般客户 1-重要客户）")
    private Integer customerType;

    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;

    @ApiModelProperty("账号名称")
    private String name;

//    @NotBlank(message="[微信昵称]不能为空")
    @Size(max = 32, message = "微信昵称不能超过32个字")
    @ApiModelProperty("微信昵称")
    @Length(max = 32, message = "微信昵称不能超过32个字")
    private String nickName;

    
    @Size(max= 500,message="编码长度不能超过500")
    @ApiModelProperty("头像")
    @Length(max= 500,message="编码长度不能超过500")
    private String pic;

    @NotBlank(message="[unionId]不能为空")
    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("unionId")
    @Length(max= 32,message="编码长度不能超过32")
    private String unionid;

    @NotBlank(message="[手机号]不能为空")
    @Size(max= 15,message="编码长度不能超过15")
    @ApiModelProperty("手机号")
    @Length(max= 15,message="编码长度不能超过15")
    private String phone;

    @NotBlank(message="[ExternalUserID企业微信外部联系人id]不能为空")
    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("ExternalUserID企业微信外部联系人id")
    @Length(max= 32,message="编码长度不能超过32")
    private String externalUserId;

    private String connectUserName;
}
