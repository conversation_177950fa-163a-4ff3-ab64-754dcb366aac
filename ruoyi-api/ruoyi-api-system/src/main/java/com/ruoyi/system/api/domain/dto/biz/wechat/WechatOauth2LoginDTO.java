package com.ruoyi.system.api.domain.dto.biz.wechat;

import com.ruoyi.common.core.enums.WxChatLoginStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 手机端页面登录信息
 *
 * <AUTHOR>
 * @date 2024/6/19
 */
@Data
@Builder
public class WechatOauth2LoginDTO implements Serializable {

    private static final long serialVersionUID = -7509431176249919318L;
    @ApiModelProperty("登录状态")
    WxChatLoginStatusEnum loginStatus;

    @ApiModelProperty("还需手机号 0:否,1:是")
    Integer phone;

    @ApiModelProperty("跳转类型 1:跳转至链接,2:展示二维码")
    Integer type;

    @ApiModelProperty("链接")
    String url;

    public static Integer TYPE_LINK = 1;

    public static Integer TYPE_QRCODE = 2;

    public WechatOauth2LoginDTO() {
    }

    public WechatOauth2LoginDTO(WxChatLoginStatusEnum loginStatus) {
        this.loginStatus = loginStatus;
    }

    public WechatOauth2LoginDTO(WxChatLoginStatusEnum loginStatus, Integer type, String url) {
        this.loginStatus = loginStatus;
        this.type = type;
        this.url = url;
    }

    public WechatOauth2LoginDTO(WxChatLoginStatusEnum loginStatus, Integer phone, Integer type, String url) {
        this.loginStatus = loginStatus;
        this.phone = phone;
        this.type = type;
        this.url = url;
    }
}
