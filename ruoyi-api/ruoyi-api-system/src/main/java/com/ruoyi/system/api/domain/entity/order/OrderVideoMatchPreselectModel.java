package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单_视频_预选模特对象 order_video_match_preselect_model
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@ApiModel(value = "订单_视频_预选模特对象 order_video_match_preselect_model")
@TableName("order_video_match_preselect_model")
@Data
public class OrderVideoMatchPreselectModel implements Serializable {

    private static final long serialVersionUID = -2986495825725948618L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    private Long videoId;

    /**
     * 匹配单ID(FK:order_video_match.id)
     */
    @ApiModelProperty(value = "匹配单ID", required = true)
    private Long matchId;

    /**
     * 添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发,5:排单推荐,6:凑单推荐）
     */
    @ApiModelProperty(value = "添加方式（1:意向模特,2:模特自选,3:运营添加）", notes = "1:意向模特,2:模特自选,3:运营添加,4:客服分发,5:排单推荐,6:凑单推荐")
    @Excel(name = "添加方式", readConverterExp = "1:意向模特,2:模特自选,3:运营添加")
    private Integer addType;

    /**
     * 添加人id
     */
    @ApiModelProperty(value = "添加人id")
    @Excel(name = "添加人id")
    private Long addUserId;

    /**
     * 添加时间
     */
    @ApiModelProperty(value = "添加时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "添加时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id")
    @Excel(name = "模特id")
    private Long modelId;

    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String modelAccount;

    /**
     * 模特名称
     */
    @ApiModelProperty(value = "模特名称")
    private String modelName;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型(0:影响者,1:素人)")
    private Integer modelType;

    /**
     * 模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private String modelPlatform;

    /**
     * 模特合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "模特合作深度(0:一般模特,1:优质模特,2:中度模特)")
    private Integer modelCooperation;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)", required = true)
    private BigDecimal modelCooperationScore;

    /**
     * 模特对接人ID
     */
    @ApiModelProperty(value = "模特对接人ID")
    private Long modelPersonId;

    /**
     * 模特对接人名称
     */
    @ApiModelProperty(value = "模特对接人名称")
    private String modelPersonName;

    /**
     * 状态（0:未对接,1:已对接,2:已选定,3:已淘汰）
     */
    @ApiModelProperty(value = "状态（0:未对接,1:已对接,2:已选定,3:已淘汰）", notes = "0:未对接,1:已对接,2:已选定,3:已淘汰")
    @Excel(name = "状态", readConverterExp = "0:未对接,1:已对接,2:已选定,3:已淘汰")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remark;

    /**
     * 淘汰类型（1:商家驳回,2:客服淘汰,3:MT不想要,4:未被选中,5:超时未选择意向,6:订单回退,7:合作状态变更,8:暂停匹配,9:交易关闭）
     */
    @ApiModelProperty(value = "淘汰类型（1:商家驳回,2:客服淘汰,3:MT不想要,4:未被选中,5:超时未选择意向,6:订单回退,7:合作状态变更,8:暂停匹配,9:交易关闭）")
    @Excel(name = "淘汰类型（1:商家驳回,2:客服淘汰,3:MT不想要,4:未被选中,5:超时未选择意向,6:订单回退,7:合作状态变更,8:暂停匹配,9:交易关闭）")
    private Integer oustType;

    /**
     * 淘汰时间
     */
    @ApiModelProperty(value = "淘汰时间")
    private Date oustTime;

    /**
     * 淘汰操作商家运营用户ID
     */
    @ApiModelProperty(value = "淘汰操作商家运营用户ID")
    private Long oustOperatorId;

    /**
     * 淘汰操作商家运营用户名称
     */
    @ApiModelProperty(value = "淘汰操作商家运营用户名称")
    private String oustOperatorName;

    /**
     * 淘汰操作商家运营用户微信名称
     */
    @ApiModelProperty(value = "淘汰操作商家运营用户微信名称")
    private String oustOperatorNickName;

    /**
     * 确认淘汰时间
     */
    @ApiModelProperty(value = "确认淘汰时间")
    private Date confirmOustTime;

    @ApiModelProperty(value = "是否暂停淘汰(0-否,1-是)")
    private Integer isPauseOust;
    /**
     * 模特意向(1:待MT确认,2:MT想要,3:MT不想要,4:未确认,5:超时未选择)
     */
    @ApiModelProperty(value = "模特意向")
    private Integer modelIntention;

    /**
     * 意向模特选择超时时间
     */
    @ApiModelProperty(value = "意向模特选择超时时间")
    private Date selectTimeout;

    /**
     * 意向模特超时处理时间
     */
    @ApiModelProperty(value = "意向模特超时处理时间")
    private Date processTime;

    /**
     * 选定时间
     */
    @ApiModelProperty(value = "选定时间")
    private Date selectedTime;

    /**
     * 模特选择状态（0:待处理,1:正在审核中,2:过期未确认,3:卖方取消,4:您已拒绝,5:已确认拍摄,6:您已取消申请）
     */
    @ApiModelProperty(value = "模特选择状态")
    private Integer selectStatus;

    /**
     * 模特选择时间
     */
    @ApiModelProperty(value = "模特选择时间")
    private Date selectTime;

    /**
     * 拍摄模特注意事项
     */
    @ApiModelProperty(value = "拍摄模特注意事项")
    private String shootAttention;

    /**
     * 需要提醒拍摄模特注意事项
     */
    @ApiModelProperty(value = "需要提醒拍摄模特注意事项")
    private Integer needRemindShootAttention;

    /**
     * 分发结果（1：待处理，2：MT想要，3：MT不想要，4：取消分发）
     */
    @ApiModelProperty(value = "分发结果（1：待处理，2：MT想要，3：MT不想要，4：取消分发）")
    private Integer distributionResult;

    /**
     * 分发结果原因（1：客服取消，2：订单回退，3：订单暂停匹配，4：模特行程中，5：模特暂停合作，6：模特取消合作，7：未确认，8：交易关闭）
     */
    @ApiModelProperty(value = "分发结果原因（1：客服取消，2：订单回退，3：订单暂停匹配，4：模特行程中，5：模特暂停合作，6：模特取消合作，7：未确认，8：交易关闭，9：模特已下架，10：模特有逾期订单，11：商家拉黑模特）")
    private Integer distributionResultCause;

    /**
     * 分发结果时间
     */
    @ApiModelProperty(value = "分发结果时间")
    private Date distributionResultTime;

    /**
     * 分发结果时间
     */
    @ApiModelProperty(value = "分发结果时间")
    private String objectKey;

    /**
     * 拍摄注意事项对象存储键值
     */
    @ApiModelProperty("拍摄注意事项对象存储键值")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String shootAttentionObjectKey;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
