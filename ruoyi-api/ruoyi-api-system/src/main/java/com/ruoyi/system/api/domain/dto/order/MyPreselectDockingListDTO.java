package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.OrderPoolPreselectedTimeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Null;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/16 17:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MyPreselectDockingListDTO extends OrderPoolListDTO {
    private static final long serialVersionUID = -5161136125046037493L;

    /**
     * 添加预选时间
     */
    @ApiModelProperty(value = "添加预选时间（1:24小时内,2:,1~2天,3:2~3天,4:3天以上）")
    @EnumValid(enumClass = OrderPoolPreselectedTimeEnum.class, message = "[添加预选时间]输入错误")
    private List<Integer> addPreselectTimes;

    /**
     * 模特意向(1:待MT确认,2:MT想要,3:MT不想要,4:未确认,5:超时未选择)
     */
    @ApiModelProperty(value = "模特意向(1:待MT确认,2:MT想要,3:MT不想要,4:未确认,5:超时未选择)")
    private List<Integer> modelIntentions;

    /**
     * 状态（0:未对接,1:已对接,2:已选定,3:已淘汰）
     */
    @ApiModelProperty(value = "沟通状态（0:未对接,1:已对接,2:已选定,3:已淘汰）")
    private Integer status;

    @ApiModelProperty(value = "沟通状态（0:未对接,1:已对接,2:已选定,3:已淘汰）")
    private List<Integer> statusList;

    /**
     * 匹配单IDS
     */
    @Null(message = "请勿传递[matchIds]")
    private Collection<Long> matchIds;

    /**
     * 添加预选时间开始
     */
    @Null(message = "请勿传递[searchMap]")
    private Map<String,String> searchMap;

    /**
     * 匹配单开始时间
     */
    @ApiModelProperty(value = "匹配单开始时间（1:24小时内,2:,1~2天,3:2~3天,4:3天以上）")
    private List<Integer> matchStartTimes;

    /**
     * 当前登录用户ID admin不设置
     */
    @Null(message = "请勿传递[currentUserId]")
    private Long currentUserId;

    /**
     * 添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发,5:排单推荐,6:凑单推荐）
     */
    @ApiModelProperty(value = "来源（1:意向模特,2:模特自选,3:运营添加,4:客服分发,5:排单推荐,6:凑单推荐）")
    private List<Integer> addTypes;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    private List<Integer> carryType;

    /**
     * 添加预选时间
     */
    @Null(message = "请勿传递[addPreselectTimeMap]")
    private Map<String, String> addPreselectTimeMap;

    /**
     * 预选模特ID
     */
    @ApiModelProperty(value = "预选模特ID")
    private Collection<Long> preselectModelIds;

    /**
     * 模特选定状态 0:未选定模特,1:已选定模特
     */
    @ApiModelProperty(value = "模特选定状态 0:未选定模特,1:已选定模特")
    private Integer modelSelectionStatus;
}
