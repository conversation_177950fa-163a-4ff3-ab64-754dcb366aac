package com.ruoyi.system.api.domain.entity.biz.channel.member;

import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会员种草提现_种草记录关联表
 *
 * <AUTHOR>
 * @TableName member_seed_record_relevance
 */
@Data
public class MemberSeedRecordRelevance implements Serializable {

    private static final long serialVersionUID = -8705791093036578005L;

    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[种草记录id FK member_seed_record.id]不能为空")
    @ApiModelProperty("种草记录id FK member_seed_record.id")
    private Long memberSeedRecordId;

    @NotNull(message = "[会员种草提现id  FK member_seed_record_withdrawal.id]不能为空")
    @ApiModelProperty("会员种草提现id  FK member_seed_record_withdrawal.id")
    private Long memberSeedRecordWithdrawalId;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;

}
