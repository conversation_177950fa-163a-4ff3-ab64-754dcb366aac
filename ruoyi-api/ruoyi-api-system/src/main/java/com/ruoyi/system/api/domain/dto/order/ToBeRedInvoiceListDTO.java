package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/7 20:46
 */
@Data
public class ToBeRedInvoiceListDTO implements Serializable {
    private static final long serialVersionUID = 2777385707937170935L;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 红冲原因（1：重开发票，2：商家提现）
     */
    @ApiModelProperty(value = "红冲原因（1：重开发票，2：商家提现）")
    private Integer invoiceRedCause;

    /**
     * 订单类型（0:视频订单,1:会员订单）
     */
    @ApiModelProperty(value = "订单类型（0:视频订单,1:会员订单）")
    private Integer orderType;

    /**
     * 申请时间-开始
     */
    @ApiModelProperty(value = "申请时间-开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date applyTimeBegin;

    /**
     * 申请时间-结束
     */
    @ApiModelProperty(value = "申请时间-结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date applyTimeEnd;
}
