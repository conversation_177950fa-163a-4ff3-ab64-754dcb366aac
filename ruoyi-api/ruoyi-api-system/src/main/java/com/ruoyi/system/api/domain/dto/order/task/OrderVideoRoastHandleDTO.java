package com.ruoyi.system.api.domain.dto.order.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/27
 */
@Data
public class OrderVideoRoastHandleDTO implements Serializable {

    private static final long serialVersionUID = 6220716850654606270L;

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "[主键id]不能为空")
    private Long id;

    @ApiModelProperty(value = "处理结果")
    @NotBlank(message = "[处理结果]不能为空")
    @Size(max= 1000,message="[处理结果]长度不能超过1000")
    private String handleResult;
}
