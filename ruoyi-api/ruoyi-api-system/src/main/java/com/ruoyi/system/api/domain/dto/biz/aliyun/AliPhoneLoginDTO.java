package com.ruoyi.system.api.domain.dto.biz.aliyun;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :
 * @description :
 * @create :2023-09-19 13:53
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AliPhoneLoginDTO implements Serializable {
    private static final long serialVersionUID = -8797332829562514872L;

    @NotBlank(message = "[手机号]不能为空")
    @ApiModelProperty("手机号")
    private String phone;

    @NotBlank(message = "[spToken]不能为空")
    @ApiModelProperty("spToken")
    private String spToken;

    @ApiModelProperty("ticket")
    private String ticket;

    @ApiModelProperty("专属链接code")
    private String linkCode;

}
