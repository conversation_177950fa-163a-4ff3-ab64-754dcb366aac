package com.ruoyi.system.api.domain.entity.biz.channel.member;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 会员种草提现
 *
 * <AUTHOR>
 * @TableName member_seed_record_withdrawal
 */
@Data
public class MemberSeedRecordWithdrawal implements Serializable {

    private static final long serialVersionUID = -6919077643422729602L;

    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotBlank(message = "[提现单号]不能为空")
    @Size(max = 16, message = "编码长度不能超过16")
    @ApiModelProperty("提现单号")
    @Length(max = 16, message = "编码长度不能超过16")
    private String withdrawalNum;

    @ApiModelProperty("渠道id")
    private Long channelId;

    @NotBlank(message = "[种草官ID（distribution_channel.seed_id）]不能为空")
    @Size(max = 16, message = "编码长度不能超过16")
    @ApiModelProperty("种草官ID（distribution_channel.seed_id）")
    @Length(max = 16, message = "编码长度不能超过16")
    private String channelSeedId;

    @ApiModelProperty("渠道种草码")
    private String channelSeedCode;

    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @NotNull(message = "[申请结算金额（所有种草记录和）]不能为空")
    @ApiModelProperty("申请结算金额（所有种草记录和）")
    private BigDecimal settleAmount;

    @NotNull(message = "[提现账号类型]不能为空")
    @ApiModelProperty("提现账号类型(2-支付宝，3-银行卡，6-公户收款)")
    private Integer withdrawalAccountType;

    @NotBlank(message = "[收款方姓名]不能为空")
    @Size(max = 16, message = "编码长度不能超过16")
    @ApiModelProperty("收款方姓名")
    @Length(max = 16, message = "编码长度不能超过16")
    private String payeeName;

    @NotBlank(message = "[收款方手机号]不能为空")
    @Size(max = 16, message = "编码长度不能超过16")
    @ApiModelProperty("收款方手机号")
    @Length(max = 16, message = "编码长度不能超过16")
    private String payeePhone;

    @NotBlank(message = "[收款方身份证号]不能为空")
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("收款方身份证号")
    @Length(max = 20, message = "编码长度不能超过20")
    private String payeeIdentityCard;

    @ApiModelProperty("开户行名称")
    private String bankName;

    @NotBlank(message = "[收款方账号]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("收款方账号")
    @Length(max = 32, message = "编码长度不能超过32")
    private String payeeAccount;

    @NotNull(message = "[状态（2-待审核，3-待打款，4-已打款，5-审核不通过，6-打款异常）]不能为空")
    @ApiModelProperty("状态（2-待审核，3-待打款，4-已打款，5-审核不通过，6-打款异常）")
    private Integer status;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @Size(max = 300, message = "编码长度不能超过300")
    @ApiModelProperty("审核备注")
    @Length(max = 300, message = "编码长度不能超过300")
    private String auditRemark;

    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("审核人员名称")
    @Length(max = 32, message = "编码长度不能超过32")
    private String auditUserName;

    @ApiModelProperty("提现审核时间")
    private Date withdrawalTime;

    @ApiModelProperty("打款时间")
    private Date payoutTime;

    @Size(max = 300, message = "编码长度不能超过300")
    @ApiModelProperty("提现备注")
    @Length(max = 300, message = "编码长度不能超过300")
    private String withdrawalRemark;

    @ApiModelProperty("提现人员id FK sys_user.user_id")
    private Long withdrawalUserId;

    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("提现人员名称")
    @Length(max = 32, message = "编码长度不能超过32")
    private String withdrawalUserName;

    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("打款账号")
    @Length(max = 32, message = "编码长度不能超过32")
    private String payAccount;

    @Size(max = 500, message = "编码长度不能超过500")
    @ApiModelProperty("打款凭证url")
    @Length(max = 500, message = "编码长度不能超过500")
    private String resourceUrl;

    @ApiModelProperty("申请时间")
    private Date createTime;

    @NotNull(message = "[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    private Date updateTime;
}
