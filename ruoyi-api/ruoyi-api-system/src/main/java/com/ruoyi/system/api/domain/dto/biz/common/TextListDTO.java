package com.ruoyi.system.api.domain.dto.biz.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:38
 */
@Data
public class TextListDTO implements Serializable {
    private static final long serialVersionUID = -6425026229562472264L;
    /**
     * 文本名称
     */
    @ApiModelProperty(value = "文本名称")
    private String name;
    /**
     * 文本id
     */
    @ApiModelProperty(value = "文本id")
    private Long id;

    @Null(message = "请勿传递[文本类型]参数")
    private Integer type;
}
