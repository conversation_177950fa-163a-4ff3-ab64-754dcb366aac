package com.ruoyi.system.api.domain.dto.order.pay;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/8 10:57
 */
@Data
public class PayInfoOrderExportDTO implements Serializable {
    private static final long serialVersionUID = 3104665163563783571L;


    /**
     * 订单号
     */
    private String orderNum;

    /**
     * 下单时间
     */
    private String orderTime;

    /**
     * 订单合计
     */
    private String payAmount;

    /**
     * 订单优惠合计
     */
    private String orderPromotionAmount;

    /**
     * 合计USD
     */
    private String totalUSD;

    /**
     * 合计CNY
     */
    private String totalCNY;

    /**
     * 视频订单费用明细
     */
    private List<PayInfoVideoExportDTO> payInfoVideoExportDTOS;

    private String[][] tableData;
}
