package com.ruoyi.system.api.domain.entity.biz.business;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 商家账号表
 * <AUTHOR>
 * @TableName business_account
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessAccount implements Serializable {
    private static final long serialVersionUID = 6009588716943403414L;

    @NotNull(message="[主键ID]不能为空")
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 账号
     */
    @NotNull(message="[账号]不能为空")
    @ApiModelProperty("账号")
    private String account;

    @NotNull(message="[密码]不能为空")
    @ApiModelProperty("密码")
    @Size(max= 200, message="编码长度不能超过200")
    @Length(max= 200, message="编码长度不能超过200")
    private String password;
    /**
     * 商家id
     */
    @NotNull(message="[商家id]不能为空")
    @ApiModelProperty("商家id")
    private Long businessId;

    @NotNull(message="[登录用户ID]不能为空")
    @ApiModelProperty("登录用户ID")
    private Long bizUserId;

    @ApiModelProperty("是否主账号")
    private Integer isOwnerAccount;

    /**
     * 主账号
     */
    @ApiModelProperty("主账号")
    private String ownerAccount;

    /**
     * 主账号用户ID
     */
    @ApiModelProperty("主账号用户ID")
    private Long ownerAccountBizUserId;
    /**
     * 名称
     */
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("名称")
    @Length(max= 20,message="编码长度不能超过20")
    private String name;

    /**
     * 账号状态（0正常 1禁用）
     */
    @NotNull(message="[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;
    /**
     * 最后登录时间
     */
    @ApiModelProperty("最后登录时间")
    private Date lastLoginTime;
    /**
     * 创建时间
     */
    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改时间
     */
    @NotNull(message="[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
