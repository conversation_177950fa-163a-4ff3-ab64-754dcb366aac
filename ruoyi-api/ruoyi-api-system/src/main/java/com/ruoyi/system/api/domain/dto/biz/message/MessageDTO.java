package com.ruoyi.system.api.domain.dto.biz.message;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :消息内容
 * @create :2025-02-24 10:17
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageDTO implements Serializable {
    private static final long serialVersionUID = -9064599260085466376L;
    @ApiModelProperty("消息类型：1-模特选单")
    private Integer type;

    @ApiModelProperty("地址")
    private String url;

    @ApiModelProperty("选单模特ID")
    private Long modelId;
}
