package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/4 18:38
 */
@Data
public class OrderModelMyOrderListDTO implements Serializable {
    private static final long serialVersionUID = -2671960776287404539L;
    /**
     * 订单状态（6:Pending completion,8:Completed,9:Seller Cancellation,18:After sales）
     */
    @ApiModelProperty(value = "订单状态（6:Pending completion,8:Completed,9:Seller Cancellation,18:After sales）")
    private Integer status;

    /**
     * 模特是否回复被驳回的素材（0:已回复,1:未回复）
     */
    @ApiModelProperty(value = "模特是否回复被驳回的素材（0:已回复,1:未回复）")
    private Integer replyStatus;

    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long videoId;

    /**
     * 当前登录模特id
     */
    @Null(message = "不接收此参数，请检查后重试")
    private Long curModelId;

    /**
     * 驳回状态（0:正常,1:驳回）
     */
    @Null(message = "不接收此参数，请检查后重试")
    private Integer rejectStatus;

    public void init() {
        if (status != null && status == 18) {
            status = 6;
            rejectStatus = 1;
        }
    }
}
