package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.ModelTypeEnum;
import com.ruoyi.common.core.enums.NationEnum;
import com.ruoyi.common.core.enums.VideoContentTypeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderOperationVideoCaseDTO implements Serializable {
    private static final long serialVersionUID = -214125091678502265L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "[视频订单id]不能为空")
    private Long id;

    /**
     * 反馈id
     */
    @ApiModelProperty(value = "反馈id")
    @NotNull(message = "[反馈id]不能为空")
    private Long caseId;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国")
    @NotNull(message = "[拍摄国家]不能为空")
    @EnumValid(enumClass = NationEnum.class, message = "[拍摄国家]输入错误")
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    @NotNull(message = "[模特类型]不能为空")
    @EnumValid(enumClass = ModelTypeEnum.class, message = "[模特类型]输入错误")
    private Integer modelType;

    /**
     * 拍摄建议（原拍摄要求）
     */
    @ApiModelProperty("拍摄建议（原拍摄要求）")
    @Valid
    private List<VideoContentDTO> shootRequired;

    /**
     * 模特要求（原匹配模特注意事项）
     */
    @ApiModelProperty("模特要求（原匹配模特注意事项）")
    @Valid
    private OrderVideoCautionsDTO orderVideoCautionsDTO;

    /**
     * 视频_关联内容对象
     */
    private List<OrderVideoContent> orderVideoContents = new ArrayList<>();

    public void init() {
        if (CollUtil.isNotEmpty(shootRequired)) {
            for (VideoContentDTO caution : shootRequired) {
                caution.setVideoId(id);
                caution.setType(VideoContentTypeEnum.REQUIRE.getCode());
            }
            shootRequired.removeIf(item -> CharSequenceUtil.isBlank(item.getContent()));
            orderVideoContents.addAll(BeanUtil.copyToList(shootRequired, OrderVideoContent.class));
        }
        if (ObjectUtil.isNotNull(orderVideoCautionsDTO) && CollUtil.isNotEmpty(orderVideoCautionsDTO.getCautions())) {
            for (VideoContentDTO caution : orderVideoCautionsDTO.getCautions()) {
                caution.setVideoId(id);
                caution.setType(VideoContentTypeEnum.CAUTIONS.getCode());
            }
            orderVideoCautionsDTO.getCautions().removeIf(item -> CharSequenceUtil.isBlank(item.getContent()));
            orderVideoContents.addAll(BeanUtil.copyToList(orderVideoCautionsDTO.getCautions(), OrderVideoContent.class));
        }
    }
}
