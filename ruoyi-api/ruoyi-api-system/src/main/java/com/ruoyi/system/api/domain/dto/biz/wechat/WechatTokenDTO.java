package com.ruoyi.system.api.domain.dto.biz.wechat;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/27
 **/

@Data
public class WechatTokenDTO implements Serializable {
    private static final long serialVersionUID = -7906209187139187593L;
    private String openid;

    private String sessionKey;

    private String unionid;

    private Integer errcode;

//    是否为快照页模式虚拟账号，只有当用户是快照页模式虚拟账号时返回，值为1
    private Integer isSnapshotuser;

    public static Integer SNAP_USER_FLAG = 1;

    private String errmsg;

    @Override
    public String toString() {
        return "WechatTokenDTO{" +
                "openid='" + openid + '\'' +
                ", sessionKey='" + sessionKey + '\'' +
                ", unionid='" + unionid + '\'' +
                ", errcode=" + errcode +
                ", errmsg='" + errmsg + '\'' +
                '}';
    }
}
