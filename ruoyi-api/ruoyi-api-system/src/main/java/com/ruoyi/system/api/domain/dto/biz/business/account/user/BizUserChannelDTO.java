package com.ruoyi.system.api.domain.dto.biz.business.account.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-27 11:47
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BizUserChannelDTO implements Serializable {
    private static final long serialVersionUID = -2161227852250556879L;

    @NotNull(message = "[用户ID (FK:biz_user.id)]不能为空")
    @ApiModelProperty("用户ID (FK:biz_user.id)")
    private Long bizUserId;

    @ApiModelProperty("注册渠道类型(0=普通,1=市场，2=分销)")
    private Integer registerChannelType;

    @ApiModelProperty("注册渠道账户id")
    private Long registerChannelId;

    @ApiModelProperty("注册时间")
    private Date registerTime;

    @ApiModelProperty("企微渠道类型(0=普通,1=市场，2=分销)")
    private Integer wechatChannelType;

    @ApiModelProperty("添加企微渠道账户id")
    private Long wechatChannelId;

    @ApiModelProperty("添加企微时间")
    private Date addWechatTime;
}
