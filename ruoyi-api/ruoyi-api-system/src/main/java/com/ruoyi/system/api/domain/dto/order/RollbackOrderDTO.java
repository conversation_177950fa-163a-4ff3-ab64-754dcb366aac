package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/25 17:59
 */
@Data
public class RollbackOrderDTO implements Serializable {
    private static final long serialVersionUID = 3347571104323933868L;

    /**
     * 视频订单ID
     */
    @ApiModelProperty(value = "视频订单ID", required = true)
    @NotNull(message = "[视频订单ID]不能为空")
    private Long videoId;

    /**
     * 回退原因
     */
    @ApiModelProperty(value = "回退原因", required = true)
    @NotBlank(message = "[回退原因]不能为空")
    @Size(max = 1000, message = "[回退原因]长度不能超过1000个字符")
    private String cause;
}
