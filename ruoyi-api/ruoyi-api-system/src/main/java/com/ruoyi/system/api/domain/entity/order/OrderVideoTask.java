package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/9 15:06
 */
@TableName("order_video_task")
@Data
public class OrderVideoTask implements Serializable {
    private static final long serialVersionUID = -8797952861691444659L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频订单id FK:order_video.id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long videoId;

    /**
     * 任务单类型（1：售后单，2：工单）
     */
    @ApiModelProperty(value = "任务单类型（1：售后单，2：工单）")
    private Integer taskType;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
