package com.ruoyi.system.api.domain.entity.order.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 活动信息表
 *
 * <AUTHOR>
 * @TableName promotion_activity
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PromotionActivity implements Serializable {

    private static final long serialVersionUID = 370339656993365470L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[优惠活动类型]不能为空")
    @ApiModelProperty("详见PromotionActivityTypeEnum")
    private Integer type;

    @NotBlank(message = "[活动名称]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("活动名称")
    @Length(max = 50, message = "编码长度不能超过50")
    private String activityName;

    @NotNull(message = "[开始时间]不能为空")
    @ApiModelProperty("开始时间")
    private Date startTime;

    @NotNull(message = "[结束时间]不能为空")
    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("活动状态（0-无效，1-有效）")
    private Integer activityStatus;

    @Size(max = 300, message = "编码长度不能超过300")
    @ApiModelProperty("备注")
    @Length(max = 300, message = "编码长度不能超过300")
    private String remark;

    @ApiModelProperty("创建人id")
    private Long createUserId;

    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("创建人名称")
    @Length(max = 32, message = "编码长度不能超过32")
    private String createUserName;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改人id")
    private Long updateUserId;

    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("修改人名称")
    @Length(max = 32, message = "编码长度不能超过32")
    private String updateUserName;

    @NotNull(message = "[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    private Date updateTime;

}
