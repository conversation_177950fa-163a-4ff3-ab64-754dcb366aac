package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.OrderPoolPreselectedTimeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/21 11:47
 */
@Data
public class MyPreselectFailListDTO implements Serializable {
    private static final long serialVersionUID = 5951860781592621073L;

    /**
     * 关键词
     */
    @ApiModelProperty(value = "关键词")
    private String keyword;

    /**
     * 淘汰时间
     */
    @ApiModelProperty(value = "淘汰时间（1:24小时内,2:,1~2天,3:2~3天,4:3天以上）")
    @EnumValid(enumClass = OrderPoolPreselectedTimeEnum.class, message = "[淘汰时间]输入错误")
    private List<Integer> oustTimes;

    /**
     * 照顾单
     */
    @ApiModelProperty(value = "照顾单（0=否,1=是）")
    private List<Integer> isCares;

    /**
     * 添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发,5:排单推荐,6:凑单推荐）
     */
    @ApiModelProperty(value = "添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发,5:排单推荐,6:凑单推荐）")
    private List<Integer> addTypes;

    /**
     * 模特意向(1:待MT确认,2:MT想要,3:MT不想要,4:未确认,5:超时未选择)
     */
    @ApiModelProperty(value = "模特意向(1:待MT确认,2:MT想要,3:MT不想要,4:未确认,5:超时未选择)")
    private List<Integer> modelIntentions;

    /**
     * 淘汰模特ID
     */
    @ApiModelProperty(value = "淘汰模特ID")
    private List<Long> oustModelIds;

    /**
     * 当前运营关联模特
     */
    @Null(message = "请勿传递[backUserRelevanceModelIds]")
    private Collection<Long> backUserRelevanceModelIds;

    @Null(message = "请勿传递[currentUserIsAdmin]")
    private Boolean currentUserIsAdmin;

    /**
     * 淘汰时间
     */
    @Null(message = "请勿传递[oustTimeMap]")
    private Map<String, String> oustTimeMap;

    /**
     * 匹配单IDS
     */
    @Null(message = "请勿传递[matchIds]")
    private Collection<Long> matchIds;

    /**
     * 预选模特ID
     */
    @Null(message = "请勿传递[preselectModelId]")
    private Long preselectModelId;
}
