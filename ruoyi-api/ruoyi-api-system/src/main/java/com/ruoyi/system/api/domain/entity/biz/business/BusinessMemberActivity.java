package com.ruoyi.system.api.domain.entity.biz.business;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

/**
 * 商家会员活动
 *
 * <AUTHOR>
 * @TableName business_member_activity
 */
@Data
@Accessors(chain = true)
public class BusinessMemberActivity implements Serializable {

    private static final long serialVersionUID = 9063909604573514664L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[套餐类型：0-季度套餐,1-年度套餐,2-三年套餐]不能为空")
    @ApiModelProperty("套餐类型：0-季度套餐,1-年度套餐,2-三年套餐")
    private Integer memberPackageType;

    @ApiModelProperty("加赠时间")
    private Integer presentedTime;

    @ApiModelProperty("加赠时间类型（1-天,2-月,3-年）")
    private Integer presentedTimeType;

    @NotNull(message = "[活动开始时间]不能为空")
    @ApiModelProperty("活动开始时间")
    private Integer startTime;

    @NotNull(message = "[活动结束时间]不能为空")
    @ApiModelProperty("活动结束时间")
    private Integer endTime;

    @NotNull(message = "[是否有效（1-有效， 0-无效）]不能为空")
    @ApiModelProperty("是否有效（1-有效， 0-无效）")
    private Integer status;

    @NotNull(message = "[创建人（sys_user.user_id）]不能为空")
    @ApiModelProperty("创建人（sys_user.user_id）")
    private Long createById;

    @NotBlank(message = "[创建人名称（sys_user.user_name）]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("创建人名称（sys_user.user_name）")
    @Length(max = 30, message = "编码长度不能超过30")
    private String createBy;

    @NotNull(message = "[创建人时间]不能为空")
    @ApiModelProperty("创建人时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @NotNull(message = "[修改人（sys_user.user_id）]不能为空")
    @ApiModelProperty("修改人（sys_user.user_id）")
    private Long updateById;

    @NotBlank(message = "[修改人名称（sys_user.user_name）]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("修改人名称（sys_user.user_name）")
    @Length(max = 30, message = "编码长度不能超过30")
    private String updateBy;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
