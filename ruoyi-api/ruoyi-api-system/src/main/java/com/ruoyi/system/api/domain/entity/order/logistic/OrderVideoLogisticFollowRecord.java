package com.ruoyi.system.api.domain.entity.order.logistic;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 跟进记录表
 *
 * <AUTHOR>
 * @TableName order_video_logistic_follow_record
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoLogisticFollowRecord implements Serializable {

    private static final long serialVersionUID = 2363598297403653675L;

    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[物流更新表id order_video_logistic_follow.id]不能为空")
    @ApiModelProperty("物流更新表id order_video_logistic_follow.id")
    private Long followId;

    @Size(max = 300, message = "编码长度不能超过300")
    @ApiModelProperty("图片资源地址id")
    @Length(max = 300, message = "编码长度不能超过300")
    private String resourceId;

    @ApiModelProperty("备注")
    private String remark;

    @NotBlank(message = "[事件名称]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("事件名称")
    @Length(max = 32, message = "编码长度不能超过32")
    private String eventName;

    @NotBlank(message = "[事件内容]不能为空")
    @Size(max = 1200, message = "编码长度不能超过1200")
    @ApiModelProperty("事件内容")
    @Length(max = 1200, message = "编码长度不能超过1,200")
    private String eventContent;

    @NotNull(message = "[事件执行对象（1:商家,2:运营,3:模特,9:系统）]不能为空")
    @ApiModelProperty("事件执行对象（1:商家,2:运营,3:模特,9:系统）")
    private Integer eventExecuteObject;

    @ApiModelProperty("事件执行人用户id")
    private Long eventExecuteUserId;

    @NotBlank(message = "[事件执行人用户名称]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("事件执行人用户名称")
    @Length(max = 32, message = "编码长度不能超过32")
    private String eventExecuteUserName;

    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("事件执行人微信昵称")
    @Length(max = 50, message = "编码长度不能超过50")
    private String eventExecuteNickName;

    @NotNull(message = "[事件执行时间]不能为空")
    @ApiModelProperty("事件执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventExecuteTime;
}
