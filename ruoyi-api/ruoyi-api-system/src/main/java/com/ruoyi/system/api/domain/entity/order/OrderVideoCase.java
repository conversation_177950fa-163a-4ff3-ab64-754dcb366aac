package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单_视频_匹配情况反馈对象 order_video_case
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@ApiModel(value = "订单_视频_匹配情况反馈对象 order_video_case")
@TableName("order_video_case")
@Data
public class OrderVideoCase implements Serializable {

    private static final long serialVersionUID = 4887159005889854658L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 视频id
     */
    @NotNull(message = "[视频id]不能为空")
    @ApiModelProperty(value = "视频id", required = true)
    @Excel(name = "视频id")
    private Long videoId;

    /**
     * 发送人id
     */
    @NotNull(message = "[发送人id]不能为空")
    @ApiModelProperty(value = "发送人id", required = true)
    @Excel(name = "发送人id")
    private Long sendId;

    /**
     * 发送内容
     */
    @NotNull(message = "[发送内容]不能为空")
    @ApiModelProperty(value = "发送内容", required = true)
    @Excel(name = "发送内容")
    private String sendContent;

    /**
     * 发送时间
     */
    @NotNull(message = "[发送时间]不能为空")
    @ApiModelProperty(value = "发送时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 回复人id
     */
    @ApiModelProperty(value = "回复人id")
    @Excel(name = "回复人id")
    private Long replyId;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "回复时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date replyTime;

    /**
     * 回复内容(0:待反馈,1:同意,2:不同意)
     */
    @ApiModelProperty(value = "回复内容(0:待反馈,1:同意,2:不同意)", notes = "0:待反馈,1:同意,2:不同意")
    @Excel(name = "回复内容", readConverterExp = "0:待反馈,1:同意,2:不同意")
    private Integer replyContent;

    /**
     * 运营是否修改了订单（1:修改了,0:还没修改）
     */
    @ApiModelProperty(value = "运营是否修改了订单（1:修改了,0:还没修改）")
    private Integer operateEdit;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 拒绝原因
     */
    @ApiModelProperty(value = "拒绝原因")
    private String reason;
}
