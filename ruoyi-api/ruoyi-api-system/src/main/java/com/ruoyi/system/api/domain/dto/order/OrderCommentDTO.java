package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 订单评论dto
 *
 * <AUTHOR>
 * @date 2024/6/17
 */

@Data
public class OrderCommentDTO implements Serializable {
    private static final long serialVersionUID = -6318903013208439179L;
    @ApiModelProperty("视频订单id")
    @NotNull(message = "[视频订单id]不能为空")
    private Long videoId;
    @ApiModelProperty("评论内容")
    @NotBlank(message = "[评论内容]不能为空")
    @Size(max = 300, message = "[评论内容]长度不能超过300个字符")
    private String commentContent;
}
