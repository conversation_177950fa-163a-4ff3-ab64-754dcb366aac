package com.ruoyi.system.api.domain.dto.biz.business.balance;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-01-08 11:38
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceDetailLockInfoDTO implements Serializable {
    private static final long serialVersionUID = -1548888480485593643L;
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date auditStartTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date auditEndTime;

    @ApiModelProperty(value = "视频编码列表")
    private List<String> videoCodes;

}
