package com.ruoyi.system.api.domain.entity.biz.business;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 余额提现审核表
 *
 * <AUTHOR>
 * @TableName business_balance_audit_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceAuditFlow implements Serializable {

    private static final long serialVersionUID = -6449571961559057550L;

    @TableId(type = IdType.AUTO)
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[商家ID]不能为空")
    @ApiModelProperty("商家ID")
    private Long businessId;

    @NotNull(message = "[提现金额]不能为空")
    @ApiModelProperty("提现金额")
    private BigDecimal amount;

    @ApiModelProperty("实付金额（单位：￥）")
    private BigDecimal realAmount;

    @ApiModelProperty("支付时间")
    private Date payTime;

    @ApiModelProperty("提现申请图片")
    private String payoutResourceUrl;

    @ApiModelProperty("图片资源地址")
    private String resourceUrl;

    @NotNull(message = "[审核状态（0:待处理,1:已提现,2.已取消）]不能为空")
    @ApiModelProperty("审核状态（0:待处理,1:已提现,2.已取消）")
    private Integer auditStatus;

    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @Size(max = 300, message = "编码长度不能超过300")
    @ApiModelProperty("备注")
    @Length(max = 300, message = "编码长度不能超过300")
    private String remark;

    @ApiModelProperty("创建人id FK sys_user.user_id")
    private Long createUserId;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("提现编号")
    private String withdrawNumber;

    @ApiModelProperty("提现申请备注")
    private String applyRemark;

    @ApiModelProperty("提现类型（1-微信，2-支付宝，3-银行卡，4-境外汇款，5-其他）")
    private Integer withdrawWay;

    @ApiModelProperty("提现通知状态 0-未通知 1-已通知")
    private Integer notifyStatus;

    @ApiModelProperty("通知时间")
    private Date notifyTime;

}
