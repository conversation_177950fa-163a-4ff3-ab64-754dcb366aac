package com.ruoyi.system.api.domain.entity.biz.common;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:25
 */
@ApiModel(value = "文本对象")
@TableName("text_table")
@Data
public class Text implements Serializable {

    private static final long serialVersionUID = 683092841480394521L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 文本名称
     */
    @ApiModelProperty(value = "文本名称")
    private String name;

    /**
     * 文本内容
     */
    @ApiModelProperty(value = "文本内容")
    private String content;

    @ApiModelProperty(value = "文本类型：0-协议信息,1-帮助中心-常见问题,2-帮助中心-新手指南")
    private Integer type;

    @ApiModelProperty(value = "排序值")
    private Integer sort;

    @ApiModelProperty(value = "状态：0启用，1-禁用")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否可删除（0:可以,1:不可以）
     */
    @ApiModelProperty(value = "是否可删除（0:可以,1:不可以）")
    private Integer canDelete;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
