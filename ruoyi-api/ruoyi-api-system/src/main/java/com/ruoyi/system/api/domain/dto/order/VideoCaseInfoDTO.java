package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11 17:11
 */
@Data
public class VideoCaseInfoDTO implements Serializable {
    private static final long serialVersionUID = -7601686876951671688L;
    /**
     * 视频订单主键
     */
    @ApiModelProperty(value = "视频订单主键")
    private Long id;
    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国")
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    private Integer modelType;

    /**
     * 拍摄建议（原拍摄要求）、限制条件 入参
     */
    @ApiModelProperty("拍摄建议（原拍摄要求）、限制条件 入参")
    private List<VideoContentDTO> videoContentHistoryDTO;
}
