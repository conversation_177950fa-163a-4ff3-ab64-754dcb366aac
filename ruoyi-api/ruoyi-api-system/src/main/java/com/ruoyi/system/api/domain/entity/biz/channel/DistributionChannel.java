package com.ruoyi.system.api.domain.entity.biz.channel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 分销渠道信息表
 *
 * <AUTHOR>
 * @TableName distribution_channel
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DistributionChannel implements Serializable {

    private static final long serialVersionUID = 2030441508344258076L;

    @TableId(type = IdType.AUTO)
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotBlank(message = "[手机号]不能为空")
    @ApiModelProperty("手机号")
    private String phone;

    @NotNull(message = "[用户ID (FK:biz_user.id)]不能为空")
    @ApiModelProperty("用户ID (FK:biz_user.id)")
    private Long bizUserId;

    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @Size(max = 6, message = "编码长度不能超过6")
    @ApiModelProperty("渠道名称")
    @Length(max = 6, message = "编码长度不能超过6")
    private String channelName;

    @ApiModelProperty("海报名称")
    private String posterName;

    @ApiModelProperty("结算佣金类型（1-固定金额，2-固定比例）")
    private Integer settleDiscountType;

    @NotNull(message = "[佣金比例]不能为空")
    @ApiModelProperty("佣金比例")
    private BigDecimal brokeRage;

    @NotBlank(message = "[种草码]不能为空")
    @Size(max = 4, message = "编码长度不能超过4")
    @ApiModelProperty("种草码")
    @Length(max = 4, message = "编码长度不能超过4")
    private String seedCode;

    @ApiModelProperty("种草官ID")
    private String seedId;

    @ApiModelProperty("密码")
    @Size(max = 4, message = "[密码]不能超过4")
    @NotBlank(message = "[密码]不能为空")
    private String password;

    @NotBlank(message = "[专属链接code（FX + 8位随机字符）]不能为空")
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("专属链接code（FX + 8位随机字符）")
    @Length(max = 10, message = "编码长度不能超过10")
    private String dedicatedLinkCode;

    @NotBlank(message = "[专属企微二维码地址]不能为空")
    @Size(max = 150, message = "编码长度不能超过150")
    @ApiModelProperty("专属企微二维码地址")
    @Length(max = 150, message = "编码长度不能超过150")
    private String weChatUrl;

    @NotBlank(message = "[标签id]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("标签id")
    @Length(max = 32, message = "编码长度不能超过32")
    private String tagId;

    @NotNull(message = "[分销状态（0=正常,1=禁用）]不能为空")
    @ApiModelProperty("分销状态（0=正常,1=禁用）")
    private Integer status;

    @ApiModelProperty("禁用时间/启用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date disableTime;

    @ApiModelProperty("激活状态（1-有效， 0-无效）")
    private Integer activationStatus;

    @ApiModelProperty("激活时间")
    private Date activationTime;

    @NotBlank(message = "[备注]不能为空")
    @Size(max = 64, message = "编码长度不能超过64")
    @ApiModelProperty("备注")
    @Length(max = 64, message = "编码长度不能超过64")
    private String remark;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @NotBlank(message = "[创建人]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("创建人")
    @Length(max = 32, message = "编码长度不能超过32")
    private String createBy;

    @ApiModelProperty("创建人ID")
    private Long createId;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("修改人名称")
    private String updateBy;

    @ApiModelProperty("修改人id")
    private Long updateId;

    public void setEntity(DistributionChannel vo) {
        this.id = vo.id;
        this.phone = vo.phone;
        this.bizUserId = vo.bizUserId;
        this.channelType = vo.channelType;
        this.channelName = vo.channelName;
        this.posterName = vo.posterName;
        this.settleDiscountType = vo.settleDiscountType;
        this.brokeRage = vo.brokeRage;
        this.seedCode = vo.seedCode;
        this.seedId = vo.seedId;
        this.password = vo.password;
        this.dedicatedLinkCode = vo.dedicatedLinkCode;
        this.weChatUrl = vo.weChatUrl;
        this.tagId = vo.tagId;
        this.disableTime = vo.disableTime;
        this.status = vo.status;
        this.activationStatus = vo.activationStatus;
        this.activationTime = vo.activationTime;
        this.remark = vo.remark;
        this.createTime = vo.createTime;
        this.createBy = vo.createBy;
        this.createId = vo.createId;
        this.updateTime = vo.updateTime;
        this.updateBy = vo.updateBy;
        this.updateId = vo.updateId;
    }
}
