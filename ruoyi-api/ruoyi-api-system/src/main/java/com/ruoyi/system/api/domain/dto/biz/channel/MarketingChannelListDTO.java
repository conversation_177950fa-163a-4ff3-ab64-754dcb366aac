package com.ruoyi.system.api.domain.dto.biz.channel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 市场渠道列表条件筛选DTO
 *
 * <AUTHOR>
 * @date 2024/9/25 9:23
 */
@Data
public class MarketingChannelListDTO implements Serializable {

    private static final long serialVersionUID = 1801522404238587978L;
    @ApiModelProperty("市场渠道名称")
    private String marketingChannelName;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)")
    private List<Integer> marketingPlatform;

    @ApiModelProperty("创建人用户ID")
    private List<Long> userIds;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "排序类型：1-独立访客数，2-注册数，3-会员成交数，4-会员总金额")
    private Integer orderByType;

    @ApiModelProperty(value = "是否正序排序")
    private Integer isAsc;
}
