package com.ruoyi.system.api.domain.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :提交模特dto
 * @create :2025-01-14 09:09
 **/
@Data
public class SubmitPreselectModelDTO implements Serializable {

    private static final long serialVersionUID = 7087341041777525851L;
    @ApiModelProperty("匹配单ID")
    @NotNull(message = "[匹配单ID]不能为空")
    private Long matchId;

    @ApiModelProperty("是否清空之前的模特：1-淘汰")
    private Integer isClear;
}
