package com.ruoyi.system.api.domain.entity.biz.pay;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 支付订单对象 pay
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@ApiModel(value = "支付订单对象 pay")
@TableName("pay")
@Data
public class Pay extends BaseEntity
{

    private static final long serialVersionUID = -3718055578285552256L;
    /** 主键 */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /** 支付订单号 */
    @ApiModelProperty(value = "支付订单号")
    @Excel(name = "支付订单号")
    private Long payNum;

    /** 支付状态（0:未支付、1:已支付） */
    @ApiModelProperty(value = "支付状态",notes = "0:未支付、1:已支付")
    @Excel(name = "支付状态", readConverterExp = "0:未支付、1:已支付")
    private String payStatus;

    /** 订单总金额 */
    @ApiModelProperty(value = "订单总金额")
    @Excel(name = "订单总金额")
    private BigDecimal orderTotalAmount;

    /** 余额抵扣 */
    @ApiModelProperty(value = "余额抵扣")
    @Excel(name = "余额抵扣")
    private BigDecimal balanceDeduction;

    /** 优惠券id */
    @ApiModelProperty(value = "优惠券id")
    @Excel(name = "优惠券id")
    private Long discountCouponId;

    /** 优惠券抵扣 */
    @ApiModelProperty(value = "优惠券抵扣")
    @Excel(name = "优惠券抵扣")
    private BigDecimal discountCouponDeduction;

    /** 是否开发票（0:否、1:是） */
    @ApiModelProperty(value = "是否开发票",notes = "0:否、1:是")
    @Excel(name = "是否开发票", readConverterExp = "0:否、1:是")
    private String isInvoice;

    /** 是否已经开发票（0:否、1:是） */
    @ApiModelProperty(value = "是否已经开发票",notes = "0:否、1:是")
    @Excel(name = "是否已经开发票", readConverterExp = "0:否、1:是")
    private String isInvoiceFinish;

    /** 开票服务费(不退款) */
    @ApiModelProperty(value = "开票服务费(不退款)")
    @Excel(name = "开票服务费(不退款)")
    private BigDecimal invoiceServiceCharge;

    /** 开票金额 */
    @ApiModelProperty(value = "开票金额")
    @Excel(name = "开票金额")
    private BigDecimal invoiceAmount;

    /** 开票申请表id */
    @ApiModelProperty(value = "开票申请表id")
    @Excel(name = "开票申请表id")
    private Long invoiceId;

    /** 实际付款金额 */
    @ApiModelProperty(value = "实际付款金额")
    @Excel(name = "实际付款金额")
    private BigDecimal actualPayment;

}
