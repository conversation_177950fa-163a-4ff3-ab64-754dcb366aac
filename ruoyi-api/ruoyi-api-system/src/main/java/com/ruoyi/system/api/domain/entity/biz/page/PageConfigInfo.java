package com.ruoyi.system.api.domain.entity.biz.page;

import com.ruoyi.system.api.domain.vo.biz.page.PageChooseCaseVO;
import com.ruoyi.system.api.domain.vo.biz.page.VideoShopWindowVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-20 09:23
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class PageConfigInfo implements Serializable {
    private static final long serialVersionUID = 3149921876919822462L;

    @ApiModelProperty("主键")
    @NotNull(message="[主键]不能为空")
    private Long id;

    @ApiModelProperty("平台类型：0:Amazon,1:tiktok,2:其他")
    private Integer platform;

    @ApiModelProperty("权限标识")
    private String userPerms;

    @ApiModelProperty("模块内容列表")
    @Valid
    private List<PageChooseCaseVO> pageChooseCaseList;

    @ApiModelProperty("橱窗")
    @Valid
    private VideoShopWindowVO videoShopWindow;
}
